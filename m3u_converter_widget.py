#!/usr/bin/env python3
"""
M3U Converter Widget
Integrated Stalker and Xtream to M3U converter for MEGA IPTV TOOLS
"""

import os
import json
import base64
import asyncio
import aiohttp
import requests
from datetime import datetime
from urllib.parse import quote, urlparse
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QComboBox, QListWidget, 
                            QProgressBar, QTextEdit, QGroupBox, QTabWidget,
                            QCheckBox, QMessageBox, QFileDialog, QFrame)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont
from styles import Colors
from translations.languages import _

class M3UConverterThread(QThread):
    """Thread for M3U conversion operations"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    conversion_finished = pyqtSignal(str, str)  # filename, message
    error_occurred = pyqtSignal(str)
    
    def __init__(self, converter_type, url, mac_or_username, password=None, selected_categories=None, content_types=None):
        super().__init__()
        self.converter_type = converter_type  # 'stalker' or 'xtream'
        self.url = url
        self.mac_or_username = mac_or_username
        self.password = password
        self.selected_categories = selected_categories or []
        self.content_types = content_types or ['channels']  # ['channels', 'vod', 'series']
        self.running = True
        self.current_task = None
    
    def run(self):
        """Run the conversion process"""
        try:
            if self.converter_type == 'stalker':
                self.convert_stalker_to_m3u()
            elif self.converter_type == 'xtream':
                self.convert_xtream_to_m3u()
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def convert_stalker_to_m3u(self):
        """Enhanced Stalker portal to M3U conversion"""
        self.status_updated.emit("🔄 Connecting to Stalker portal...")

        try:
            # Use asyncio for Stalker conversion
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self._enhanced_stalker_conversion_async())
            loop.close()

            if result:
                content_summary = ", ".join(self.content_types)
                self.conversion_finished.emit(result, f"✅ Enhanced Stalker to M3U conversion completed! Content: {content_summary}")
            else:
                self.error_occurred.emit("Failed to convert Stalker portal")

        except Exception as e:
            self.error_occurred.emit(f"Enhanced Stalker conversion error: {str(e)}")
    
    async def _enhanced_stalker_conversion_async(self):
        """Enhanced Stalker conversion logic using combined_app.py methods"""
        try:
            base_url = self.get_base_url(self.url)
            mac = self.mac_or_username.upper()

            # Initialize client using combined_app.py method
            async with aiohttp.ClientSession(cookies={'mac': mac}) as session:
                # Get token using combined_app.py method
                token = await self.get_token(session, base_url)
                if not token:
                    raise Exception("Failed to get authentication token")

                headers = {'Authorization': f'Bearer {token}'}
                self.status_updated.emit("Connected! Loading categories...")
                self.progress_updated.emit(10)

                # Fetch categories using combined_app.py method
                categories = await self.fetch_channels_categories(session, base_url, headers)
                if not categories:
                    raise Exception("Failed to fetch channel categories")

                self.status_updated.emit(f"Found {len(categories)} categories. Loading channels...")
                self.progress_updated.emit(20)

                # Get play token for streams (combined_app.py method)
                play_token = await self.get_play_token(session, base_url, headers)
                if not play_token:
                    self.status_updated.emit("No play token, using fallback method...")

                # Fetch all channels using combined_app.py method
                all_channels = []
                categorized_channels = {}

                # Initialize categorized channels structure
                for category in categories:
                    if category['id'] != '*':
                        categorized_channels[category['id']] = {
                            'title': category['title'],
                            'channels': []
                        }

                # Get all channels using combined_app.py method
                channels_url = f'{base_url}/portal.php?type=itv&action=get_all_channels&JsHttpRequest=1-xml'
                async with session.get(channels_url, headers=headers, ssl=False) as response:
                    if response.status == 200:
                        channels_data = await response.json(content_type=None)
                        channels = channels_data.get('js', {}).get('data', [])

                        # Categorize channels like combined_app.py
                        for channel in channels:
                            genre_id = channel.get('tv_genre_id')
                            if genre_id in categorized_channels:
                                channel['category_name'] = categorized_channels[genre_id]['title']
                                categorized_channels[genre_id]['channels'].append(channel)
                                all_channels.append(channel)

                if not all_channels:
                    raise Exception("No channels found")

                self.progress_updated.emit(50)
                self.status_updated.emit(f"Found {len(all_channels)} channels across {len(categorized_channels)} categories. Generating M3U...")

                # Generate M3U file using combined_app.py format with multiple content types
                timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
                sanitized_url = base_url.replace('://', '_').replace('/', '_').replace('.', '_').replace(':', '_')
                content_types_str = "_".join(self.content_types)
                filename = f'{content_types_str}_{sanitized_url}_{timestamp}.m3u'

                with open(filename, 'w', encoding='utf-8') as file:
                    file.write('#EXTM3U\n')

                    total_processed = 0
                    total_items = len(all_channels)

                    # Process Channels (if requested)
                    if 'channels' in self.content_types:
                        self.status_updated.emit("Processing channels...")
                        # Write channels by category (combined_app.py style)
                        for category in categories:
                            if category['id'] in categorized_channels and categorized_channels[category['id']]['channels']:
                                # Category header
                                file.write(f'\n#EXTINF:-1 group-title="Channels - {category["title"]}",=== {category["title"]} ===\n')
                                file.write('http://localhost/\n')

                                # Category channels
                                for channel in categorized_channels[category['id']]['channels']:
                                    name = channel.get('name', 'Unknown')
                                    cmd = channel.get('cmd', '')
                                    logo = channel.get('logo', '')
                                    channel_id = channel.get('id', '')

                                    if cmd:
                                        # Extract stream ID using combined_app.py method
                                        stream_id = self.extract_stream_id(cmd)
                                        if stream_id:
                                            # Create stream URL with play_token (combined_app.py style)
                                            if play_token:
                                                stream_url = f'{base_url}/play/live.php?mac={mac}&stream={stream_id}&extension=ts&play_token={play_token}'
                                            else:
                                                stream_url = f'{base_url}/play/live.php?mac={mac}&stream={stream_id}&extension=ts'

                                            # Write M3U entry (combined_app.py format)
                                            channel_entry = f'#EXTINF:-1 tvg-id="{channel_id}" tvg-name="{name}" tvg-logo="{logo}" group-title="Channels - {category["title"]}",{name}\n{stream_url}\n'
                                            file.write(channel_entry)

                                    total_processed += 1
                                    progress = 30 + (total_processed / total_items) * 20
                                    self.progress_updated.emit(int(progress))

                                    if total_processed % 10 == 0:
                                        self.status_updated.emit(f'Processed {total_processed} channels...')

                    # Process VOD (if requested)
                    if 'vod' in self.content_types:
                        self.status_updated.emit("Processing VOD...")
                        self.progress_updated.emit(50)

                        # Fetch VOD categories
                        vod_categories = await self.fetch_vod_categories(session, base_url, headers)
                        vod_content = []

                        if vod_categories:
                            # Fetch VOD content using categories
                            vod_content = await self.fetch_vod_content(session, base_url, headers, vod_categories)
                        else:
                            # Fallback: try to get VOD without categories
                            self.status_updated.emit("🎬 Trying fallback VOD method...")
                            vod_content = await self.fetch_vod_fallback(session, base_url, headers)
                            # Create a default category for fallback content
                            vod_categories = [{'id': 'all', 'title': 'All Movies'}]

                        # Write VOD content
                        if vod_content:
                            for category in vod_categories:
                                category_vods = [vod for vod in vod_content if vod.get('category_name') == category['title']]
                                if not category_vods and len(vod_categories) == 1:
                                    # For fallback method, use all content
                                    category_vods = vod_content

                                if category_vods:
                                    # Category header
                                    file.write(f'\n#EXTINF:-1 group-title="VOD - {category["title"]}",=== {category["title"]} ===\n')
                                    file.write('http://localhost/\n')

                                    for vod in category_vods:
                                        name = vod.get('name', 'Unknown Movie')
                                        cmd = vod.get('cmd', '')
                                        logo = vod.get('screenshot_uri', '')
                                        vod_id = vod.get('id', '')

                                        if cmd:
                                            # Get VOD play link
                                            play_link = await self.get_vod_play_link(session, base_url, headers, cmd)
                                            if play_link:
                                                vod_entry = f'#EXTINF:-1 tvg-id="{vod_id}" tvg-name="{name}" tvg-logo="{logo}" group-title="VOD - {category["title"]}",{name}\n{play_link}\n'
                                                file.write(vod_entry)

                                        total_processed += 1
                                        if len(vod_content) > 0:
                                            progress = 50 + (total_processed / (total_items + len(vod_content))) * 30
                                            self.progress_updated.emit(int(progress))

                                        if total_processed % 5 == 0:
                                            self.status_updated.emit(f'🎬 Processed {total_processed} VOD items...')
                        else:
                            self.status_updated.emit("⚠️ No VOD content found")

                    # Process Series (if requested)
                    if 'series' in self.content_types:
                        self.status_updated.emit("📺 Processing series...")
                        self.progress_updated.emit(80)

                        # Fetch series categories
                        series_categories = await self.fetch_series_categories(session, base_url, headers)
                        series_content = []

                        if series_categories:
                            # Fetch series content using combined_app.py method
                            series_content = await self.fetch_series_content(session, base_url, headers, series_categories)
                        else:
                            # Fallback: try to get series without categories
                            self.status_updated.emit("📺 Trying fallback series method...")
                            series_content = await self.fetch_series_fallback(session, base_url, headers)
                            # Create a default category for fallback content
                            series_categories = [{'id': 'all', 'title': 'All Series'}]

                        # Write series content
                        if series_content:
                            for category in series_categories:
                                category_series = [s for s in series_content if s.get('category_name') == category['title']]
                                if not category_series and len(series_categories) == 1:
                                    # For fallback method, use all content
                                    category_series = series_content

                                if category_series:
                                    # Category header
                                    file.write(f'\n#EXTINF:-1 group-title="Series - {category["title"]}",=== {category["title"]} ===\n')
                                    file.write('http://localhost/\n')

                                    for series in category_series:
                                        series_name = series.get('name', 'Unknown Series')
                                        series_id = series.get('id', '').split(':')[0]
                                        logo = series.get('screenshot_uri', '')

                                        # Get seasons and episodes (combined_app.py method)
                                        seasons_data = await self.get_series_seasons(session, base_url, headers, series_id, category['id'])
                                        if seasons_data:
                                            for season in seasons_data:
                                                season_num = season['id'].split(':')[1] if ':' in season['id'] else '1'
                                                episodes = season.get('series', [])

                                                for episode_num in episodes:
                                                    # Create episode entry (combined_app.py format)
                                                    episode_title = f"{series_name} S{season_num}E{episode_num:02d}"

                                                    # Create series command (combined_app.py method)
                                                    cmd_data = {
                                                        'series_id': series_id,
                                                        'season_num': int(season_num),
                                                        'type': 'series'
                                                    }
                                                    cmd = base64.b64encode(json.dumps(cmd_data).encode()).decode()

                                                    # Get actual play link using Stalker API (combined_app.py method)
                                                    play_link = await self.get_series_play_link(session, base_url, headers, cmd, episode_num)

                                                    if play_link:
                                                        series_entry = f'#EXTINF:-1 tvg-type="serie" tvg-serie="{series_id}" tvg-season="{season_num}" tvg-episode="{episode_num}" serie-title="{series_name}" tvg-logo="{logo}" group-title="Series - {category["title"]}",{episode_title}\n{play_link}\n'
                                                        file.write(series_entry)
                                                    else:
                                                        # Fallback: create a basic entry if play link fails
                                                        self.status_updated.emit(f"⚠️ No play link for {series_name} S{season_num}E{episode_num}")
                                                        continue

                                        total_processed += 1
                                        if len(series_content) > 0:
                                            progress = 80 + (total_processed / (total_items + len(series_content))) * 20
                                            self.progress_updated.emit(int(progress))

                                        if total_processed % 3 == 0:
                                            self.status_updated.emit(f'📺 Processed {total_processed} series...')
                        else:
                            self.status_updated.emit("⚠️ No series content found")

                self.progress_updated.emit(100)
                content_summary = f"{total_processed} items from {len(self.content_types)} content types"
                self.status_updated.emit(f"Successfully created enhanced M3U with {content_summary}")
                return filename

        except Exception as e:
            raise Exception(f"Enhanced Stalker conversion failed: {str(e)}")

    async def get_token(self, session, base_url):
        """Get authentication token (combined_app.py method)"""
        try:
            url = f'{base_url}/portal.php?action=handshake&type=stb&token=&JsHttpRequest=1-xml'
            async with session.get(url, ssl=False) as response:
                if response.status == 200:
                    text = await response.text()
                    try:
                        data = json.loads(text)
                        return data['js']['token']
                    except json.JSONDecodeError:
                        return None
                return None
        except Exception:
            return None

    async def fetch_channels_categories(self, session, base_url, headers):
        """Fetch channel categories (combined_app.py method)"""
        try:
            url = f'{base_url}/portal.php?type=itv&action=get_genres&JsHttpRequest=1-xml'
            async with session.get(url, headers=headers, ssl=False) as response:
                if response.status == 200:
                    data = await response.json(content_type=None)
                    genres = data.get('js', [])
                    formatted_categories = []
                    for genre in genres:
                        if genre['id'] != '*':
                            formatted_categories.append({'id': genre['id'], 'title': genre['title']})
                    return formatted_categories
                return None
        except Exception:
            return None

    async def get_play_token(self, session, base_url, headers):
        """Get play token from server (combined_app.py method)"""
        try:
            url = f'{base_url}/portal.php?type=itv&action=create_link&cmd=ffmpeg http://localhost/ch/1_&series=&forced_storage=0&disable_ad=0&download=0&force_ch_link_check=0&JsHttpRequest=1-xml'
            async with session.get(url, headers=headers, ssl=False) as response:
                if response.status == 200:
                    data = await response.json(content_type=None)
                    if 'js' in data:
                        if 'token' in data['js']:
                            return data['js']['token']
                        elif 'cmd' in data['js']:
                            cmd = data['js']['cmd']
                            if 'play_token=' in cmd:
                                return cmd.split('play_token=')[1].split('&')[0]
                return None
        except Exception:
            return None

    async def fetch_vod_categories(self, session, base_url, headers):
        """Fetch VOD categories (combined_app.py method) with enhanced debugging"""
        try:
            url = f'{base_url}/portal.php?type=vod&action=get_categories&JsHttpRequest=1-xml'
            self.status_updated.emit(f"🎬 Fetching VOD categories from: {url}")

            async with session.get(url, headers=headers, ssl=False) as response:
                if response.status == 200:
                    text_response = await response.text()
                    try:
                        data = json.loads(text_response)
                        categories = data.get('js', [])

                        if not categories:
                            self.status_updated.emit(f"⚠️ No VOD categories found in response")
                            return None

                        formatted_categories = []
                        for category in categories:
                            if isinstance(category, dict) and category.get('id') != '*':
                                formatted_categories.append({
                                    'id': category['id'],
                                    'title': category.get('title', f"Category {category['id']}")
                                })

                        self.status_updated.emit(f"🎬 Found {len(formatted_categories)} VOD categories")
                        return formatted_categories

                    except json.JSONDecodeError as e:
                        self.status_updated.emit(f"❌ VOD categories JSON decode error: {str(e)}")
                        return None
                else:
                    self.status_updated.emit(f"❌ VOD categories HTTP error: {response.status}")
                    return None
        except Exception as e:
            self.status_updated.emit(f"❌ VOD categories fetch error: {str(e)}")
            return None

    async def fetch_series_categories(self, session, base_url, headers):
        """Fetch series categories (combined_app.py method)"""
        try:
            url = f'{base_url}/portal.php?type=series&action=get_categories&JsHttpRequest=1-xml'
            async with session.get(url, headers=headers, ssl=False) as response:
                if response.status == 200:
                    data = await response.json(content_type=None)
                    categories = data.get('js', [])
                    formatted_categories = []
                    for category in categories:
                        if category['id'] != '*':
                            formatted_categories.append({'id': category['id'], 'title': category['title']})
                    return formatted_categories
                return None
        except Exception:
            return None

    async def fetch_vod_content(self, session, base_url, headers, categories):
        """Fetch VOD content (combined_app.py method) with enhanced debugging"""
        try:
            all_vod_content = []
            self.status_updated.emit(f"🎬 Fetching VOD from {len(categories)} categories...")

            for i, category in enumerate(categories):
                category_id = category["id"]
                category_title = category["title"]

                # Try different VOD API endpoints
                vod_urls = [
                    f'{base_url}/portal.php?type=vod&action=get_ordered_list&category={category_id}&sortby=added&p=1&JsHttpRequest=1-xml',
                    f'{base_url}/portal.php?type=vod&action=get_ordered_list&genre={category_id}&p=1&JsHttpRequest=1-xml',
                    f'{base_url}/portal.php?type=vod&action=get_all_movies&category={category_id}&JsHttpRequest=1-xml'
                ]

                category_vod_count = 0
                for url_attempt, url in enumerate(vod_urls):
                    try:
                        async with session.get(url, headers=headers, ssl=False) as response:
                            if response.status == 200:
                                text_response = await response.text()
                                try:
                                    data = json.loads(text_response)

                                    # Try different data structures
                                    vod_items = None
                                    if 'js' in data:
                                        if isinstance(data['js'], dict) and 'data' in data['js']:
                                            vod_items = data['js']['data']
                                        elif isinstance(data['js'], list):
                                            vod_items = data['js']
                                    elif isinstance(data, list):
                                        vod_items = data

                                    if vod_items and len(vod_items) > 0:
                                        for vod in vod_items:
                                            if isinstance(vod, dict):
                                                vod['category_name'] = category_title
                                                vod['category_id'] = category_id
                                                all_vod_content.append(vod)
                                                category_vod_count += 1

                                        self.status_updated.emit(f"🎬 Category '{category_title}': {category_vod_count} VOD items")
                                        break  # Success, no need to try other URLs

                                except json.JSONDecodeError as e:
                                    self.status_updated.emit(f"⚠️ JSON decode error for category {category_title}: {str(e)}")
                                    continue
                            else:
                                self.status_updated.emit(f"⚠️ HTTP {response.status} for category {category_title} (attempt {url_attempt + 1})")

                    except Exception as e:
                        self.status_updated.emit(f"⚠️ Error fetching VOD for {category_title}: {str(e)}")
                        continue

                # Progress update
                if (i + 1) % 5 == 0:
                    self.status_updated.emit(f"🎬 Processed {i + 1}/{len(categories)} VOD categories...")

            self.status_updated.emit(f"🎬 Total VOD items found: {len(all_vod_content)}")
            return all_vod_content

        except Exception as e:
            self.status_updated.emit(f"❌ VOD fetch error: {str(e)}")
            return []

    async def fetch_vod_fallback(self, session, base_url, headers):
        """Fallback method to fetch VOD without categories"""
        try:
            all_vod_content = []

            # Try different fallback endpoints
            fallback_urls = [
                f'{base_url}/portal.php?type=vod&action=get_all_movies&JsHttpRequest=1-xml',
                f'{base_url}/portal.php?type=vod&action=get_ordered_list&JsHttpRequest=1-xml',
                f'{base_url}/portal.php?type=vod&action=get_ordered_list&p=1&JsHttpRequest=1-xml'
            ]

            for url in fallback_urls:
                try:
                    self.status_updated.emit(f"🎬 Trying fallback URL: {url}")
                    async with session.get(url, headers=headers, ssl=False) as response:
                        if response.status == 200:
                            text_response = await response.text()
                            try:
                                data = json.loads(text_response)

                                # Try different data structures
                                vod_items = None
                                if 'js' in data:
                                    if isinstance(data['js'], dict) and 'data' in data['js']:
                                        vod_items = data['js']['data']
                                    elif isinstance(data['js'], list):
                                        vod_items = data['js']
                                elif isinstance(data, list):
                                    vod_items = data

                                if vod_items and len(vod_items) > 0:
                                    for vod in vod_items:
                                        if isinstance(vod, dict):
                                            vod['category_name'] = 'All Movies'
                                            all_vod_content.append(vod)

                                    self.status_updated.emit(f"🎬 Fallback method found {len(all_vod_content)} VOD items")
                                    return all_vod_content

                            except json.JSONDecodeError as e:
                                self.status_updated.emit(f"⚠️ Fallback JSON decode error: {str(e)}")
                                continue
                        else:
                            self.status_updated.emit(f"⚠️ Fallback HTTP {response.status}")

                except Exception as e:
                    self.status_updated.emit(f"⚠️ Fallback error: {str(e)}")
                    continue

            self.status_updated.emit("❌ All fallback methods failed")
            return []

        except Exception as e:
            self.status_updated.emit(f"❌ Fallback fetch error: {str(e)}")
            return []

    async def get_vod_play_link(self, session, base_url, headers, cmd):
        """Get VOD play link (combined_app.py method)"""
        try:
            from urllib.parse import quote
            url = f'{base_url}/portal.php?type=vod&action=create_link&cmd={quote(cmd)}&JsHttpRequest=1-xml'
            async with session.get(url, headers=headers, ssl=False) as response:
                if response.status == 200:
                    data = await response.json(content_type=None)
                    if 'js' in data and 'cmd' in data['js']:
                        cmd_result = data['js']['cmd']
                        if ' ' in cmd_result:
                            return cmd_result.split(' ')[1]
                        else:
                            return cmd_result
                return None
        except Exception:
            return None

    async def fetch_series_content(self, session, base_url, headers, categories):
        """Fetch series content (combined_app.py method) with enhanced debugging"""
        try:
            all_series_content = []
            self.status_updated.emit(f"📺 Fetching series from {len(categories)} categories...")

            for i, category in enumerate(categories):
                category_id = category["id"]
                category_title = category["title"]

                # Try different series API endpoints (combined_app.py method)
                page = 1
                category_series_count = 0

                # Paginated fetching like combined_app.py
                while True:
                    series_urls = [
                        f'{base_url}/portal.php?type=series&action=get_ordered_list&category={category_id}&sortby=added&p={page}&JsHttpRequest=1-xml',
                        f'{base_url}/portal.php?type=series&action=get_all_series&category={category_id}&p={page}&JsHttpRequest=1-xml'
                    ]

                    found_series = False
                    for url_attempt, url in enumerate(series_urls):
                        try:
                            async with session.get(url, headers=headers, ssl=False) as response:
                                if response.status == 200:
                                    text_response = await response.text()
                                    try:
                                        data = json.loads(text_response)

                                        # Try different data structures
                                        series_items = None
                                        if 'js' in data:
                                            if isinstance(data['js'], dict) and 'data' in data['js']:
                                                series_items = data['js']['data']
                                            elif isinstance(data['js'], list):
                                                series_items = data['js']
                                        elif isinstance(data, list):
                                            series_items = data

                                        if series_items and len(series_items) > 0:
                                            for series in series_items:
                                                if isinstance(series, dict):
                                                    series['category_name'] = category_title
                                                    series['category_id'] = category_id
                                                    all_series_content.append(series)
                                                    category_series_count += 1

                                            found_series = True
                                            break  # Success, no need to try other URLs

                                    except json.JSONDecodeError as e:
                                        self.status_updated.emit(f"⚠️ Series JSON decode error for category {category_title}: {str(e)}")
                                        continue
                                else:
                                    self.status_updated.emit(f"⚠️ Series HTTP {response.status} for category {category_title} (attempt {url_attempt + 1})")

                        except Exception as e:
                            self.status_updated.emit(f"⚠️ Error fetching series for {category_title}: {str(e)}")
                            continue

                    if not found_series:
                        break  # No more pages or all URLs failed

                    page += 1
                    if page > 10:  # Safety limit
                        break

                if category_series_count > 0:
                    self.status_updated.emit(f"📺 Category '{category_title}': {category_series_count} series")

                # Progress update
                if (i + 1) % 3 == 0:
                    self.status_updated.emit(f"📺 Processed {i + 1}/{len(categories)} series categories...")

            self.status_updated.emit(f"📺 Total series found: {len(all_series_content)}")
            return all_series_content

        except Exception as e:
            self.status_updated.emit(f"❌ Series fetch error: {str(e)}")
            return []

    async def fetch_series_fallback(self, session, base_url, headers):
        """Fallback method to fetch series without categories"""
        try:
            all_series_content = []

            # Try different fallback endpoints
            fallback_urls = [
                f'{base_url}/portal.php?type=series&action=get_all_series&JsHttpRequest=1-xml',
                f'{base_url}/portal.php?type=series&action=get_ordered_list&JsHttpRequest=1-xml',
                f'{base_url}/portal.php?type=series&action=get_ordered_list&p=1&JsHttpRequest=1-xml'
            ]

            for url in fallback_urls:
                try:
                    self.status_updated.emit(f"📺 Trying series fallback URL: {url}")
                    async with session.get(url, headers=headers, ssl=False) as response:
                        if response.status == 200:
                            text_response = await response.text()
                            try:
                                data = json.loads(text_response)

                                # Try different data structures
                                series_items = None
                                if 'js' in data:
                                    if isinstance(data['js'], dict) and 'data' in data['js']:
                                        series_items = data['js']['data']
                                    elif isinstance(data['js'], list):
                                        series_items = data['js']
                                elif isinstance(data, list):
                                    series_items = data

                                if series_items and len(series_items) > 0:
                                    for series in series_items:
                                        if isinstance(series, dict):
                                            series['category_name'] = 'All Series'
                                            all_series_content.append(series)

                                    self.status_updated.emit(f"📺 Fallback method found {len(all_series_content)} series")
                                    return all_series_content

                            except json.JSONDecodeError as e:
                                self.status_updated.emit(f"⚠️ Series fallback JSON decode error: {str(e)}")
                                continue
                        else:
                            self.status_updated.emit(f"⚠️ Series fallback HTTP {response.status}")

                except Exception as e:
                    self.status_updated.emit(f"⚠️ Series fallback error: {str(e)}")
                    continue

            self.status_updated.emit("❌ All series fallback methods failed")
            return []

        except Exception as e:
            self.status_updated.emit(f"❌ Series fallback fetch error: {str(e)}")
            return []

    async def get_series_seasons(self, session, base_url, headers, series_id, category_id):
        """Get seasons and episodes for a series (combined_app.py method) with retry logic"""
        max_retries = 2
        for attempt in range(max_retries):
            try:
                url = f'{base_url}/portal.php?type=series&action=get_ordered_list&movie_id={series_id}&season_id=0&episode_id=0&row=0&category={category_id}&sortby=added&p=1&JsHttpRequest=1-xml'

                # Add timeout to prevent hanging
                timeout = aiohttp.ClientTimeout(total=10)
                async with session.get(url, headers=headers, ssl=False, timeout=timeout) as response:
                    if response.status == 200:
                        text_response = await response.text()
                        try:
                            data = json.loads(text_response)
                            if 'js' in data and 'data' in data['js']:
                                return data['js']['data']
                            return None
                        except json.JSONDecodeError as e:
                            self.status_updated.emit(f"⚠️ Series seasons JSON decode error: {str(e)}")
                            return None
                    else:
                        if attempt < max_retries - 1:
                            self.status_updated.emit(f"⚠️ Series seasons HTTP {response.status}, retrying...")
                            await asyncio.sleep(1)  # Wait before retry
                            continue
                        else:
                            self.status_updated.emit(f"⚠️ Series seasons HTTP {response.status} (final attempt)")
                            return None
            except asyncio.TimeoutError:
                if attempt < max_retries - 1:
                    self.status_updated.emit(f"⚠️ Timeout fetching seasons for series {series_id}, retrying...")
                    await asyncio.sleep(1)
                    continue
                else:
                    self.status_updated.emit(f"⚠️ Timeout fetching seasons for series {series_id} (final attempt)")
                    return None
            except Exception as e:
                if "network name is no longer available" in str(e).lower() or "winerror 64" in str(e).lower():
                    if attempt < max_retries - 1:
                        self.status_updated.emit(f"⚠️ Network error for series {series_id}, retrying...")
                        await asyncio.sleep(2)  # Longer wait for network issues
                        continue
                    else:
                        self.status_updated.emit(f"⚠️ Network error for series {series_id} (skipping)")
                        return None
                else:
                    self.status_updated.emit(f"⚠️ Error fetching seasons for series {series_id}: {str(e)}")
                    return None
        return None

    async def get_series_play_link(self, session, base_url, headers, cmd, episode_num):
        """Get series play link using Stalker API (combined_app.py method) with retry logic"""
        max_retries = 2
        for attempt in range(max_retries):
            try:
                from urllib.parse import quote
                # Use the same API endpoint as combined_app.py for series
                url = f'{base_url}/portal.php?type=vod&action=create_link&cmd={quote(cmd)}&series={episode_num}&JsHttpRequest=1-xml'

                # Add timeout to prevent hanging
                timeout = aiohttp.ClientTimeout(total=8)
                async with session.get(url, headers=headers, ssl=False, timeout=timeout) as response:
                    if response.status == 200:
                        text_response = await response.text()
                        try:
                            data = json.loads(text_response)
                            if 'js' in data and 'cmd' in data['js']:
                                cmd_result = data['js']['cmd']
                                # Extract the actual streaming URL from the command
                                if ' ' in cmd_result:
                                    return cmd_result.split(' ')[1]
                                else:
                                    return cmd_result
                            return None
                        except json.JSONDecodeError as e:
                            self.status_updated.emit(f"⚠️ Series play link JSON decode error: {str(e)}")
                            return None
                    else:
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)  # Short wait before retry
                            continue
                        else:
                            return None
            except asyncio.TimeoutError:
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue
                else:
                    return None
            except Exception as e:
                if "network name is no longer available" in str(e).lower() or "winerror 64" in str(e).lower():
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                    else:
                        return None
                else:
                    return None
        return None

    def convert_xtream_to_m3u(self):
        """Convert Xtream API to M3U with VOD and Series support"""
        self.status_updated.emit("🌐 Connecting to Xtream API...")

        try:
            base_url = self.url.rstrip('/')
            username = self.mac_or_username
            password = self.password

            # Test connection
            auth_url = f"{base_url}/player_api.php?username={username}&password={password}"
            response = requests.get(auth_url, timeout=10)

            if response.status_code != 200:
                raise Exception("Failed to connect to Xtream API")

            user_info = response.json()
            if user_info.get('user_info', {}).get('status') != 'Active':
                raise Exception("Account is not active")

            self.status_updated.emit("✅ Connected! Fetching content...")
            self.progress_updated.emit(10)

            # Generate M3U file
            timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
            content_types_str = "_".join(self.content_types)
            filename = f'xtream_{content_types_str}_{timestamp}.m3u'

            total_processed = 0

            with open(filename, 'w', encoding='utf-8') as file:
                file.write('#EXTM3U\n')

                # Process Live Channels (if requested)
                if 'channels' in self.content_types:
                    self.status_updated.emit("📺 Fetching live channels...")
                    self.progress_updated.emit(20)

                    streams_url = f"{base_url}/player_api.php?username={username}&password={password}&action=get_live_streams"
                    response = requests.get(streams_url, timeout=30)

                    if response.status_code == 200:
                        streams = response.json()
                        if streams:
                            self.status_updated.emit(f"📺 Found {len(streams)} live channels")

                            # Group channels by category
                            categories = {}
                            for stream in streams:
                                category = stream.get('category_name', 'General')
                                if category not in categories:
                                    categories[category] = []
                                categories[category].append(stream)

                            # Write channels by category
                            for category_name, category_streams in categories.items():
                                # Category header
                                file.write(f'\n#EXTINF:-1 group-title="Live - {category_name}",=== {category_name} ===\n')
                                file.write('http://localhost/\n')

                                for stream in category_streams:
                                    if not self.running:
                                        return

                                    name = stream.get('name', 'Unknown')
                                    stream_id = stream.get('stream_id', '')
                                    stream_icon = stream.get('stream_icon', '')

                                    if stream_id:
                                        stream_url = f"{base_url}/live/{username}/{password}/{stream_id}.m3u8"
                                        file.write(f'#EXTINF:-1 tvg-id="{stream_id}" tvg-name="{name}" tvg-logo="{stream_icon}" group-title="Live - {category_name}",{name}\n')
                                        file.write(f'{stream_url}\n')

                                    total_processed += 1
                                    if total_processed % 50 == 0:
                                        self.status_updated.emit(f'📺 Processed {total_processed} channels...')

                # Process VOD Movies (if requested)
                if 'vod' in self.content_types:
                    self.status_updated.emit("🎬 Fetching VOD movies...")
                    self.progress_updated.emit(50)

                    vod_url = f"{base_url}/player_api.php?username={username}&password={password}&action=get_vod_streams"
                    response = requests.get(vod_url, timeout=30)

                    if response.status_code == 200:
                        vod_streams = response.json()
                        if vod_streams:
                            self.status_updated.emit(f"🎬 Found {len(vod_streams)} VOD movies")

                            # Group VOD by category
                            vod_categories = {}
                            for vod in vod_streams:
                                category = vod.get('category_name', 'Movies')
                                if category not in vod_categories:
                                    vod_categories[category] = []
                                vod_categories[category].append(vod)

                            # Write VOD by category
                            for category_name, category_vods in vod_categories.items():
                                # Category header
                                file.write(f'\n#EXTINF:-1 group-title="VOD - {category_name}",=== {category_name} ===\n')
                                file.write('http://localhost/\n')

                                for vod in category_vods:
                                    if not self.running:
                                        return

                                    name = vod.get('name', 'Unknown Movie')
                                    stream_id = vod.get('stream_id', '')
                                    stream_icon = vod.get('stream_icon', '')

                                    if stream_id:
                                        vod_url = f"{base_url}/movie/{username}/{password}/{stream_id}.mp4"
                                        file.write(f'#EXTINF:-1 tvg-id="{stream_id}" tvg-name="{name}" tvg-logo="{stream_icon}" group-title="VOD - {category_name}",{name}\n')
                                        file.write(f'{vod_url}\n')

                                    total_processed += 1
                                    if total_processed % 25 == 0:
                                        self.status_updated.emit(f'🎬 Processed {total_processed} VOD items...')

                # Process TV Series (if requested)
                if 'series' in self.content_types:
                    self.status_updated.emit("📺 Fetching TV series...")
                    self.progress_updated.emit(80)

                    series_url = f"{base_url}/player_api.php?username={username}&password={password}&action=get_series"
                    response = requests.get(series_url, timeout=30)

                    if response.status_code == 200:
                        series_list = response.json()
                        if series_list:
                            self.status_updated.emit(f"📺 Found {len(series_list)} TV series")

                            # Group series by category
                            series_categories = {}
                            for series in series_list:
                                category = series.get('category_name', 'TV Series')
                                if category not in series_categories:
                                    series_categories[category] = []
                                series_categories[category].append(series)

                            # Write series by category
                            for category_name, category_series in series_categories.items():
                                # Category header
                                file.write(f'\n#EXTINF:-1 group-title="Series - {category_name}",=== {category_name} ===\n')
                                file.write('http://localhost/\n')

                                for series in category_series:
                                    if not self.running:
                                        return

                                    series_name = series.get('name', 'Unknown Series')
                                    series_id = series.get('series_id', '')
                                    series_icon = series.get('cover', '')

                                    if series_id:
                                        # Get series info to get episodes
                                        series_info_url = f"{base_url}/player_api.php?username={username}&password={password}&action=get_series_info&series_id={series_id}"
                                        series_response = requests.get(series_info_url, timeout=10)

                                        if series_response.status_code == 200:
                                            series_info = series_response.json()
                                            episodes = series_info.get('episodes', {})

                                            # Process each season
                                            for season_num, season_episodes in episodes.items():
                                                for episode in season_episodes:
                                                    episode_title = episode.get('title', f'Episode {episode.get("episode_num", "Unknown")}')
                                                    episode_id = episode.get('id', '')
                                                    episode_num = episode.get('episode_num', '1')

                                                    if episode_id:
                                                        episode_url = f"{base_url}/series/{username}/{password}/{episode_id}.mp4"
                                                        full_title = f"{series_name} S{season_num}E{episode_num:02d} - {episode_title}"

                                                        file.write(f'#EXTINF:-1 tvg-id="{episode_id}" tvg-name="{full_title}" tvg-logo="{series_icon}" group-title="Series - {category_name}",{full_title}\n')
                                                        file.write(f'{episode_url}\n')

                                                    total_processed += 1
                                                    if total_processed % 10 == 0:
                                                        self.status_updated.emit(f'📺 Processed {total_processed} series episodes...')

            self.progress_updated.emit(100)
            content_summary = f"{total_processed} items from {len(self.content_types)} content types"
            self.conversion_finished.emit(filename, f"✅ Xtream to M3U conversion completed! {content_summary}")

        except Exception as e:
            self.error_occurred.emit(f"Xtream conversion error: {str(e)}")
    
    def get_base_url(self, url):
        """Get base URL from full URL"""
        parsed_url = urlparse(url)
        host = parsed_url.hostname or ''
        port = parsed_url.port or 80
        return f'http://{host}:{port}'
    
    def extract_stream_id(self, cmd):
        """Extract stream ID from command string"""
        if not cmd:
            return None
        if cmd.isdigit():
            return cmd
        
        import re
        # Try different patterns
        patterns = [
            r'/ch/(\d+)_',
            r'stream=(\d+)',
            r'id=(\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, cmd)
            if match:
                return match.group(1)
        
        return cmd
    
    def stop(self):
        """Stop the conversion process"""
        self.running = False
        if self.current_task and not self.current_task.done():
            self.current_task.cancel()
            self.status_updated.emit("🛑 Stopping conversion...")
        self.quit()
        self.wait(3000)  # Wait up to 3 seconds for thread to finish

class M3UConverterWidget(QWidget):
    """Widget for converting Stalker and Xtream to M3U"""
    
    def __init__(self):
        super().__init__()
        self.conversion_thread = None
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        

        
        # Description
        desc = QLabel(_("m3u_converter_desc", "Convert Stalker Portal and Xtream API to M3U playlists"))
        desc.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc.setStyleSheet(f"color: {Colors.TEXT}; padding: 5px;")
        layout.addWidget(desc)
        
        # Converter Type Selection
        type_group = QGroupBox(_("converter_type", "Converter Type"))
        type_layout = QVBoxLayout()
        
        self.converter_type = QComboBox()
        self.converter_type.addItems([
            _("stalker_portal", "📡 Stalker Portal"),
            _("xtream_api", "🌐 Xtream API")
        ])
        self.converter_type.currentTextChanged.connect(self.on_converter_type_changed)
        type_layout.addWidget(self.converter_type)
        
        type_group.setLayout(type_layout)
        layout.addWidget(type_group)
        
        # Input Section
        self.setup_input_section(layout)

        # Content Selection Section (Enhanced like combined_app.py)
        self.setup_content_selection_section(layout)

        # Control Buttons
        self.setup_control_buttons(layout)
        
        # Progress Section
        self.setup_progress_section(layout)
        
        # Status Section
        self.setup_status_section(layout)
        
        self.setLayout(layout)
        self.on_converter_type_changed()  # Initialize UI state
    
    def setup_input_section(self, layout):
        """Setup input fields section"""
        input_group = QGroupBox(_("connection_details", "Connection Details"))
        input_layout = QVBoxLayout()
        
        # URL input
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel(_("server_url", "Server URL:")))
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText(_("enter_server_url", "Enter server URL..."))
        url_layout.addWidget(self.url_input)
        input_layout.addLayout(url_layout)
        
        # MAC/Username input
        mac_layout = QHBoxLayout()
        self.mac_label = QLabel(_("mac_address", "MAC Address:"))
        mac_layout.addWidget(self.mac_label)
        self.mac_input = QLineEdit()
        self.mac_input.setPlaceholderText(_("enter_mac", "Enter MAC address..."))
        mac_layout.addWidget(self.mac_input)
        input_layout.addLayout(mac_layout)
        
        # Password input (for Xtream)
        password_layout = QHBoxLayout()
        self.password_label = QLabel(_("password", "Password:"))
        password_layout.addWidget(self.password_label)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText(_("enter_password", "Enter password..."))
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(self.password_input)
        input_layout.addLayout(password_layout)
        
        input_group.setLayout(input_layout)
        layout.addWidget(input_group)

    def setup_content_selection_section(self, layout):
        """Setup content selection section (Enhanced like combined_app.py)"""
        content_group = QGroupBox(_("content_selection", "Content Selection"))
        content_layout = QVBoxLayout()

        # Description
        desc_label = QLabel(_("select_content_types", "Select content types to include in M3U:"))
        desc_label.setStyleSheet(f"color: {Colors.TEXT}; font-style: italic; padding: 5px;")
        content_layout.addWidget(desc_label)

        # Content type checkboxes
        checkboxes_layout = QHBoxLayout()

        # Channels checkbox
        self.channels_checkbox = QCheckBox(_("live_channels", "📺 Live Channels"))
        self.channels_checkbox.setChecked(True)
        self.channels_checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {Colors.TEXT};
                font-weight: bold;
                padding: 5px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {Colors.SUCCESS};
                border: 2px solid {Colors.SUCCESS};
            }}
        """)
        checkboxes_layout.addWidget(self.channels_checkbox)

        # VOD checkbox
        self.vod_checkbox = QCheckBox(_("vod_movies", "🎬 VOD Movies"))
        self.vod_checkbox.setChecked(False)
        self.vod_checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {Colors.TEXT};
                font-weight: bold;
                padding: 5px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {Colors.SUCCESS};
                border: 2px solid {Colors.SUCCESS};
            }}
        """)
        checkboxes_layout.addWidget(self.vod_checkbox)

        # Series checkbox
        self.series_checkbox = QCheckBox(_("tv_series", "📺 TV Series"))
        self.series_checkbox.setChecked(False)
        self.series_checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {Colors.TEXT};
                font-weight: bold;
                padding: 5px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {Colors.SUCCESS};
                border: 2px solid {Colors.SUCCESS};
            }}
        """)
        checkboxes_layout.addWidget(self.series_checkbox)

        checkboxes_layout.addStretch()
        content_layout.addLayout(checkboxes_layout)

        # Enhanced features note
        features_label = QLabel(_("enhanced_features", "✨ Enhanced features: Category organization, Play tokens, Better URLs"))
        features_label.setStyleSheet(f"color: {Colors.PRIMARY}; font-size: 11px; font-style: italic; padding: 5px;")
        content_layout.addWidget(features_label)

        content_group.setLayout(content_layout)
        layout.addWidget(content_group)

    def setup_control_buttons(self, layout):
        """Setup control buttons"""
        buttons_layout = QHBoxLayout()
        
        self.convert_button = QPushButton(_("start_conversion", "🚀 Start Conversion"))
        self.convert_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SUCCESS};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {Colors.SUCCESS}DD;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
            }}
        """)
        self.convert_button.clicked.connect(self.start_conversion)
        
        self.stop_button = QPushButton(_("stop", "⏹️ Stop"))
        self.stop_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.ERROR};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {Colors.ERROR}DD;
            }}
        """)
        self.stop_button.clicked.connect(self.stop_conversion)
        self.stop_button.setEnabled(False)
        
        self.open_folder_button = QPushButton(_("open_folder", "📁 Open Folder"))
        self.open_folder_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
        """)
        self.open_folder_button.clicked.connect(self.open_output_folder)
        
        buttons_layout.addWidget(self.convert_button)
        buttons_layout.addWidget(self.stop_button)
        buttons_layout.addWidget(self.open_folder_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
    
    def setup_progress_section(self, layout):
        """Setup progress bar section"""
        progress_group = QGroupBox(_("progress", "Progress"))
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {Colors.SURFACE};
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.SUCCESS};
                border-radius: 3px;
            }}
        """)
        progress_layout.addWidget(self.progress_bar)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
    
    def setup_status_section(self, layout):
        """Setup status display section"""
        status_group = QGroupBox(_("status", "Status"))
        status_layout = QVBoxLayout()
        
        self.status_display = QTextEdit()
        self.status_display.setReadOnly(True)
        self.status_display.setMaximumHeight(150)
        self.status_display.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }}
        """)
        status_layout.addWidget(self.status_display)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
    
    def on_converter_type_changed(self):
        """Handle converter type change"""
        current_type = self.converter_type.currentText()
        
        if "Stalker" in current_type:
            # Stalker Portal mode
            self.mac_label.setText(_("mac_address", "MAC Address:"))
            self.mac_input.setPlaceholderText(_("enter_mac", "Enter MAC address (e.g., 00:1A:79:XX:XX:XX)"))
            self.password_label.hide()
            self.password_input.hide()
        else:
            # Xtream API mode
            self.mac_label.setText(_("username", "Username:"))
            self.mac_input.setPlaceholderText(_("enter_username", "Enter username"))
            self.password_label.show()
            self.password_input.show()
    
    def start_conversion(self):
        """Start the conversion process"""
        # Validate inputs
        url = self.url_input.text().strip()
        mac_or_username = self.mac_input.text().strip()
        
        if not url or not mac_or_username:
            QMessageBox.warning(self, _("error", "Error"), 
                              _("fill_required_fields", "Please fill in all required fields"))
            return
        
        current_type = self.converter_type.currentText()
        converter_type = 'stalker' if 'Stalker' in current_type else 'xtream'
        
        if converter_type == 'xtream':
            password = self.password_input.text().strip()
            if not password:
                QMessageBox.warning(self, _("error", "Error"), 
                                  _("password_required", "Password is required for Xtream API"))
                return
        else:
            password = None

        # Get selected content types (Enhanced like combined_app.py)
        content_types = []
        if self.channels_checkbox.isChecked():
            content_types.append('channels')
        if self.vod_checkbox.isChecked():
            content_types.append('vod')
        if self.series_checkbox.isChecked():
            content_types.append('series')

        if not content_types:
            QMessageBox.warning(self, _("error", "Error"),
                              _("select_content_type", "Please select at least one content type"))
            return

        # Start enhanced conversion thread
        self.conversion_thread = M3UConverterThread(
            converter_type, url, mac_or_username, password, None, content_types
        )
        
        # Connect signals
        self.conversion_thread.progress_updated.connect(self.update_progress)
        self.conversion_thread.status_updated.connect(self.update_status)
        self.conversion_thread.conversion_finished.connect(self.on_conversion_finished)
        self.conversion_thread.error_occurred.connect(self.on_conversion_error)
        
        # Update UI
        self.convert_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_display.clear()
        
        # Start thread
        self.conversion_thread.start()
        self.update_status(_("conversion_started", "🚀 Conversion started..."))
    
    def stop_conversion(self):
        """Stop the conversion process"""
        if self.conversion_thread and self.conversion_thread.isRunning():
            self.conversion_thread.stop()
            self.conversion_thread.quit()
            self.conversion_thread.wait()
        
        self.convert_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.update_status(_("conversion_stopped", "⏹️ Conversion stopped by user"))
    
    def update_progress(self, value):
        """Update progress bar"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """Update status display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_display.append(f"[{timestamp}] {message}")
        
        # Auto-scroll to bottom
        cursor = self.status_display.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_display.setTextCursor(cursor)
    
    def on_conversion_finished(self, filename, message):
        """Handle conversion completion"""
        self.convert_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(100)
        self.update_status(message)
        self.update_status(f"📁 File saved: {filename}")
        
        # Show success message
        QMessageBox.information(self, _("success", "Success"), 
                              f"{message}\n\n{_('file_saved', 'File saved')}: {filename}")
    
    def on_conversion_error(self, error_message):
        """Handle conversion error"""
        self.convert_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.update_status(f"Error: {error_message}")
        
        # Show error message
        QMessageBox.critical(self, _("error", "Error"), error_message)
    
    def open_output_folder(self):
        """Open the output folder"""
        import subprocess
        import platform
        
        try:
            current_dir = os.getcwd()
            
            if platform.system() == "Windows":
                subprocess.run(["explorer", current_dir])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", current_dir])
            else:  # Linux
                subprocess.run(["xdg-open", current_dir])
                
        except Exception as e:
            QMessageBox.warning(self, _("error", "Error"), 
                              f"{_('failed_open_folder', 'Failed to open folder')}: {str(e)}")
    
    def update_translations(self):
        """Update all translatable text"""
        # This method will be called when language changes
        # Re-populate combo box and update labels
        pass
