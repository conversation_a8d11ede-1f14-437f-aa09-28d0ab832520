#!/usr/bin/env python3
"""
Language Management System
Handles language detection, loading, and switching for MEGA IPTV TOOLS
"""

import json
import os
from typing import Dict, List, Optional
from pathlib import Path

class LanguageManager:
    """Manages application languages and translations"""

    def __init__(self, translations_dir: str = "translations"):
        self.translations_dir = Path(translations_dir)
        self.current_language = "english"
        self.translations: Dict[str, Dict[str, str]] = {}
        self.available_languages: Dict[str, str] = {}

        # Create translations directory if it doesn't exist
        self.translations_dir.mkdir(exist_ok=True)

        # Load available languages
        self.load_available_languages()

        # Load existing translation files only
        self.load_existing_translations()

    def load_available_languages(self):
        """Load list of available languages"""
        self.available_languages = {
            "english": "🇺🇸 English",
            "spanish": "🇪🇸 Español",
            "french": "🇫🇷 Français",
            "german": "🇩🇪 Deutsch",
            "italian": "🇮🇹 Italiano",
            "portuguese": "🇵🇹 Português",
            "russian": "🇷🇺 Русский",
            "chinese": "🇨🇳 中文",
            "japanese": "🇯🇵 日本語",
            "korean": "🇰🇷 한국어",
            "arabic": "🇸🇦 العربية",
            "turkish": "🇹🇷 Türkçe",
            "dutch": "🇳🇱 Nederlands",
            "polish": "🇵🇱 Polski",
            "czech": "🇨🇿 Čeština",
            "hungarian": "🇭🇺 Magyar",
            "romanian": "🇷🇴 Română",
            "bulgarian": "🇧🇬 Български",
            "greek": "🇬🇷 Ελληνικά",
            "serbian": "🇷🇸 Српски",
            "croatian": "🇭🇷 Hrvatski",
            "slovenian": "🇸🇮 Slovenščina",
            "slovak": "🇸🇰 Slovenčina",
            "ukrainian": "🇺🇦 Українська",
            "hindi": "🇮🇳 हिन्दी",
            "thai": "🇹🇭 ไทย",
            "vietnamese": "🇻🇳 Tiếng Việt",
            "indonesian": "🇮🇩 Bahasa Indonesia",
            "malay": "🇲🇾 Bahasa Melayu",
            "filipino": "🇵🇭 Filipino",
            "hebrew": "🇮🇱 עברית",
            "persian": "🇮🇷 فارسی",
            "urdu": "🇵🇰 اردو",
            "bengali": "🇧🇩 বাংলা",
            "tamil": "🇱🇰 தமிழ்",
            "telugu": "🇮🇳 తెలుగు",
            "marathi": "🇮🇳 मराठी",
            "gujarati": "🇮🇳 ગુજરાતી",
            "punjabi": "🇮🇳 ਪੰਜਾਬੀ",
            "kannada": "🇮🇳 ಕನ್ನಡ",
            "malayalam": "🇮🇳 മലയാളം",
            "oriya": "🇮🇳 ଓଡ଼ିଆ",
            "assamese": "🇮🇳 অসমীয়া",
            "nepali": "🇳🇵 नेपाली",
            "sinhala": "🇱🇰 සිංහල",
            "burmese": "🇲🇲 မြန်မာ",
            "khmer": "🇰🇭 ខ្មែរ",
            "lao": "🇱🇦 ລາວ",
            "mongolian": "🇲🇳 Монгол",
            "tibetan": "🇨🇳 བོད་ཡིག",
            "georgian": "🇬🇪 ქართული",
            "armenian": "🇦🇲 Հայերեն",
            "azerbaijani": "🇦🇿 Azərbaycan",
            "kazakh": "🇰🇿 Қазақ",
            "uzbek": "🇺🇿 O'zbek",
            "kyrgyz": "🇰🇬 Кыргыз",
            "tajik": "🇹🇯 Тоҷикӣ",
            "turkmen": "🇹🇲 Türkmen",
            "albanian": "🇦🇱 Shqip",
            "macedonian": "🇲🇰 Македонски",
            "bosnian": "🇧🇦 Bosanski",
            "montenegrin": "🇲🇪 Crnogorski",
            "latvian": "🇱🇻 Latviešu",
            "lithuanian": "🇱🇹 Lietuvių",
            "estonian": "🇪🇪 Eesti",
            "finnish": "🇫🇮 Suomi",
            "swedish": "🇸🇪 Svenska",
            "norwegian": "🇳🇴 Norsk",
            "danish": "🇩🇰 Dansk",
            "icelandic": "🇮🇸 Íslenska",
            "irish": "🇮🇪 Gaeilge",
            "welsh": "🏴󠁧󠁢󠁷󠁬󠁳󠁿 Cymraeg",
            "scottish": "🏴󠁧󠁢󠁳󠁣󠁴󠁿 Gàidhlig",
            "basque": "🇪🇸 Euskera",
            "catalan": "🇪🇸 Català",
            "galician": "🇪🇸 Galego",
            "maltese": "🇲🇹 Malti",
            "luxembourgish": "🇱🇺 Lëtzebuergesch",
            "afrikaans": "🇿🇦 Afrikaans",
            "swahili": "🇰🇪 Kiswahili",
            "amharic": "🇪🇹 አማርኛ",
            "yoruba": "🇳🇬 Yorùbá",
            "igbo": "🇳🇬 Igbo",
            "hausa": "🇳🇬 Hausa",
            "zulu": "🇿🇦 isiZulu",
            "xhosa": "🇿🇦 isiXhosa",
            "sotho": "🇿🇦 Sesotho",
            "tswana": "🇧🇼 Setswana",
            "shona": "🇿🇼 chiShona",
            "ndebele": "🇿🇼 isiNdebele",
            "malagasy": "🇲🇬 Malagasy"
        }

    def load_existing_translations(self):
        """Load only existing translation files"""
        # Always load English first
        self.load_language("english")

        # Load other existing translation files
        for lang_file in self.translations_dir.glob("*.json"):
            lang_code = lang_file.stem
            if lang_code != "english" and lang_code in self.available_languages:
                self.load_language(lang_code)

    def load_language(self, language_code: str) -> bool:
        """Load a specific language file"""
        try:
            lang_file = self.translations_dir / f"{language_code}.json"

            if lang_file.exists():
                with open(lang_file, 'r', encoding='utf-8') as f:
                    self.translations[language_code] = json.load(f)
                print(f"✅ Loaded language: {language_code}")
                return True
            else:
                # Create default translation file if it doesn't exist
                if language_code == "english":
                    self.create_default_english_translations()
                    return True
                else:
                    # Don't print warnings for missing files - this is normal
                    return False

        except Exception as e:
            print(f"❌ Error loading language {language_code}: {e}")
            return False

    def create_default_english_translations(self):
        """Create default English translation file"""
        default_translations = {
            # Application
            "app_name": "MEGA IPTV TOOLS",
            "app_version": "Version 6.5",
            "app_description": "All-in-One IPTV Tool Suite",

            # Main Menu
            "stalker_tab": "🔗 Stalker Portal",
            "xtream_tab": "🌊 Xtream Codes",
            "player_tab": "🎬 IPTV Player",
            "other_tools_tab": "🛠️ Other Tools",

            # Common Actions
            "connect": "Connect",
            "disconnect": "Disconnect",
            "test": "Test",
            "save": "Save",
            "load": "Load",
            "export": "Export",
            "import": "Import",
            "clear": "Clear",
            "refresh": "Refresh",
            "update": "Update",
            "download": "Download",
            "upload": "Upload",
            "cancel": "Cancel",
            "ok": "OK",
            "yes": "Yes",
            "no": "No",
            "apply": "Apply",
            "reset": "Reset",
            "delete": "Delete",
            "edit": "Edit",
            "add": "Add",
            "remove": "Remove",
            "browse": "Browse",
            "search": "Search",
            "filter": "Filter",
            "sort": "Sort",
            "copy": "Copy",
            "paste": "Paste",
            "cut": "Cut",
            "undo": "Undo",
            "redo": "Redo",
            "select_all": "Select All",
            "close": "Close",
            "minimize": "Minimize",
            "maximize": "Maximize",
            "restore": "Restore",
            "help": "Help",
            "about": "About",
            "settings": "Settings",
            "preferences": "Preferences",
            "options": "Options",
            "tools": "Tools",
            "view": "View",
            "file": "File",
            "edit_menu": "Edit",
            "window": "Window",

            # Status Messages
            "ready": "Ready",
            "loading": "Loading...",
            "connecting": "Connecting...",
            "connected": "Connected",
            "disconnected": "Disconnected",
            "error": "Error",
            "warning": "Warning",
            "info": "Information",
            "success": "Success",
            "failed": "Failed",
            "completed": "Completed",
            "cancelled": "Cancelled",
            "timeout": "Timeout",
            "not_found": "Not Found",
            "invalid": "Invalid",
            "valid": "Valid",
            "enabled": "Enabled",
            "disabled": "Disabled",
            "online": "Online",
            "offline": "Offline",
            "available": "Available",
            "unavailable": "Unavailable",
            "working": "Working",
            "not_working": "Not Working",
            "testing": "Testing...",
            "processing": "Processing...",
            "please_wait": "Please wait...",

            # Stalker Portal
            "stalker_portal": "Stalker Portal",
            "portal_url": "Portal URL",
            "mac_address": "MAC Address",
            "generate_mac": "Generate MAC",
            "load_mac_file": "Load MAC File",
            "mac_source": "MAC Source",
            "manual_entry": "Manual Entry",
            "file_selection": "File Selection",
            "categories": "Categories",
            "channels": "Channels",
            "movies": "Movies",
            "series": "Series",
            "live_tv": "Live TV",
            "vod": "Video on Demand",
            "epg": "Electronic Program Guide",

            # Xtream Codes
            "xtream_codes": "Xtream Codes",
            "server_url": "Server URL",
            "username": "Username",
            "password": "Password",
            "port": "Port",
            "get_info": "Get Info",
            "get_categories": "Get Categories",
            "get_channels": "Get Channels",
            "get_movies": "Get Movies",
            "get_series": "Get Series",

            # IPTV Player
            "iptv_player": "IPTV Player",
            "play": "Play",
            "pause": "Pause",
            "stop": "Stop",
            "volume": "Volume",
            "mute": "Mute",
            "fullscreen": "Fullscreen",
            "playlist": "Playlist",
            "current_channel": "Current Channel",
            "next_channel": "Next Channel",
            "previous_channel": "Previous Channel",
            "channel_list": "Channel List",
            "now_playing": "Now Playing",

            # Other Tools
            "other_tools": "Other Tools",
            "basic_tools": "🛠️ Basic",
            "mac_generator": "MAC Address Generator",
            "portal_converter": "Portal Converter",
            "proxy_grabber": "Proxy Grabber",
            "epg_manager": "📺 EPG",
            "m3u_converter": "🔄 M3U",
            "server_analyzer": "Server Analyzer",
            "playlist_manager": "Playlist Manager",

            # EPG Manager
            "epg_configuration": "EPG Configuration",
            "epg_url": "EPG URL",
            "confidence_threshold": "Confidence Threshold",
            "prefer_internal": "Prefer Internal EPG",
            "auto_update": "Auto Update",
            "channel_matching": "Channel Matching",
            "test_matching": "Test Matching",
            "program_guide": "Program Guide",
            "epg_statistics": "EPG Statistics",
            "total_channels": "Total Channels",
            "total_programs": "Total Programs",
            "current_programs": "Current Programs",
            "future_programs": "Future Programs",
            "last_update": "Last Update",
            "update_epg": "Update EPG",
            "validate_url": "Validate URL",
            "clear_cache": "Clear Cache",

            # Language Settings
            "language": "Language",
            "language_settings": "🌍 Language",
            "select_language": "Select Language",
            "change_language": "Change Language",
            "language_changed": "Language changed successfully",
            "restart_required": "Restart required for full language change",

            # File Operations
            "open_file": "Open File",
            "save_file": "Save File",
            "save_as": "Save As",
            "file_saved": "File saved successfully",
            "file_loaded": "File loaded successfully",
            "file_not_found": "File not found",
            "invalid_file": "Invalid file format",
            "file_error": "File operation error",

            # Network
            "network_error": "Network error",
            "connection_failed": "Connection failed",
            "timeout_error": "Connection timeout",
            "server_error": "Server error",
            "invalid_url": "Invalid URL",
            "invalid_credentials": "Invalid credentials",
            "authentication_failed": "Authentication failed",
            "access_denied": "Access denied",

            # Validation
            "required_field": "This field is required",
            "invalid_format": "Invalid format",
            "invalid_mac": "Invalid MAC address format",
            "invalid_ip": "Invalid IP address",
            "invalid_port": "Invalid port number",
            "field_too_short": "Field is too short",
            "field_too_long": "Field is too long",

            # Progress
            "progress": "Progress",
            "step": "Step",
            "of": "of",
            "percent_complete": "% Complete",
            "estimated_time": "Estimated Time",
            "time_remaining": "Time Remaining",
            "elapsed_time": "Elapsed Time",

            # Confirmation
            "confirm_action": "Confirm Action",
            "are_you_sure": "Are you sure?",
            "confirm_delete": "Are you sure you want to delete this item?",
            "confirm_clear": "Are you sure you want to clear all data?",
            "confirm_reset": "Are you sure you want to reset settings?",
            "confirm_exit": "Are you sure you want to exit?",
            "unsaved_changes": "You have unsaved changes. Do you want to save them?",

            # Tips and Help
            "tip": "Tip",
            "hint": "Hint",
            "example": "Example",
            "note": "Note",
            "warning_note": "Warning",
            "important": "Important",
            "recommended": "Recommended",
            "optional": "Optional",
            "advanced": "Advanced",
            "basic": "Basic",

            # Time and Date
            "today": "Today",
            "yesterday": "Yesterday",
            "tomorrow": "Tomorrow",
            "this_week": "This Week",
            "last_week": "Last Week",
            "next_week": "Next Week",
            "this_month": "This Month",
            "last_month": "Last Month",
            "next_month": "Next Month",
            "never": "Never",
            "always": "Always",
            "now": "Now",
            "later": "Later",
            "soon": "Soon",
            "recently": "Recently",

            # Quality and Performance
            "quality": "Quality",
            "performance": "Performance",
            "speed": "Speed",
            "bandwidth": "Bandwidth",
            "latency": "Latency",
            "buffer": "Buffer",
            "resolution": "Resolution",
            "bitrate": "Bitrate",
            "fps": "FPS",
            "codec": "Codec",
            "format": "Format",

            # Statistics
            "statistics": "Statistics",
            "total": "Total",
            "count": "Count",
            "average": "Average",
            "minimum": "Minimum",
            "maximum": "Maximum",
            "median": "Median",
            "percentage": "Percentage",
            "ratio": "Ratio",
            "rate": "Rate",
            "frequency": "Frequency",

            # About Dialog
            "about_app": "About MEGA IPTV TOOLS",
            "version": "Version",
            "build": "Build",
            "author": "Author",
            "website": "Website",
            "support": "Support",
            "license": "License",
            "copyright": "Copyright",
            "all_rights_reserved": "All rights reserved",

            # Error Messages
            "error_occurred": "An error occurred",
            "unexpected_error": "Unexpected error",
            "operation_failed": "Operation failed",
            "invalid_input": "Invalid input",
            "missing_data": "Missing data",
            "corrupted_data": "Corrupted data",
            "insufficient_permissions": "Insufficient permissions",
            "disk_full": "Disk full",
            "memory_error": "Memory error",
            "system_error": "System error"
        }

        # Save default English translations
        self.translations["english"] = default_translations
        self.save_language("english")

    def save_language(self, language_code: str) -> bool:
        """Save translations for a specific language"""
        try:
            if language_code not in self.translations:
                return False

            lang_file = self.translations_dir / f"{language_code}.json"

            with open(lang_file, 'w', encoding='utf-8') as f:
                json.dump(self.translations[language_code], f,
                         indent=2, ensure_ascii=False, sort_keys=True)

            print(f"💾 Saved language: {language_code}")
            return True

        except Exception as e:
            print(f"❌ Error saving language {language_code}: {e}")
            return False

    def set_language(self, language_code: str) -> bool:
        """Set the current language"""
        if language_code in self.translations:
            self.current_language = language_code
            print(f"🌍 Language set to: {language_code}")
            return True
        else:
            print(f"⚠️ Language not available: {language_code}")
            return False

    def get_text(self, key: str, default: str = None) -> str:
        """Get translated text for a key"""
        if self.current_language in self.translations:
            return self.translations[self.current_language].get(key, default or key)
        else:
            return default or key

    def get_available_languages(self) -> Dict[str, str]:
        """Get list of available languages"""
        return self.available_languages.copy()

    def get_loaded_languages(self) -> List[str]:
        """Get list of loaded languages"""
        return list(self.translations.keys())

    def is_language_loaded(self, language_code: str) -> bool:
        """Check if a language is loaded"""
        return language_code in self.translations

    def get_current_language(self) -> str:
        """Get current language code"""
        return self.current_language

    def get_language_name(self, language_code: str) -> str:
        """Get display name for a language"""
        return self.available_languages.get(language_code, language_code)

    def load_all_available_languages(self):
        """Load all available language files (on demand)"""
        for lang_code in self.available_languages.keys():
            if lang_code not in self.translations:
                self.load_language(lang_code)

# Global language manager instance
language_manager = LanguageManager()

def _(key: str, default: str = None) -> str:
    """Shorthand function for getting translated text"""
    return language_manager.get_text(key, default)

def set_language(language_code: str) -> bool:
    """Shorthand function for setting language"""
    return language_manager.set_language(language_code)

def get_available_languages() -> Dict[str, str]:
    """Shorthand function for getting available languages"""
    return language_manager.available_languages.copy()
