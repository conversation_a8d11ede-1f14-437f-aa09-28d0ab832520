#!/usr/bin/env python3
"""
M3U File-based Download Widget
Allows loading M3U files and downloading individual movies/series with bypass features
"""

import os
import sys
import json
import uuid
from typing import List, Dict, Optional
from urllib.parse import urlparse, unquote
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTreeWidget, QTreeWidgetItem, QGroupBox, QFileDialog, QMessageBox,
    QCheckBox, QSpinBox, QTextEdit, QSplitter, QFrame, QProgressBar,
    QHeaderView, QMenu, QApplication
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QAction

import logging
logger = logging.getLogger(__name__)

class M3UEntry:
    """Represents an M3U entry (movie/series)"""
    def __init__(self, title: str, url: str, filename: str):
        self.title = title
        self.url = url
        self.filename = filename
        self.group = ""  # For grouping (Movies, Series, etc.)
        self.duration = ""
        self.logo = ""

class M3UParser:
    """Parser for M3U files"""
    
    @staticmethod
    def parse(file_path: str) -> List[M3UEntry]:
        """Parse M3U file and return list of entries"""
        entries = []
        current_title = None
        current_group = ""
        current_duration = ""
        current_logo = ""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    
                    if line.startswith('#EXTINF:'):
                        # Parse EXTINF line: #EXTINF:duration,title
                        parts = line.split(',', 1)
                        if len(parts) > 1:
                            current_title = parts[1]
                            # Extract duration
                            duration_part = parts[0].replace('#EXTINF:', '')
                            current_duration = duration_part.split()[0] if duration_part else ""
                        
                        # Extract group-title if present
                        if 'group-title=' in line:
                            group_start = line.find('group-title="') + 13
                            group_end = line.find('"', group_start)
                            if group_end > group_start:
                                current_group = line[group_start:group_end]
                        
                        # Extract logo if present
                        if 'tvg-logo=' in line:
                            logo_start = line.find('tvg-logo="') + 10
                            logo_end = line.find('"', logo_start)
                            if logo_end > logo_start:
                                current_logo = line[logo_start:logo_end]
                    
                    elif line and not line.startswith('#'):
                        # This is a URL line
                        if current_title:
                            title = current_title
                        else:
                            title = f'Video_{len(entries) + 1}'
                        
                        filename = M3UParser.sanitize_filename(title) + M3UParser.get_extension_from_url(line)
                        
                        entry = M3UEntry(title, line, filename)
                        entry.group = current_group
                        entry.duration = current_duration
                        entry.logo = current_logo
                        
                        entries.append(entry)
                        
                        # Reset for next entry
                        current_title = None
                        current_group = ""
                        current_duration = ""
                        current_logo = ""
                        
        except Exception as e:
            raise Exception(f'Failed to parse M3U file: {str(e)}')
        
        return entries
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for filesystem compatibility"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()
    
    @staticmethod
    def get_extension_from_url(url: str) -> str:
        """Extract file extension from URL or default to .mp4"""
        parsed = urlparse(unquote(url))
        path = parsed.path
        
        video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.m4v', '.ts', '.flv', '.webm']
        ext = os.path.splitext(path)[1].lower()
        
        if ext in video_extensions:
            return ext
        
        # Check URL for extension hints
        url_lower = url.lower()
        for ext in video_extensions:
            if ext in url_lower:
                return ext
        
        return '.mp4'  # Default

class M3UDownloadWidget(QWidget):
    """Widget for M3U file-based downloads"""
    
    def __init__(self, download_manager=None):
        super().__init__()
        self.download_manager = download_manager
        self.entries: List[M3UEntry] = []
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # File selection group
        file_group = QGroupBox("📁 M3U File Selection")
        file_layout = QVBoxLayout(file_group)
        
        # M3U file input
        m3u_frame = QFrame()
        m3u_layout = QHBoxLayout(m3u_frame)
        m3u_layout.setContentsMargins(0, 0, 0, 0)
        
        m3u_label = QLabel("M3U File:")
        self.m3u_path = QLineEdit()
        self.m3u_path.setPlaceholderText("Select an M3U file containing movie/series URLs...")
        m3u_browse = QPushButton("📂 Browse")
        m3u_browse.clicked.connect(self.browse_m3u_file)
        
        m3u_layout.addWidget(m3u_label)
        m3u_layout.addWidget(self.m3u_path)
        m3u_layout.addWidget(m3u_browse)
        
        # Load button
        load_btn = QPushButton("📋 Load M3U File")
        load_btn.clicked.connect(self.load_m3u_file)
        
        file_layout.addWidget(m3u_frame)
        file_layout.addWidget(load_btn)
        
        # Content list
        content_group = QGroupBox("🎬 Movies & Series")
        content_layout = QVBoxLayout(content_group)
        
        # Tree widget for displaying M3U entries
        self.content_tree = QTreeWidget()
        self.content_tree.setHeaderLabels(['Title', 'Group', 'URL', 'Duration'])
        self.content_tree.setColumnWidth(0, 300)
        self.content_tree.setColumnWidth(1, 100)
        self.content_tree.setColumnWidth(2, 400)
        self.content_tree.setColumnWidth(3, 80)
        
        # Enable multiple selection
        self.content_tree.setSelectionMode(QTreeWidget.SelectionMode.ExtendedSelection)
        
        # Context menu
        self.content_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.content_tree.customContextMenuRequested.connect(self.show_context_menu)
        
        content_layout.addWidget(self.content_tree)
        
        # Control buttons
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        self.download_selected_btn = QPushButton("📥 Download Selected")
        self.download_selected_btn.clicked.connect(self.download_selected)
        self.download_selected_btn.setEnabled(False)
        
        self.download_all_btn = QPushButton("📦 Download All")
        self.download_all_btn.clicked.connect(self.download_all)
        self.download_all_btn.setEnabled(False)
        
        self.clear_btn = QPushButton("🗑️ Clear List")
        self.clear_btn.clicked.connect(self.clear_list)
        
        button_layout.addWidget(self.download_selected_btn)
        button_layout.addWidget(self.download_all_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        
        content_layout.addWidget(button_frame)
        
        # Status
        self.status_label = QLabel("Ready - Load an M3U file to begin")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        
        # Add to main layout
        layout.addWidget(file_group)
        layout.addWidget(content_group)
        layout.addWidget(self.status_label)
    
    def browse_m3u_file(self):
        """Browse for M3U file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select M3U File",
            "",
            "M3U Files (*.m3u *.m3u8);;All Files (*.*)"
        )
        
        if file_path:
            self.m3u_path.setText(file_path)
    
    def load_m3u_file(self):
        """Load and parse M3U file"""
        file_path = self.m3u_path.text().strip()
        
        if not file_path:
            QMessageBox.warning(self, "Warning", "Please select an M3U file first.")
            return
        
        if not os.path.exists(file_path):
            QMessageBox.critical(self, "Error", "Selected M3U file does not exist.")
            return
        
        try:
            self.status_label.setText("🔄 Loading M3U file...")
            QApplication.processEvents()
            
            # Parse M3U file
            self.entries = M3UParser.parse(file_path)
            
            # Clear and populate tree
            self.content_tree.clear()
            
            # Group entries by group-title
            groups = {}
            for entry in self.entries:
                group_name = entry.group if entry.group else "Ungrouped"
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(entry)
            
            # Add entries to tree
            for group_name, group_entries in groups.items():
                # Create group item
                group_item = QTreeWidgetItem([f"📁 {group_name} ({len(group_entries)} items)", "", "", ""])
                group_item.setFont(0, QFont("", -1, QFont.Weight.Bold))
                group_item.setBackground(0, QColor(240, 240, 240))
                self.content_tree.addTopLevelItem(group_item)
                
                # Add entries under group
                for entry in group_entries:
                    item = QTreeWidgetItem([
                        entry.title,
                        entry.group,
                        entry.url[:100] + "..." if len(entry.url) > 100 else entry.url,
                        entry.duration
                    ])
                    item.setData(0, Qt.ItemDataRole.UserRole, entry)  # Store entry object
                    group_item.addChild(item)
                
                # Expand group
                group_item.setExpanded(True)
            
            # Update UI
            self.download_selected_btn.setEnabled(True)
            self.download_all_btn.setEnabled(True)
            
            self.status_label.setText(f"✅ Loaded {len(self.entries)} items from M3U file")
            
        except Exception as e:
            logger.error(f"Error loading M3U file: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load M3U file:\n{str(e)}")
            self.status_label.setText("❌ Failed to load M3U file")

    def show_context_menu(self, position):
        """Show context menu for tree items"""
        item = self.content_tree.itemAt(position)
        if not item:
            return

        # Check if it's an entry item (not a group)
        entry = item.data(0, Qt.ItemDataRole.UserRole)
        if not entry:
            return

        menu = QMenu(self)

        # Copy URL action
        copy_url_action = menu.addAction("📋 Copy URL")
        copy_url_action.triggered.connect(lambda: self.copy_url(entry))

        # Download single item action
        download_action = menu.addAction("📥 Download This Item")
        download_action.triggered.connect(lambda: self.download_single_item(entry))

        menu.exec(self.content_tree.mapToGlobal(position))

    def copy_url(self, entry):
        """Copy URL to clipboard"""
        clipboard = QApplication.clipboard()
        clipboard.setText(entry.url)
        self.status_label.setText(f"✅ URL copied to clipboard: {entry.title}")

    def download_single_item(self, entry):
        """Download a single item"""
        if not self.download_manager:
            QMessageBox.warning(self, "Warning", "Download manager not available.")
            return

        self.add_to_downloads([entry])

    def download_selected(self):
        """Download selected items"""
        selected_items = self.content_tree.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "Info", "Please select items to download.")
            return

        # Extract entries from selected items
        entries = []
        for item in selected_items:
            entry = item.data(0, Qt.ItemDataRole.UserRole)
            if entry:  # Skip group items
                entries.append(entry)

        if not entries:
            QMessageBox.information(self, "Info", "No valid items selected.")
            return

        self.add_to_downloads(entries)

    def download_all(self):
        """Download all items"""
        if not self.entries:
            QMessageBox.information(self, "Info", "No items to download.")
            return

        confirm = QMessageBox.question(
            self,
            "Confirm Download",
            f"Download all {len(self.entries)} items?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            self.add_to_downloads(self.entries)

    def add_to_downloads(self, entries: List[M3UEntry]):
        """Add entries to download manager"""
        if not self.download_manager:
            QMessageBox.warning(self, "Warning", "Download manager not available.")
            return

        try:
            from download_manager import DownloadItem

            added_count = 0
            for entry in entries:
                # Create download item
                download_item = DownloadItem(
                    id=str(uuid.uuid4()),
                    title=entry.title,
                    url=entry.url,
                    server_type='m3u',  # New server type for M3U downloads
                    content_type='movie',  # Assume movie for now
                    file_extension=M3UParser.get_extension_from_url(entry.url).replace('.', ''),
                    file_size=0,
                    duration=entry.duration,
                    quality="Original",
                    season="",
                    episode="",
                    year="",
                    genre=entry.group,
                    description=f"Downloaded from M3U file",
                    poster_url=entry.logo,
                    subtitle_url=""
                )

                # Add to download manager
                if hasattr(self.download_manager, 'add_download'):
                    self.download_manager.add_download(download_item)
                    added_count += 1
                else:
                    logger.warning("Download manager doesn't have add_download method")

            if added_count > 0:
                self.status_label.setText(f"✅ Added {added_count} items to downloads")
                QMessageBox.information(
                    self,
                    "Downloads Added",
                    f"Successfully added {added_count} items to the download queue.\n\n"
                    f"Go to the Downloads tab to start downloading with bypass features:\n"
                    f"• Proxy support\n"
                    f"• User-agent rotation\n"
                    f"• Smart retry logic\n"
                    f"• Resume capability"
                )
            else:
                self.status_label.setText("❌ Failed to add items to downloads")

        except Exception as e:
            logger.error(f"Error adding downloads: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add downloads:\n{str(e)}")

    def clear_list(self):
        """Clear the content list"""
        if self.entries:
            confirm = QMessageBox.question(
                self,
                "Confirm Clear",
                "Clear all loaded items?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if confirm == QMessageBox.StandardButton.Yes:
                self.content_tree.clear()
                self.entries.clear()
                self.download_selected_btn.setEnabled(False)
                self.download_all_btn.setEnabled(False)
                self.status_label.setText("Ready - Load an M3U file to begin")

    def get_selected_count(self):
        """Get count of selected items"""
        selected_items = self.content_tree.selectedItems()
        count = 0
        for item in selected_items:
            entry = item.data(0, Qt.ItemDataRole.UserRole)
            if entry:
                count += 1
        return count
