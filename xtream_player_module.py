#!/usr/bin/env python3
"""
Xtream Player Module - Full Implementation
Xtream API player for browsing and playing Xtream content with M3U support
Based on Xtream Player.py with VLC integration
"""

import sys
import logging
import requests
import re
import os
import time
from urllib.parse import quote
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QListWidget, QListWidgetItem, QTabWidget, QProgressBar, QMessageBox,
    QSplitter, QTextEdit, QComboBox, QFrame, QStackedWidget, QCheckBox,
    QMenu, QSlider
)
from PyQt6.QtGui import QFont
from styles import Colors

# Download integration imports
try:
    from download_integration_gui import DownloadButtonWidget
    from download_manager_gui import DownloadManagerWidget
    DOWNLOAD_AVAILABLE = True
except ImportError:
    DOWNLOAD_AVAILABLE = False
    class DownloadButtonWidget:
        """Dummy class when download manager is not available"""
        pass

# VLC imports
try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False
    print("VLC not available - external player will be used")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Custom User Agent (same as Xtream Player.py)
CUSTOM_USER_AGENT = "Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3"

class XtreamPlayerThread(QThread):
    """Thread for handling Xtream API operations (based on Xtream Player.py)"""
    categories_loaded = pyqtSignal(dict)
    channels_loaded = pyqtSignal(list)
    series_info_loaded = pyqtSignal(dict)
    progress_updated = pyqtSignal(int)
    error_occurred = pyqtSignal(str)
    status_updated = pyqtSignal(str)
    playlist_loaded = pyqtSignal(dict)

    def __init__(self, server_url=None, username=None, password=None, operation="categories",
                 category_type=None, m3u_url=None, category_id=None, series_id=None):
        super().__init__()
        self.server_url = server_url
        self.username = username
        self.password = password
        self.operation = operation
        self.category_type = category_type
        self.m3u_url = m3u_url
        self.category_id = category_id
        self.series_id = series_id

    def run(self):
        try:
            if self.operation == "load_m3u":
                self.load_m3u_playlist()
            elif self.operation == "categories":
                self.load_categories()
            elif self.operation == "channels":
                self.load_channels()
            elif self.operation == "series_info":
                self.load_series_info()
            else:
                self.error_occurred.emit(f"Unknown operation: {self.operation}")

        except Exception as e:
            logger.error(f"XtreamPlayerThread error: {e}")
            self.error_occurred.emit(str(e))

    def load_m3u_playlist(self):
        """Load M3U playlist (same logic as Xtream Player.py)"""
        try:
            self.status_updated.emit("Loading M3U playlist...")
            self.progress_updated.emit(10)

            # Extract credentials from M3U URL if needed
            if self.m3u_url and "get.php" in self.m3u_url:
                pattern = r'(http[s]?://[^/]+)/get\.php\?username=([^&]*)&password=([^&]*)&type=(m3u_plus|m3u|&output=m3u8)'
                match = re.match(pattern, self.m3u_url)
                if match:
                    self.server_url = match.group(1)
                    self.username = match.group(2)
                    self.password = match.group(3)
                    logger.info(f"Extracted credentials from M3U URL: {self.server_url}")

            self.status_updated.emit("Authenticating...")
            self.progress_updated.emit(30)

            # Test authentication
            auth_url = f"{self.server_url}/player_api.php"
            params = {
                'username': self.username,
                'password': self.password,
                'action': 'user_info'
            }

            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(auth_url, params=params, headers=headers, timeout=15)
            response.raise_for_status()
            user_info = response.json()

            if user_info.get('user_info', {}).get('auth') != 1:
                raise Exception("Authentication failed - Invalid credentials")

            self.status_updated.emit("Authentication successful!")
            self.progress_updated.emit(50)

            # Load categories using enhanced method
            self.status_updated.emit("Loading categories...")
            self.progress_updated.emit(70)

            # Use same enhanced logic as load_categories method
            params = {
                'username': self.username,
                'password': self.password,
                'action': 'get_live_categories'
            }

            categories_url = f"{self.server_url}/player_api.php"
            headers = {'User-Agent': CUSTOM_USER_AGENT}

            # Fetch live categories
            live_response = requests.get(categories_url, params=params, headers=headers, timeout=10)
            live_response.raise_for_status()

            # Fetch movies categories
            params['action'] = 'get_vod_categories'
            movies_response = requests.get(categories_url, params=params, headers=headers, timeout=10)
            movies_response.raise_for_status()

            # Fetch series categories
            params['action'] = 'get_series_categories'
            series_response = requests.get(categories_url, params=params, headers=headers, timeout=10)
            series_response.raise_for_status()

            # Handle response format like enhanced method
            live_categories = live_response.json()
            movies_categories = movies_response.json()
            series_categories = series_response.json()

            logger.info(f"M3U Raw responses - Live: {type(live_categories)}, Movies: {type(movies_categories)}, Series: {type(series_categories)}")

            # Ensure we have lists (handle both direct list and dict formats)
            if isinstance(live_categories, dict) and 'live_categories' in live_categories:
                live_categories = live_categories['live_categories']
            if isinstance(movies_categories, dict) and 'vod_categories' in movies_categories:
                movies_categories = movies_categories['vod_categories']
            if isinstance(series_categories, dict) and 'series_categories' in series_categories:
                series_categories = series_categories['series_categories']

            categories = {
                'live': live_categories if isinstance(live_categories, list) else [],
                'vod': movies_categories if isinstance(movies_categories, list) else [],
                'series': series_categories if isinstance(series_categories, list) else []
            }

            logger.info(f"M3U Processed categories - Live: {len(categories['live'])}, Movies: {len(categories['vod'])}, Series: {len(categories['series'])}")

            self.status_updated.emit("✅ Playlist loaded successfully!")
            self.progress_updated.emit(100)
            self.playlist_loaded.emit({
                'server': self.server_url,
                'username': self.username,
                'password': self.password,
                'categories': categories
            })

        except Exception as e:
            logger.error(f"Failed to load M3U playlist: {e}")
            self.error_occurred.emit(f"Failed to load M3U playlist: {str(e)}")



    def load_categories(self):
        """Load categories using exact logic from original Xtream Player.py"""
        try:
            self.status_updated.emit("Loading categories...")
            self.progress_updated.emit(30)

            # Use same parameter structure as original
            params = {
                'username': self.username,
                'password': self.password,
                'action': 'get_live_categories'
            }

            categories_url = f"{self.server_url}/player_api.php"
            headers = {'User-Agent': CUSTOM_USER_AGENT}

            # Fetch live categories
            live_response = requests.get(categories_url, params=params, headers=headers, timeout=10)
            live_response.raise_for_status()

            # Fetch movies categories
            params['action'] = 'get_vod_categories'
            movies_response = requests.get(categories_url, params=params, headers=headers, timeout=10)
            movies_response.raise_for_status()

            # Fetch series categories
            params['action'] = 'get_series_categories'
            series_response = requests.get(categories_url, params=params, headers=headers, timeout=10)
            series_response.raise_for_status()

            # Handle response format like original - direct JSON response
            live_categories = live_response.json()
            movies_categories = movies_response.json()
            series_categories = series_response.json()

            # Ensure we have lists (handle both direct list and dict formats)
            if isinstance(live_categories, dict) and 'live_categories' in live_categories:
                live_categories = live_categories['live_categories']
            if isinstance(movies_categories, dict) and 'vod_categories' in movies_categories:
                movies_categories = movies_categories['vod_categories']
            if isinstance(series_categories, dict) and 'series_categories' in series_categories:
                series_categories = series_categories['series_categories']

            categories = {
                'live': live_categories if isinstance(live_categories, list) else [],
                'vod': movies_categories if isinstance(movies_categories, list) else [],
                'series': series_categories if isinstance(series_categories, list) else []
            }

            logger.info(f"Fetched categories - Live: {len(categories['live'])}, Movies: {len(categories['vod'])}, Series: {len(categories['series'])}")

            self.status_updated.emit("✅ Categories loaded!")
            self.progress_updated.emit(100)
            self.categories_loaded.emit(categories)

        except Exception as e:
            logger.error(f"Failed to load categories: {e}")
            self.error_occurred.emit(f"Failed to load categories: {str(e)}")

    def load_channels(self):
        """Load channels using exact logic from original Xtream Player.py"""
        try:
            self.status_updated.emit("Loading channels...")
            self.progress_updated.emit(30)

            # Use same parameter structure as original
            params = {
                'username': self.username,
                'password': self.password,
                'action': '',
                'category_id': self.category_id
            }

            if self.category_type == 'live':
                params['action'] = 'get_live_streams'
                stream_type = "live"
            elif self.category_type == 'vod':
                params['action'] = 'get_vod_streams'
                stream_type = "movie"
            elif self.category_type == 'series':
                params['action'] = 'get_series'
                stream_type = "series"
            else:
                raise Exception(f"Unknown category type: {self.category_type}")

            streams_url = f"{self.server_url}/player_api.php"
            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(streams_url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            data = response.json()
            if not isinstance(data, list):
                raise Exception("Expected a list of channels")

            # Add stream URLs exactly like original Xtream Player.py
            for entry in data:
                stream_id = entry.get("stream_id")
                container_extension = entry.get("container_extension", "m3u8")
                if stream_id:
                    entry["url"] = f"{self.server_url}/{stream_type}/{self.username}/{self.password}/{stream_id}.{container_extension}"
                else:
                    entry["url"] = None

            self.status_updated.emit(f"✅ Loaded {len(data)} channels!")
            self.progress_updated.emit(100)
            self.channels_loaded.emit(data)

        except Exception as e:
            logger.error(f"Failed to load channels: {e}")
            self.error_occurred.emit(f"Failed to load channels: {str(e)}")

    def load_series_info(self):
        """Load series information including seasons and episodes"""
        try:
            self.status_updated.emit("Loading series information...")
            self.progress_updated.emit(30)

            if not self.series_id:
                raise Exception("Series ID is required")

            url = f"{self.server_url}/player_api.php"
            params = {
                'username': self.username,
                'password': self.password,
                'action': 'get_series_info',
                'series_id': self.series_id
            }

            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            series_info = response.json()
            if not isinstance(series_info, dict):
                raise Exception("Expected series info as dictionary")

            self.status_updated.emit("✅ Series info loaded!")
            self.progress_updated.emit(100)
            self.series_info_loaded.emit(series_info)

        except Exception as e:
            logger.error(f"Failed to load series info: {e}")
            self.error_occurred.emit(f"Failed to load series info: {str(e)}")

class XtreamPlayerWidget(QWidget, DownloadButtonWidget):
    """Xtream player widget with full implementation (same design as Stalker player)"""

    def __init__(self):
        super().__init__()
        if DOWNLOAD_AVAILABLE:
            DownloadButtonWidget.__init__(self)
        self.server_url = None
        self.username = None
        self.password = None
        self.categories = {}
        self.current_channels = []
        self.current_view = "categories"
        self.current_category = None

        # Navigation system like original Xtream Player
        self.navigation_stacks = {'Live': [], 'Movies': [], 'Series': []}
        self.top_level_scroll_positions = {'Live': 0, 'Movies': 0, 'Series': 0}
        self.groups = {'Live': [], 'Movies': [], 'Series': []}
        self.entries_per_tab = {'Live': [], 'Movies': [], 'Series': []}
        self.series_info = None
        self.current_series_list = []
        self.current_seasons = []
        self.current_episodes = []

        # VLC setup
        self.vlc_instance = None
        self.vlc_media_player = None
        self.vlc_widget = None

        self.init_ui()
        self.setup_vlc()

    def init_ui(self):
        """Initialize the user interface using design.py layout structure"""
        # Main container - horizontal split like design.py
        main_container = QSplitter(Qt.Orientation.Horizontal)
        main_container.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {Colors.PRIMARY};
                width: 3px;
            }}
        """)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.addWidget(main_container)

        # Left panel (weight=1) - Connection fields and content lists
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(5)
        left_layout.setContentsMargins(5, 5, 5, 5)

        # Connection section (compact design like Stalker player)
        connection_frame = QFrame()
        connection_frame.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                background-color: {Colors.SURFACE};
                padding: 5px;
                margin-bottom: 3px;
            }}
        """)
        connection_layout = QVBoxLayout(connection_frame)
        connection_layout.setSpacing(3)  # Reduced spacing for compact layout

        # Connection method selection
        method_layout = QHBoxLayout()
        method_label = QLabel("Connection Method:")
        method_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.method_combo = QComboBox()
        self.method_combo.addItems(["Manual Entry", "M3U URL"])
        self.method_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                min-width: 120px;
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: none;
            }}
        """)
        self.method_combo.currentTextChanged.connect(self.on_method_changed)
        method_layout.addWidget(method_label)
        method_layout.addWidget(self.method_combo)
        method_layout.addStretch()
        connection_layout.addLayout(method_layout)

        # Stacked widget for different input methods
        self.input_stack = QStackedWidget()
        self.input_stack.setMinimumHeight(120)  # Increased minimum height for better field visibility

        # Manual entry widget
        manual_widget = QWidget()
        manual_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {Colors.SURFACE};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        manual_layout = QVBoxLayout(manual_widget)
        manual_layout.setSpacing(8)  # Increased spacing for better visibility
        manual_layout.setContentsMargins(10, 10, 10, 10)  # Increased margins for better visibility

        # Server URL
        server_layout = QHBoxLayout()
        server_label = QLabel("Server URL:")
        server_label.setMinimumWidth(90)
        server_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.server_input = QLineEdit()
        self.server_input.setPlaceholderText("http://server.com:8080")
        self.server_input.setMinimumHeight(30)  # Increased height
        self.server_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        server_layout.addWidget(server_label)
        server_layout.addWidget(self.server_input)
        manual_layout.addLayout(server_layout)

        # Username
        username_layout = QHBoxLayout()
        username_label = QLabel("Username:")
        username_label.setMinimumWidth(90)
        username_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("username")
        self.username_input.setMinimumHeight(30)  # Increased height
        self.username_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        manual_layout.addLayout(username_layout)

        # Password
        password_layout = QHBoxLayout()
        password_label = QLabel("Password:")
        password_label.setMinimumWidth(90)
        password_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("password")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setMinimumHeight(30)  # Increased height
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        manual_layout.addLayout(password_layout)

        self.input_stack.addWidget(manual_widget)

        # M3U URL widget
        m3u_widget = QWidget()
        m3u_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {Colors.SURFACE};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        m3u_layout = QVBoxLayout(m3u_widget)
        m3u_layout.setSpacing(8)  # Increased spacing for better visibility
        m3u_layout.setContentsMargins(10, 10, 10, 10)  # Increased margins for better visibility

        m3u_url_layout = QHBoxLayout()
        m3u_label = QLabel("M3U URL:")
        m3u_label.setMinimumWidth(90)
        m3u_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; font-size: 12px;")
        self.m3u_input = QLineEdit()
        self.m3u_input.setPlaceholderText("http://server.com:8080/get.php?username=user&password=pass&type=m3u_plus")
        self.m3u_input.setMinimumHeight(30)  # Increased height
        self.m3u_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.SUCCESS};
                background-color: {Colors.SURFACE};
            }}
        """)
        m3u_url_layout.addWidget(m3u_label)
        m3u_url_layout.addWidget(self.m3u_input)
        m3u_layout.addLayout(m3u_url_layout)

        self.input_stack.addWidget(m3u_widget)
        connection_layout.addWidget(self.input_stack)

        # Connect button
        button_layout = QHBoxLayout()
        self.connect_btn = QPushButton("🔗 Connect")
        self.connect_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SUCCESS};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.SUCCESS}DD;
            }}
            QPushButton:pressed {{
                background-color: {Colors.SUCCESS}BB;
            }}
            QPushButton:disabled {{
                background-color: #CCCCCC;
                color: #666666;
            }}
        """)
        self.connect_btn.clicked.connect(self.connect_to_server)
        button_layout.addWidget(self.connect_btn)
        button_layout.addStretch()
        connection_layout.addLayout(button_layout)

        # Set size policy to keep connection frame compact and always visible
        from PyQt6.QtWidgets import QSizePolicy
        connection_frame.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        connection_frame.setMinimumHeight(180)  # Increased minimum height for larger fields
        connection_frame.setMaximumHeight(220)  # Increased to accommodate larger fields

        left_layout.addWidget(connection_frame)

        # Content area (like design.py notebook) - moved to left panel
        # Removed "Content" label to save space

        # Content tabs
        self.content_tabs = QTabWidget()
        self.content_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {Colors.PRIMARY};
                background-color: {Colors.BACKGROUND};
            }}
            QTabBar::tab {{
                background-color: {Colors.SURFACE};
                color: {Colors.TEXT};
                padding: 6px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
                min-width: 60px;
            }}
            QTabBar::tab:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {Colors.PRIMARY}CC;
            }}
        """)

        # Create content lists for each category
        self.live_list = QListWidget()
        self.movies_list = QListWidget()
        self.series_list = QListWidget()

        # Style the lists (same as Stalker player)
        list_style = f"""
            QListWidget {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {Colors.SURFACE};
                border-radius: 4px;
                margin: 1px;
            }}
            QListWidget::item:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QListWidget::item:hover {{
                background-color: {Colors.PRIMARY}AA;
                color: white;
            }}
        """

        self.live_list.setStyleSheet(list_style)
        self.movies_list.setStyleSheet(list_style)
        self.series_list.setStyleSheet(list_style)

        # Connect double-click events
        self.live_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.movies_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.series_list.itemDoubleClicked.connect(self.on_item_double_clicked)

        # Setup context menus for download functionality
        if DOWNLOAD_AVAILABLE:
            self.setup_context_menus()

        # Add tabs
        self.content_tabs.addTab(self.live_list, "📺 Live TV")
        self.content_tabs.addTab(self.movies_list, "🎬 Movies")
        self.content_tabs.addTab(self.series_list, "📺 Series")

        # Add content tabs with stretch factor so they take remaining space
        # while keeping connection frame fixed at the top
        left_layout.addWidget(self.content_tabs, 1)  # Stretch factor 1

        # Test button for controls (temporary for debugging)
        self.test_controls_btn = QPushButton("🎮 Show Controls")
        self.test_controls_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.WARNING};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {Colors.WARNING}DD;
            }}
        """)
        self.test_controls_btn.clicked.connect(self.test_show_controls)
        left_layout.addWidget(self.test_controls_btn)

        # Add left panel to main container
        main_container.addWidget(left_panel)

        # Right panel (weight=3) - Large video player like design.py
        right_panel = QWidget()
        self.setup_video_player_right(right_panel)
        main_container.addWidget(right_panel)

        # Set proportions like design.py (left=1, right=3)
        main_container.setSizes([300, 900])

        # Progress bar (at bottom of left panel)
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                text-align: center;
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                font-weight: bold;
                max-height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.SUCCESS};
                border-radius: 3px;
            }}
        """)
        self.progress_bar.setVisible(False)
        left_layout.addWidget(self.progress_bar)

        # Status label (at bottom of left panel) - REMOVED "Ready to connect" for more space
        self.status_label = QLabel("")
        self.status_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold; padding: 2px; font-size: 11px;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(self.status_label)

    def setup_video_player_right(self, right_panel):
        """Setup the large video player for right panel with auto-hide controls"""
        player_layout = QVBoxLayout(right_panel)
        player_layout.setContentsMargins(5, 5, 5, 5)

        # Create a container for video player with overlay controls
        self.video_container = QWidget()
        self.video_container.setStyleSheet("QWidget { background-color: #1a1a1a; }")
        player_layout.addWidget(self.video_container)

        # Use absolute positioning for overlay controls
        container_layout = QVBoxLayout(self.video_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # Large VLC Player widget (like design.py video_frame) - NO transparency here
        self.vlc_widget = QWidget()
        self.vlc_widget.setStyleSheet(f"""
            QWidget {{
                background-color: #1a1a1a;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                min-height: 500px;
            }}
        """)

        # Enable mouse tracking for auto-hide functionality
        self.vlc_widget.setMouseTracking(True)
        self.video_container.setMouseTracking(True)
        right_panel.setMouseTracking(True)

        container_layout.addWidget(self.vlc_widget)

        # Player controls - Overlay with auto-hide functionality
        self.controls_frame = QFrame(self.video_container)
        self.controls_frame.setStyleSheet(f"""
            QFrame {{
                background-color: rgba(45, 45, 45, 0.9);
                border: 1px solid rgba(0, 123, 255, 0.5);
                border-radius: 25px;
                padding: 4px 8px;
                max-height: 50px;
            }}
        """)

        # Position controls as overlay at bottom center (wider for new controls)
        self.controls_frame.setFixedSize(400, 60)
        self.controls_frame.move(
            (self.video_container.width() - 400) // 2,
            self.video_container.height() - 80
        )

        # Initially hide controls
        self.controls_frame.hide()

        controls_layout = QHBoxLayout(self.controls_frame)
        controls_layout.setSpacing(8)  # Reduce spacing between buttons
        controls_layout.setContentsMargins(8, 4, 8, 4)

        # Control buttons - Compact and transparent design
        self.play_btn = QPushButton("▶️")
        self.pause_btn = QPushButton("⏸️")
        self.stop_btn = QPushButton("⏹️")

        # Style the control buttons - Smaller and more transparent
        button_style = f"""
            QPushButton {{
                background-color: rgba(0, 123, 255, 0.7);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                padding: 4px;
                font-weight: bold;
                font-size: 14px;
                min-width: 40px;
                max-width: 40px;
                min-height: 40px;
                max-height: 40px;
            }}
            QPushButton:hover {{
                background-color: rgba(0, 123, 255, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }}
            QPushButton:pressed {{
                background-color: rgba(0, 123, 255, 1.0);
            }}
            QPushButton:disabled {{
                background-color: rgba(204, 204, 204, 0.5);
                color: rgba(102, 102, 102, 0.7);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """

        self.play_btn.setStyleSheet(button_style)
        self.pause_btn.setStyleSheet(button_style)
        self.stop_btn.setStyleSheet(button_style)

        # Connect control buttons
        self.play_btn.clicked.connect(self.play_video)
        self.pause_btn.clicked.connect(self.pause_video)
        self.stop_btn.clicked.connect(self.stop_video)

        # Initially disable controls
        self.play_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)

        # Volume controls
        self.volume_btn = QPushButton("🔊")
        self.volume_btn.setStyleSheet(button_style)
        self.volume_btn.setFixedSize(30, 30)
        self.volume_btn.clicked.connect(self.toggle_mute)
        self.volume_btn.setToolTip("Toggle Mute")

        # Volume slider
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)  # Default volume 80%
        self.volume_slider.setFixedWidth(60)
        self.volume_slider.valueChanged.connect(self.change_volume)
        self.volume_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {Colors.PRIMARY};
                height: 4px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 2px;
            }}
            QSlider::handle:horizontal {{
                background: {Colors.PRIMARY};
                border: 1px solid {Colors.PRIMARY};
                width: 12px;
                height: 12px;
                border-radius: 6px;
                margin: -4px 0;
            }}
            QSlider::sub-page:horizontal {{
                background: {Colors.PRIMARY};
                border-radius: 2px;
            }}
        """)

        # Subtitle controls
        self.subtitle_btn = QPushButton("📝")
        self.subtitle_btn.setStyleSheet(button_style)
        self.subtitle_btn.setFixedSize(30, 30)
        self.subtitle_btn.clicked.connect(self.toggle_subtitles)
        self.subtitle_btn.setToolTip("Toggle Subtitles")

        # Subtitle file button
        self.subtitle_file_btn = QPushButton("📁")
        self.subtitle_file_btn.setStyleSheet(button_style)
        self.subtitle_file_btn.setFixedSize(30, 30)
        self.subtitle_file_btn.clicked.connect(self.load_subtitle_file)
        self.subtitle_file_btn.setToolTip("Load Subtitle File")

        controls_layout.addWidget(self.play_btn)
        controls_layout.addWidget(self.pause_btn)
        controls_layout.addWidget(self.stop_btn)

        # Add separator
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        separator1.setStyleSheet("color: rgba(255, 255, 255, 0.3);")
        controls_layout.addWidget(separator1)

        # Add volume controls
        controls_layout.addWidget(self.volume_btn)
        controls_layout.addWidget(self.volume_slider)

        # Add separator
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        separator2.setStyleSheet("color: rgba(255, 255, 255, 0.3);")
        controls_layout.addWidget(separator2)

        # Add subtitle controls
        controls_layout.addWidget(self.subtitle_btn)
        controls_layout.addWidget(self.subtitle_file_btn)

        # Setup auto-hide timer
        from PyQt6.QtCore import QTimer
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_controls)
        self.hide_timer.setSingleShot(True)

        # Enable mouse tracking for the video area
        self.video_container.setMouseTracking(True)
        self.vlc_widget.setMouseTracking(True)

        # Install event filters to capture mouse events properly
        self.video_container.installEventFilter(self)
        self.vlc_widget.installEventFilter(self)

    def eventFilter(self, obj, event):
        """Event filter to handle mouse events on video widgets"""
        from PyQt6.QtCore import QEvent

        # Check if the event is from our video widgets
        if obj in [self.video_container, self.vlc_widget]:
            if event.type() == QEvent.Type.Enter:
                # Mouse entered video area
                logger.debug("Xtream Mouse entered video area")
                self.show_controls()
                return False
            elif event.type() == QEvent.Type.Leave:
                # Mouse left video area - start hide timer
                logger.debug("Xtream Mouse left video area")
                self.hide_timer.start(2000)  # Hide after 2 seconds
                return False
            elif event.type() == QEvent.Type.MouseMove:
                # Mouse moved in video area
                logger.debug("Xtream Mouse moved in video area")
                self.show_controls()
                return False

        # Pass the event to the parent class
        return super().eventFilter(obj, event)

    def show_controls(self):
        """Show the overlay controls"""
        if hasattr(self, 'controls_frame'):
            # Position controls at bottom center
            container_width = self.video_container.width()
            container_height = self.video_container.height()
            controls_width = 160
            controls_height = 50

            x = (container_width - controls_width) // 2
            y = container_height - controls_height - 20

            self.controls_frame.move(x, y)
            self.controls_frame.show()
            self.controls_frame.raise_()

            # Stop hide timer if running
            if self.hide_timer.isActive():
                self.hide_timer.stop()

    def hide_controls(self):
        """Hide the overlay controls"""
        if hasattr(self, 'controls_frame'):
            self.controls_frame.hide()

    def test_show_controls(self):
        """Test method to manually show controls for debugging"""
        logger.info("Xtream Test button clicked - attempting to show controls")
        if hasattr(self, 'controls_frame'):
            logger.info(f"Xtream Controls frame exists: {self.controls_frame}")
            logger.info(f"Xtream Controls frame parent: {self.controls_frame.parent()}")
            logger.info(f"Xtream Controls frame visible: {self.controls_frame.isVisible()}")
            logger.info(f"Xtream Video container size: {self.video_container.size()}")

            # Force show controls
            self.show_controls()

            # Also try to show them manually
            self.controls_frame.setVisible(True)
            self.controls_frame.show()
            self.controls_frame.raise_()

            logger.info(f"Xtream After manual show - visible: {self.controls_frame.isVisible()}")
        else:
            logger.error("Xtream Controls frame does not exist!")

    def setup_vlc(self):
        """Setup VLC player (same as Stalker player)"""
        if not VLC_AVAILABLE:
            logger.warning("VLC not available - external player will be used")
            return

        try:
            # Create VLC instance
            self.vlc_instance = vlc.Instance()

            # Create VLC media player
            self.vlc_media_player = self.vlc_instance.media_player_new()

            # Set the VLC widget as the video output
            if hasattr(self.vlc_widget, 'winId'):
                self.vlc_media_player.set_hwnd(self.vlc_widget.winId())

            logger.info("VLC integration setup successful")

        except Exception as e:
            logger.error(f"Failed to setup VLC: {e}")
            self.vlc_instance = None
            self.vlc_media_player = None

    def on_method_changed(self):
        """Handle connection method change"""
        method = self.method_combo.currentText()
        if method == "Manual Entry":
            self.input_stack.setCurrentIndex(0)
        else:  # M3U URL
            self.input_stack.setCurrentIndex(1)

    def connect_to_server(self):
        """Connect to Xtream server (same logic as Xtream Player.py)"""
        try:
            method = self.method_combo.currentText()

            if method == "Manual Entry":
                # Get manual credentials
                self.server_url = self.server_input.text().strip()
                self.username = self.username_input.text().strip()
                self.password = self.password_input.text().strip()

                if not all([self.server_url, self.username, self.password]):
                    QMessageBox.warning(self, "Error", "Please fill in all fields")
                    return

                # Start connection thread
                self.connect_btn.setEnabled(False)
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.status_label.setText("Connecting...")

                self.connection_thread = XtreamPlayerThread(
                    server_url=self.server_url,
                    username=self.username,
                    password=self.password,
                    operation="categories"
                )

            else:  # M3U URL
                # Get M3U URL
                m3u_url = self.m3u_input.text().strip()

                if not m3u_url:
                    QMessageBox.warning(self, "Error", "Please enter M3U URL")
                    return

                # Start M3U loading thread
                self.connect_btn.setEnabled(False)
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.status_label.setText("Loading M3U playlist...")

                self.connection_thread = XtreamPlayerThread(
                    m3u_url=m3u_url,
                    operation="load_m3u"
                )

            # Connect signals
            self.connection_thread.categories_loaded.connect(self.on_categories_loaded)
            self.connection_thread.playlist_loaded.connect(self.on_playlist_loaded)
            self.connection_thread.progress_updated.connect(self.progress_bar.setValue)
            self.connection_thread.status_updated.connect(self.status_label.setText)
            self.connection_thread.error_occurred.connect(self.on_connection_error)
            self.connection_thread.finished.connect(self.on_connection_finished)

            # Start the thread
            self.connection_thread.start()

        except Exception as e:
            logger.error(f"Connection error: {e}")
            QMessageBox.critical(self, "Error", f"Connection failed: {str(e)}")
            self.on_connection_finished()

    def on_categories_loaded(self, categories):
        """Handle categories loaded (same as original Xtream Player.py)"""
        try:
            self.categories = categories

            # Set up groups like original Xtream Player.py
            self.groups = {
                "Live": categories.get('live', []),
                "Movies": categories.get('vod', []),
                "Series": categories.get('series', [])
            }

            # Initialize navigation stacks
            self.navigation_stacks = {'Live': [], 'Movies': [], 'Series': []}
            self.top_level_scroll_positions = {'Live': 0, 'Movies': 0, 'Series': 0}
            self.entries_per_tab = {'Live': [], 'Movies': [], 'Series': []}

            # Update category lists for all tabs
            self.update_category_lists('Live')
            self.update_category_lists('Movies')
            self.update_category_lists('Series')

            self.status_label.setText("✅ Connected successfully!")

        except Exception as e:
            logger.error(f"Error loading categories: {e}")
            self.status_label.setText("Error loading categories")

    def on_playlist_loaded(self, playlist_data):
        """Handle M3U playlist loaded"""
        try:
            self.server_url = playlist_data['server']
            self.username = playlist_data['username']
            self.password = playlist_data['password']
            self.categories = playlist_data['categories']

            logger.info(f"Playlist loaded - Server: {self.server_url}")
            logger.info(f"Categories received: {self.categories}")

            # Log category counts
            live_count = len(self.categories.get('live', []))
            movies_count = len(self.categories.get('vod', []))
            series_count = len(self.categories.get('series', []))
            logger.info(f"Category counts - Live: {live_count}, Movies: {movies_count}, Series: {series_count}")

            # Update manual fields with extracted credentials
            self.server_input.setText(self.server_url)
            self.username_input.setText(self.username)
            self.password_input.setText(self.password)

            # Set up groups like original Xtream Player.py
            self.groups = {
                "Live": self.categories.get('live', []),
                "Movies": self.categories.get('vod', []),
                "Series": self.categories.get('series', [])
            }

            # Initialize navigation stacks
            self.navigation_stacks = {'Live': [], 'Movies': [], 'Series': []}
            self.top_level_scroll_positions = {'Live': 0, 'Movies': 0, 'Series': 0}
            self.entries_per_tab = {'Live': [], 'Movies': [], 'Series': []}

            self.populate_categories()
            self.status_label.setText("✅ M3U playlist loaded successfully!")

        except Exception as e:
            logger.error(f"Error loading playlist: {e}")
            self.status_label.setText("Error loading playlist")

    def populate_categories(self):
        """Populate category lists - now uses update_category_lists for consistency"""
        try:
            # Use the same method as the navigation system for consistency
            self.update_category_lists('Live')
            self.update_category_lists('Movies')
            self.update_category_lists('Series')

            live_count = len(self.groups.get('Live', []))
            movies_count = len(self.groups.get('Movies', []))
            series_count = len(self.groups.get('Series', []))

            logger.info(f"Populated categories: {live_count} Live, {movies_count} Movies, {series_count} Series")

        except Exception as e:
            logger.error(f"Error populating categories: {e}")

    def on_item_double_clicked(self, item):
        """Handle double-click on list items (same logic as original Xtream Player.py)"""
        try:
            # Get the current tab
            current_tab = self.content_tabs.currentIndex()
            tab_names = ["Live", "Movies", "Series"]
            tab_name = tab_names[current_tab]

            selected_text = item.text()
            current_list = [self.live_list, self.movies_list, self.series_list][current_tab]

            # Save current scroll position
            current_scroll_position = current_list.verticalScrollBar().value()
            stack = self.navigation_stacks[tab_name]

            if stack:
                stack[-1]['scroll_position'] = current_scroll_position
            else:
                self.top_level_scroll_positions[tab_name] = current_scroll_position

            self.handle_xtream_double_click(item, selected_text, tab_name, current_list)

        except Exception as e:
            logger.error(f"Error handling double-click: {e}")
            self.status_label.setText(f"❌ Error: {str(e)}")

    def handle_xtream_double_click(self, selected_item, selected_text, tab_name, sender):
        """Handle Xtream double-click logic (same as original Xtream Player.py)"""
        try:
            list_widget = sender
            stack = self.navigation_stacks[tab_name]

            if selected_text == "Go Back":
                if stack:
                    stack.pop()
                    if stack:
                        last_level = stack[-1]
                        level = last_level['level']
                        data = last_level['data']
                        scroll_position = last_level.get('scroll_position', 0)
                        if level == 'channels':
                            self.entries_per_tab[tab_name] = data['entries']
                            self.show_channels(list_widget, tab_name)
                            list_widget.verticalScrollBar().setValue(scroll_position)
                        elif level == 'series_categories':
                            self.show_series_in_category(data['series_list'], restore_scroll_position=True, scroll_position=scroll_position)
                        elif level == 'series':
                            self.show_seasons(data['seasons'], restore_scroll_position=True, scroll_position=scroll_position)
                        elif level == 'season':
                            self.show_episodes(data['episodes'], restore_scroll_position=True, scroll_position=scroll_position)
                    else:
                        self.update_category_lists(tab_name)
                        list_widget.verticalScrollBar().setValue(self.top_level_scroll_positions.get(tab_name, 0))
                return

            if tab_name == "Series":
                if not stack:
                    # Check if this is a category selection
                    selected_entry = selected_item.data(Qt.ItemDataRole.UserRole)
                    logger.info(f"Series category clicked - Text: '{selected_text}', Data: {selected_entry}")

                    if selected_entry and selected_entry.get('category_name'):
                        # This is a category - fetch series in this category
                        category_name = selected_entry.get('category_name')
                        logger.info(f"Fetching series for category: {category_name}")
                        self.fetch_series_in_category(category_name)
                    else:
                        # Fallback: try to match by text (remove emoji)
                        clean_text = selected_text.replace("📺 ", "").strip()
                        logger.info(f"Fallback: trying to match '{clean_text}' against categories")
                        available_categories = [group["category_name"] for group in self.groups["Series"]]
                        logger.info(f"Available series categories: {available_categories}")
                        if clean_text in available_categories:
                            logger.info(f"Match found! Fetching series for: {clean_text}")
                            self.fetch_series_in_category(clean_text)
                        else:
                            logger.warning(f"No match found for '{clean_text}' in series categories")
                            self.status_label.setText(f"❌ Category '{clean_text}' not found")
                elif stack[-1]['level'] == 'series_categories':
                    series_entry = selected_item.data(Qt.ItemDataRole.UserRole)
                    if series_entry and "series_id" in series_entry:
                        self.fetch_seasons(series_entry)
                elif stack[-1]['level'] == 'series':
                    season_number = selected_item.data(Qt.ItemDataRole.UserRole)
                    series_entry = stack[-1]['data']['series_entry']
                    self.fetch_episodes(series_entry, season_number)
                elif stack[-1]['level'] == 'season':
                    selected_entry = selected_item.data(Qt.ItemDataRole.UserRole)
                    if selected_entry and "url" in selected_entry:
                        self.play_channel(selected_entry)
                return
            else:
                # For Live and Movies tabs
                selected_entry = selected_item.data(Qt.ItemDataRole.UserRole)
                if selected_entry and selected_entry.get('category_name'):
                    # This is a category - fetch channels in this category
                    category_name = selected_entry.get('category_name')
                    self.fetch_channels(category_name, tab_name)
                elif selected_entry and "url" in selected_entry:
                    # This is a channel/movie - play it
                    self.play_channel(selected_entry)
                else:
                    # Fallback: try to match by text (remove emoji)
                    clean_text = selected_text.replace("📺 ", "").replace("🎬 ", "").strip()
                    if clean_text in [group["category_name"] for group in self.groups[tab_name]]:
                        self.fetch_channels(clean_text, tab_name)
        except Exception as e:
            logger.error(f"Error loading channels: {e}")
            self.status_label.setText(f"❌ Error: {str(e)}")

    def handle_series_click(self, series_data):
        """Handle series click - load seasons and episodes"""
        try:
            series_name = series_data.get("name", "Unknown Series")
            series_id = series_data.get("series_id") or series_data.get("stream_id")

            if not series_id:
                QMessageBox.warning(self, "Error", "Series ID not found")
                return

            # Show loading
            self.status_label.setText(f"Loading series info for: {series_name}")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # Start series info loading thread
            self.series_thread = XtreamPlayerThread(
                server_url=self.server_url,
                username=self.username,
                password=self.password,
                operation="series_info",
                series_id=series_id
            )

            # Connect signals
            self.series_thread.series_info_loaded.connect(self.on_series_info_loaded)
            self.series_thread.progress_updated.connect(self.progress_bar.setValue)
            self.series_thread.status_updated.connect(self.status_label.setText)
            self.series_thread.error_occurred.connect(self.on_series_error)
            self.series_thread.finished.connect(self.on_connection_finished)

            # Start the thread
            self.series_thread.start()

        except Exception as e:
            logger.error(f"Error handling series click: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load series: {str(e)}")

    def on_series_info_loaded(self, series_info):
        """Handle series info loaded - display seasons and episodes"""
        try:
            self.progress_bar.setVisible(False)

            # Get series details
            info = series_info.get('info', {})
            seasons = series_info.get('seasons', [])
            episodes = series_info.get('episodes', {})

            series_name = info.get('name', 'Unknown Series')

            # Clear current list and show series content
            current_list = self.series_list
            current_list.clear()

            # Add back button
            back_item = QListWidgetItem("⬅️ Back to Series")
            back_item.setData(Qt.ItemDataRole.UserRole, {"type": "back"})
            current_list.addItem(back_item)

            # If there are seasons, show them
            if seasons:
                for season in seasons:
                    season_name = season.get('name', f"Season {season.get('season_number', '?')}")
                    season_number = season.get('season_number')

                    # Get episodes for this season
                    season_episodes = episodes.get(str(season_number), []) if season_number else []
                    episode_count = len(season_episodes)

                    item_text = f"📺 {season_name} ({episode_count} episodes)"
                    item = QListWidgetItem(item_text)

                    # Store season data with episodes
                    season_data = season.copy()
                    season_data['episodes'] = season_episodes
                    season_data['series_info'] = series_info
                    season_data['type'] = 'season'

                    item.setData(Qt.ItemDataRole.UserRole, season_data)
                    current_list.addItem(item)

                self.status_label.setText(f"✅ Loaded {len(seasons)} seasons for {series_name}")

            # If no seasons but episodes exist, show episodes directly
            elif episodes:
                all_episodes = []
                for season_episodes in episodes.values():
                    all_episodes.extend(season_episodes)

                for episode in all_episodes:
                    episode_name = episode.get('title', f"Episode {episode.get('episode_num', '?')}")
                    item_text = f"🎬 {episode_name}"
                    item = QListWidgetItem(item_text)

                    # Create episode URL for playback - improved format
                    episode_id = episode.get('id')
                    container_extension = episode.get('container_extension', 'mp4')
                    if episode_id:
                        # Use proper Xtream-Codes series URL format
                        episode['url'] = f"{self.server_url}/series/{self.username}/{self.password}/{episode_id}.{container_extension}"
                        # Also set name for playback
                        episode['name'] = episode_name

                    episode['type'] = 'episode'
                    item.setData(Qt.ItemDataRole.UserRole, episode)
                    current_list.addItem(item)

                self.status_label.setText(f"✅ Loaded {len(all_episodes)} episodes for {series_name}")

            else:
                # No seasons or episodes found
                no_content_item = QListWidgetItem("❌ No content available for this series")
                current_list.addItem(no_content_item)
                self.status_label.setText(f"No content found for {series_name}")

        except Exception as e:
            logger.error(f"Error handling series info: {e}")
            QMessageBox.critical(self, "Error", f"Failed to process series info: {str(e)}")

    def on_series_error(self, error_message):
        """Handle series loading error"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "Series Error", f"Failed to load series:\n\n{error_message}")
        self.status_label.setText("Failed to load series")

    def show_season_episodes(self, season_data):
        """Show episodes for a selected season"""
        try:
            episodes = season_data.get('episodes', [])
            season_name = season_data.get('name', 'Unknown Season')

            # Clear current list and show episodes
            current_list = self.series_list
            current_list.clear()

            # Add back button
            back_item = QListWidgetItem("⬅️ Back to Seasons")
            back_item.setData(Qt.ItemDataRole.UserRole, {"type": "back_to_seasons", "series_info": season_data.get('series_info')})
            current_list.addItem(back_item)

            # Add episodes
            for episode in episodes:
                episode_name = episode.get('title', f"Episode {episode.get('episode_num', '?')}")
                item_text = f"🎬 {episode_name}"
                item = QListWidgetItem(item_text)

                # Create episode URL for playback - improved format
                episode_id = episode.get('id')
                container_extension = episode.get('container_extension', 'mp4')
                if episode_id:
                    # Use proper Xtream-Codes series URL format
                    episode['url'] = f"{self.server_url}/series/{self.username}/{self.password}/{episode_id}.{container_extension}"
                    # Ensure name is set for playback
                    episode['name'] = episode_name

                episode['type'] = 'episode'
                item.setData(Qt.ItemDataRole.UserRole, episode)
                current_list.addItem(item)

            self.status_label.setText(f"✅ Loaded {len(episodes)} episodes for {season_name}")

        except Exception as e:
            logger.error(f"Error showing season episodes: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show episodes: {str(e)}")

    def go_back_to_categories(self):
        """Go back to categories view"""
        try:
            if self.categories:
                self.populate_categories()
                self.status_label.setText("✅ Back to categories")

        except Exception as e:
            logger.error(f"Error going back to categories: {e}")

    def load_channels(self, category_data, category_type):
        """Load channels for a category"""
        try:
            category_id = category_data.get('category_id')
            category_name = category_data.get('category_name', 'Unknown')

            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText(f"Loading {category_name}...")

            # Start channels loading thread
            self.channels_thread = XtreamPlayerThread(
                server_url=self.server_url,
                username=self.username,
                password=self.password,
                operation="channels",
                category_type=category_type,
                category_id=category_id
            )

            # Connect signals
            self.channels_thread.channels_loaded.connect(self.on_channels_loaded)
            self.channels_thread.progress_updated.connect(self.progress_bar.setValue)
            self.channels_thread.status_updated.connect(self.status_label.setText)
            self.channels_thread.error_occurred.connect(self.on_connection_error)
            self.channels_thread.finished.connect(self.on_connection_finished)

            # Start the thread
            self.channels_thread.start()

        except Exception as e:
            logger.error(f"Error loading channels: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load channels: {str(e)}")

    def on_channels_loaded(self, channels):
        """Handle channels loaded"""
        try:
            self.current_channels = channels

            # Clear current list and populate with channels
            current_tab = self.content_tabs.currentIndex()
            if current_tab == 0:  # Live TV
                current_list = self.live_list
                icon = "📺"
            elif current_tab == 1:  # Movies
                current_list = self.movies_list
                icon = "🎬"
            else:  # Series
                current_list = self.series_list
                icon = "📺"

            current_list.clear()

            # Add back button
            back_item = QListWidgetItem("⬅️ Back to Categories")
            back_item.setData(Qt.ItemDataRole.UserRole, {"type": "back"})
            current_list.addItem(back_item)

            # Add channels
            for channel in channels:
                channel_name = channel.get('name', 'Unknown')
                item = QListWidgetItem(f"{icon} {channel_name}")
                item.setData(Qt.ItemDataRole.UserRole, channel)
                current_list.addItem(item)

            self.status_label.setText(f"✅ Loaded {len(channels)} items")

        except Exception as e:
            logger.error(f"Error handling channels loaded: {e}")
            self.status_label.setText("Error loading channels")

    def play_video(self):
        """Play or resume video"""
        try:
            if self.vlc_media_player and self.vlc_media_player.get_media():
                # Check current state
                state = self.vlc_media_player.get_state()

                if state == vlc.State.Paused:
                    # Resume from pause
                    self.vlc_media_player.set_pause(0)  # 0 = resume, 1 = pause
                    logger.info("Xtream VLC player resumed from pause")
                else:
                    # Start playing
                    self.vlc_media_player.play()
                    logger.info("Xtream VLC player started playing")

                self.play_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)
                self.status_label.setText("▶️ Playing...")
            else:
                QMessageBox.information(self, "Info", "No content selected. Double-click on a channel to play.")

        except Exception as e:
            logger.error(f"Error playing video: {e}")
            QMessageBox.critical(self, "Error", f"Failed to play video: {str(e)}")

    def pause_video(self):
        """Pause video properly"""
        try:
            if self.vlc_media_player:
                try:
                    # Check current state
                    state = self.vlc_media_player.get_state()

                    if state == vlc.State.Playing:
                        # Pause the video
                        self.vlc_media_player.set_pause(1)  # 1 = pause
                        self.play_btn.setEnabled(True)
                        self.pause_btn.setEnabled(False)
                        self.status_label.setText("⏸️ Paused")
                        logger.info("Xtream VLC player paused successfully")
                    else:
                        logger.warning(f"Cannot pause - current state: {state}")
                        self.status_label.setText("⚠️ Cannot pause - not playing")

                except Exception as vlc_error:
                    logger.warning(f"Error pausing Xtream VLC: {vlc_error}")
                    self.status_label.setText("⚠️ Pause may not work with this stream")

        except Exception as e:
            logger.error(f"Error pausing video: {e}")
            self.status_label.setText("❌ Pause error occurred")

    def stop_video(self):
        """Stop video (completely non-blocking)"""
        try:
            # Update UI immediately to prevent freezing
            self.status_label.setText("⏹️ Stopping...")
            self.play_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

            # Stop VLC in a separate thread to prevent freezing
            from PyQt6.QtCore import QThread, pyqtSignal

            class StopThread(QThread):
                finished_signal = pyqtSignal()

                def __init__(self, vlc_player):
                    super().__init__()
                    self.vlc_player = vlc_player

                def run(self):
                    try:
                        if self.vlc_player:
                            self.vlc_player.stop()
                            logger.info("Xtream VLC player stopped successfully")
                    except Exception as e:
                        logger.warning(f"Error stopping Xtream VLC: {e}")
                    finally:
                        self.finished_signal.emit()

            # Create and start stop thread
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                self.stop_thread = StopThread(self.vlc_media_player)
                self.stop_thread.finished_signal.connect(self.on_stop_finished)
                self.stop_thread.start()
            else:
                # No VLC player, just update UI
                self.on_stop_finished()

        except Exception as e:
            logger.error(f"Error stopping video: {e}")
            self.on_stop_finished()

    def on_stop_finished(self):
        """Called when stop operation is finished"""
        try:
            # Reset player widget style
            self.vlc_widget.setStyleSheet(f"""
                QWidget {{
                    background-color: #1a1a1a;
                    border: 2px solid {Colors.PRIMARY};
                    border-radius: 8px;
                    min-height: 300px;
                }}
            """)

            # Update UI to final state
            self.status_label.setText("⏹️ Stopped")
            self.play_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

        except Exception as e:
            logger.error(f"Error in stop finished: {e}")
            self.status_label.setText("❌ Stop error occurred")
            self.play_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

    def play_channel(self, entry):
        """Play a channel (same logic as original Xtream Player.py)"""
        try:
            stream_url = entry.get("url")
            channel_name = entry.get("name", "Unknown")

            if not stream_url:
                self.status_label.setText("❌ Stream URL not found")
                return

            if self.vlc_media_player:
                media = self.vlc_instance.media_new(stream_url)
                self.vlc_media_player.set_media(media)
                self.vlc_media_player.play()

                self.play_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)

                # Update player widget background to show it's active
                self.vlc_widget.setStyleSheet(f"""
                    QWidget {{
                        background-color: #1a1a1a;
                        border: 2px solid {Colors.SUCCESS};
                        border-radius: 8px;
                        min-height: 300px;
                    }}
                """)

                self.status_label.setText(f"🎬 Playing: {channel_name}")
                logger.info(f"Playing: {channel_name}")
            else:
                self.status_label.setText("❌ VLC player not available")

        except Exception as e:
            logger.error(f"Error playing channel: {e}")
            self.status_label.setText(f"❌ Error playing channel: {str(e)}")

    def on_connection_error(self, error_message):
        """Handle connection errors"""
        logger.error(f"Connection error: {error_message}")
        QMessageBox.critical(self, "Connection Error", error_message)
        self.status_label.setText("Connection failed")

    def on_connection_finished(self):
        """Handle connection finished"""
        self.connect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

    # Series handling methods from original Xtream Player.py
    def fetch_series_in_category(self, category_name):
        """Fetch series in category (same as original Xtream Player.py)"""
        try:
            list_widget = self.series_list
            current_scroll_position = list_widget.verticalScrollBar().value()
            stack = self.navigation_stacks['Series']
            if stack:
                stack[-1]['scroll_position'] = current_scroll_position
            else:
                self.top_level_scroll_positions['Series'] = current_scroll_position

            category_id = next(g["category_id"] for g in self.groups["Series"] if g["category_name"] == category_name)

            params = {
                'username': self.username,
                'password': self.password,
                'action': 'get_series',
                'category_id': category_id
            }

            episodes_url = f"{self.server_url}/player_api.php"
            response = requests.get(episodes_url, params=params, timeout=10)
            response.raise_for_status()

            series_list = response.json()
            self.current_series_list = series_list

            self.navigation_stacks['Series'].append({'level': 'series_categories', 'data': {'series_list': series_list}, 'scroll_position': 0})
            self.show_series_in_category(series_list)

        except Exception as e:
            logger.error(f"Error fetching series: {e}")
            self.status_label.setText(f"❌ Error fetching series: {str(e)}")

    def show_series_in_category(self, series_list, restore_scroll_position=False, scroll_position=0):
        """Show series in category (same as original Xtream Player.py)"""
        try:
            list_widget = self.series_list
            list_widget.clear()
            if self.navigation_stacks['Series']:
                go_back_item = QListWidgetItem("Go Back")
                list_widget.addItem(go_back_item)

            items = []
            for entry in series_list:
                item = QListWidgetItem(entry["name"])
                item.setData(Qt.ItemDataRole.UserRole, entry)
                items.append(item)

            items.sort(key=lambda x: x.text())
            for item in items:
                list_widget.addItem(item)

            if restore_scroll_position:
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(0, lambda: list_widget.verticalScrollBar().setValue(scroll_position))
            else:
                list_widget.verticalScrollBar().setValue(0)

            self.current_series_list = series_list
        except Exception as e:
            logger.error(f"Error displaying series: {e}")

    def fetch_seasons(self, series_entry):
        """Fetch seasons (same as original Xtream Player.py)"""
        try:
            list_widget = self.series_list
            current_scroll_position = list_widget.verticalScrollBar().value()
            stack = self.navigation_stacks['Series']
            if stack:
                stack[-1]['scroll_position'] = current_scroll_position
            else:
                self.top_level_scroll_positions['Series'] = current_scroll_position

            params = {
                'username': self.username,
                'password': self.password,
                'action': 'get_series_info',
                'series_id': series_entry["series_id"]
            }

            eps_url = f"{self.server_url}/player_api.php"
            response = requests.get(eps_url, params=params, timeout=10)
            response.raise_for_status()

            series_info = response.json()
            self.series_info = series_info

            seasons = list(series_info.get("episodes", {}).keys())
            self.navigation_stacks['Series'].append({'level': 'series', 'data': {'series_entry': series_entry, 'seasons': seasons}, 'scroll_position': 0})
            self.show_seasons(seasons)

        except Exception as e:
            logger.error(f"Error fetching seasons: {e}")
            self.status_label.setText(f"❌ Error fetching seasons: {str(e)}")

    def show_seasons(self, seasons, restore_scroll_position=False, scroll_position=0):
        """Show seasons (same as original Xtream Player.py)"""
        try:
            list_widget = self.series_list
            list_widget.clear()
            if self.navigation_stacks['Series']:
                go_back_item = QListWidgetItem("Go Back")
                list_widget.addItem(go_back_item)

            seasons_int = sorted([int(season) for season in seasons])
            items = []
            for season in seasons_int:
                item = QListWidgetItem(f"Season {season}")
                item.setData(Qt.ItemDataRole.UserRole, str(season))
                items.append(item)

            for item in items:
                list_widget.addItem(item)

            if restore_scroll_position:
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(0, lambda: list_widget.verticalScrollBar().setValue(scroll_position))
            else:
                list_widget.verticalScrollBar().setValue(0)

            self.current_seasons = [str(season) for season in seasons_int]
        except Exception as e:
            logger.error(f"Error displaying seasons: {e}")

    def fetch_episodes(self, series_entry, season_number):
        """Fetch episodes (same as original Xtream Player.py)"""
        try:
            list_widget = self.series_list
            current_scroll_position = list_widget.verticalScrollBar().value()
            stack = self.navigation_stacks['Series']
            if stack:
                stack[-1]['scroll_position'] = current_scroll_position
            else:
                self.top_level_scroll_positions['Series'] = current_scroll_position

            episodes = self.series_info.get("episodes", {}).get(str(season_number), [])
            self.navigation_stacks['Series'].append({'level': 'season', 'data': {'season_number': season_number, 'episodes': episodes}, 'scroll_position': 0})
            self.show_episodes(episodes)

        except Exception as e:
            logger.error(f"Error fetching episodes: {e}")
            self.status_label.setText(f"❌ Error fetching episodes: {str(e)}")

    def show_episodes(self, episodes, restore_scroll_position=False, scroll_position=0):
        """Show episodes (same as original Xtream Player.py)"""
        try:
            list_widget = self.series_list
            list_widget.clear()
            if self.navigation_stacks['Series']:
                go_back_item = QListWidgetItem("Go Back")
                list_widget.addItem(go_back_item)

            episodes_sorted = sorted(episodes, key=lambda x: int(x.get('episode_num', 0)))
            stack = self.navigation_stacks['Series']
            if stack and len(stack) >= 2 and 'series_entry' in stack[-2]['data']:
                series_title = stack[-2]['data']['series_entry'].get('name', '').strip()
            else:
                series_title = "Unknown Series"

            items = []
            for episode in episodes_sorted:
                raw_episode_title = str(episode.get('title', 'Untitled Episode')).strip()
                season = str(episode.get('season', '1'))
                episode_num = str(episode.get('episode_num', '1'))

                try:
                    season_int = int(season)
                    episode_num_int = int(episode_num)
                    episode_code = f"S{season_int:02d}E{episode_num_int:02d}"
                except ValueError:
                    episode_code = f"S{season}E{episode_num}"

                import re
                if series_title.lower() in raw_episode_title.lower():
                    episode_title = re.sub(re.escape(series_title), '', raw_episode_title, flags=re.IGNORECASE).strip(" -")
                else:
                    episode_title = raw_episode_title

                if episode_code.lower() in episode_title.lower():
                    episode_title = re.sub(re.escape(episode_code), '', episode_title, flags=re.IGNORECASE).strip(" -")

                display_text = f"{series_title} - {episode_code} - {episode_title}"

                episode_entry = {
                    "season": season,
                    "episode_num": episode_num,
                    "name": display_text,
                    "url": f"{self.server_url}/series/{self.username}/{self.password}/{episode['id']}.{episode.get('container_extension', 'm3u8')}",
                    "title": episode_title
                }

                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, episode_entry)
                items.append(item)

            for item in items:
                list_widget.addItem(item)

            if restore_scroll_position:
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(0, lambda: list_widget.verticalScrollBar().setValue(scroll_position))
            else:
                list_widget.verticalScrollBar().setValue(0)

            self.current_episodes = episodes_sorted
        except Exception as e:
            logger.error(f"Error displaying episodes: {e}")

    def fetch_channels(self, category_name, tab_name):
        """Fetch channels using exact logic from original Xtream Player.py"""
        try:
            category_id = next(g["category_id"] for g in self.groups[tab_name] if g["category_name"] == category_name)

            list_widget = [self.live_list, self.movies_list, self.series_list][["Live", "Movies", "Series"].index(tab_name)]
            current_scroll_position = list_widget.verticalScrollBar().value()
            stack = self.navigation_stacks[tab_name]
            if stack:
                stack[-1]['scroll_position'] = current_scroll_position
            else:
                self.top_level_scroll_positions[tab_name] = current_scroll_position

            # Use exact parameter structure from original
            params = {
                'username': self.username,
                'password': self.password,
                'action': '',
                'category_id': category_id
            }

            if tab_name == "Live":
                params['action'] = 'get_live_streams'
                stream_type = "live"
            elif tab_name == "Movies":
                params['action'] = 'get_vod_streams'
                stream_type = "movie"

            streams_url = f"{self.server_url}/player_api.php"
            headers = {'User-Agent': CUSTOM_USER_AGENT}
            response = requests.get(streams_url, params=params, headers=headers, timeout=10)
            response.raise_for_status()

            data = response.json()
            if not isinstance(data, list):
                raise ValueError("Expected a list of channels")
            self.entries_per_tab[tab_name] = data

            entries = self.entries_per_tab[tab_name]

            # Add URLs exactly like original Xtream Player.py
            for entry in entries:
                stream_id = entry.get("stream_id")
                container_extension = entry.get("container_extension", "m3u8")
                if stream_id:
                    entry["url"] = f"{self.server_url}/{stream_type}/{self.username}/{self.password}/{stream_id}.{container_extension}"
                else:
                    entry["url"] = None

            self.navigation_stacks[tab_name].append({'level': 'channels', 'data': {'tab_name': tab_name, 'entries': entries}, 'scroll_position': 0})
            self.show_channels(list_widget, tab_name)

        except Exception as e:
            logger.error(f"Error fetching channels: {e}")
            self.status_label.setText(f"❌ Error fetching channels: {str(e)}")

    def show_channels(self, list_widget, tab_name):
        """Show channels using exact logic from original Xtream Player.py"""
        try:
            list_widget.clear()

            if self.navigation_stacks[tab_name]:
                go_back_item = QListWidgetItem("Go Back")
                list_widget.addItem(go_back_item)

            items = []
            for entry in self.entries_per_tab[tab_name]:
                display_text = entry.get("name", "Unnamed Channel")
                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, entry)
                items.append(item)

            items.sort(key=lambda x: x.text())
            for item in items:
                list_widget.addItem(item)

            list_widget.verticalScrollBar().setValue(0)
        except Exception as e:
            logger.error(f"Error displaying channels: {e}")

    def update_category_lists(self, tab_name):
        """Update category lists (same as original Xtream Player.py)"""
        try:
            list_widget = [self.live_list, self.movies_list, self.series_list][["Live", "Movies", "Series"].index(tab_name)]
            list_widget.clear()

            if self.navigation_stacks[tab_name]:
                go_back_item = QListWidgetItem("Go Back")
                list_widget.addItem(go_back_item)

            group_list = self.groups[tab_name]
            items = []
            for group in group_list:
                category_name = group["category_name"]
                item = QListWidgetItem(category_name)
                # Set the full category data as UserRole
                item.setData(Qt.ItemDataRole.UserRole, group)
                items.append(item)

            items.sort(key=lambda x: x.text())
            for item in items:
                list_widget.addItem(item)

            scroll_position = self.top_level_scroll_positions.get(tab_name, 0)
            list_widget.verticalScrollBar().setValue(scroll_position)
        except Exception as e:
            logger.error(f"Error updating category lists: {e}")
            self.status_label.setText(f"❌ Error updating lists: {str(e)}")

    def setup_context_menus(self):
        """Setup context menus for download functionality"""
        try:
            # Enable context menus for movies and series lists
            self.movies_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            self.series_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

            # Connect context menu signals
            self.movies_list.customContextMenuRequested.connect(
                lambda pos: self.show_context_menu(pos, self.movies_list, 'movie')
            )
            self.series_list.customContextMenuRequested.connect(
                lambda pos: self.show_context_menu(pos, self.series_list, 'series')
            )

        except Exception as e:
            logger.error(f"Error setting up context menus: {e}")

    def show_context_menu(self, position, list_widget, content_type):
        """Show context menu with download options"""
        try:
            item = list_widget.itemAt(position)
            if not item:
                return

            # Get item data
            item_data = item.data(Qt.ItemDataRole.UserRole)
            if not item_data or not isinstance(item_data, dict):
                return

            # Skip back buttons and navigation items
            if item_data.get('type') in ['back', 'back_to_seasons']:
                return

            # Create context menu
            menu = QMenu(self)

            # Add download action for movies and episodes
            if content_type == 'movie' or (content_type == 'series' and 'url' in item_data):
                download_action = menu.addAction("📥 Download")
                download_action.triggered.connect(
                    lambda: self.download_single_item(item_data, content_type)
                )

            # Add batch download for series
            if content_type == 'series' and 'series_id' in item_data:
                batch_action = menu.addAction("📦 Download All Episodes")
                batch_action.triggered.connect(
                    lambda: self.download_series_batch(item_data)
                )

            # Show menu if it has actions
            if menu.actions():
                menu.exec(list_widget.mapToGlobal(position))

        except Exception as e:
            logger.error(f"Error showing context menu: {e}")

    def download_single_item(self, item_data, content_type):
        """Download a single movie or episode"""
        try:
            if not self.download_manager:
                QMessageBox.warning(
                    self, "Download Manager",
                    "Download manager is not available. Please check your installation."
                )
                return

            # Prepare server info
            server_info = {
                'server_url': self.server_url,
                'username': self.username,
                'password': self.password
            }

            # Handle download request
            self.handle_download_request(item_data, server_info, 'xtream')

        except Exception as e:
            logger.error(f"Error downloading single item: {e}")
            QMessageBox.critical(
                self, "Download Error",
                f"Failed to start download: {str(e)}"
            )

    def download_series_batch(self, series_data):
        """Download all episodes of a series"""
        try:
            if not self.download_manager:
                QMessageBox.warning(
                    self, "Download Manager",
                    "Download manager is not available. Please check your installation."
                )
                return

            # Get series info to find all episodes
            series_id = series_data.get('series_id')
            if not series_id:
                QMessageBox.warning(self, "Error", "Series ID not found.")
                return

            # Show confirmation dialog
            reply = QMessageBox.question(
                self, "Batch Download",
                f"Do you want to download all episodes of '{series_data.get('name', 'Unknown')}'?\n\n"
                "This will fetch all seasons and episodes and add them to the download queue.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Start thread to fetch series info and episodes
                self.start_batch_download_thread(series_data)

        except Exception as e:
            logger.error(f"Error starting batch download: {e}")
            QMessageBox.critical(
                self, "Batch Download Error",
                f"Failed to start batch download: {str(e)}"
            )

    def start_batch_download_thread(self, series_data):
        """Start thread to fetch series episodes for batch download"""
        try:
            # Create thread to fetch series info
            self.batch_download_thread = XtreamPlayerThread(
                server_url=self.server_url,
                username=self.username,
                password=self.password,
                operation="series_info",
                series_id=series_data.get('series_id')
            )

            # Connect signals
            self.batch_download_thread.series_info_loaded.connect(
                lambda info: self.process_batch_download(series_data, info)
            )
            self.batch_download_thread.error_occurred.connect(
                lambda error: QMessageBox.critical(
                    self, "Batch Download Error",
                    f"Failed to fetch series information: {error}"
                )
            )

            # Start thread
            self.batch_download_thread.start()
            self.status_label.setText("Fetching series information for batch download...")

        except Exception as e:
            logger.error(f"Error starting batch download thread: {e}")

    def process_batch_download(self, series_data, series_info):
        """Process series info and add all episodes to download queue"""
        try:
            episodes_list = []

            # Extract all episodes from all seasons
            episodes_dict = series_info.get('episodes', {})
            for season_num, season_episodes in episodes_dict.items():
                for episode in season_episodes:
                    # Add season info to episode
                    episode_data = episode.copy()
                    episode_data['season'] = season_num
                    episode_data['series_name'] = series_data.get('name', 'Unknown')
                    episodes_list.append(episode_data)

            if episodes_list:
                # Prepare server info
                server_info = {
                    'server_url': self.server_url,
                    'username': self.username,
                    'password': self.password
                }

                # Add batch downloads
                self.handle_batch_download_request(
                    series_data, episodes_list, server_info, 'xtream'
                )

                self.status_label.setText(f"Added {len(episodes_list)} episodes to download queue")
            else:
                QMessageBox.warning(
                    self, "No Episodes",
                    "No episodes found for this series."
                )
                self.status_label.setText("No episodes found for batch download")

        except Exception as e:
            logger.error(f"Error processing batch download: {e}")
            QMessageBox.critical(
                self, "Batch Download Error",
                f"Failed to process series episodes: {str(e)}"
            )

    def set_download_manager(self, download_manager):
        """Set the download manager instance"""
        if DOWNLOAD_AVAILABLE:
            self.setup_download_manager(download_manager)

    def toggle_mute(self):
        """Toggle mute/unmute"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Get current mute state
                is_muted = self.vlc_media_player.audio_get_mute()

                # Toggle mute
                self.vlc_media_player.audio_set_mute(not is_muted)

                # Update button icon
                if not is_muted:  # Now muted
                    self.volume_btn.setText("🔇")
                    self.volume_btn.setToolTip("Unmute")
                    self.status_label.setText("🔇 Audio muted")
                else:  # Now unmuted
                    self.volume_btn.setText("🔊")
                    self.volume_btn.setToolTip("Mute")
                    self.status_label.setText("🔊 Audio unmuted")

                logger.info(f"Audio {'muted' if not is_muted else 'unmuted'}")
            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error toggling mute: {e}")
            self.status_label.setText("❌ Error controlling audio")

    def change_volume(self, value):
        """Change volume level"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Set volume (0-100)
                self.vlc_media_player.audio_set_volume(value)

                # Update volume button icon based on level
                if value == 0:
                    self.volume_btn.setText("🔇")
                elif value < 30:
                    self.volume_btn.setText("🔈")
                elif value < 70:
                    self.volume_btn.setText("🔉")
                else:
                    self.volume_btn.setText("🔊")

                # Update status
                self.status_label.setText(f"🔊 Volume: {value}%")
                logger.debug(f"Volume set to {value}%")
            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error changing volume: {e}")
            self.status_label.setText("❌ Error controlling volume")

    def toggle_subtitles(self):
        """Show subtitle track selection menu"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Get available subtitle tracks
                subtitle_count = self.vlc_media_player.video_get_spu_count()
                current_track = self.vlc_media_player.video_get_spu()

                # Create context menu for subtitle selection
                from PyQt6.QtWidgets import QMenu
                from PyQt6.QtGui import QAction

                menu = QMenu(self)
                menu.setStyleSheet(f"""
                    QMenu {{
                        background-color: {Colors.SURFACE};
                        color: {Colors.TEXT};
                        border: 2px solid {Colors.PRIMARY};
                        border-radius: 6px;
                        padding: 5px;
                    }}
                    QMenu::item {{
                        background-color: transparent;
                        padding: 8px 20px;
                        border-radius: 4px;
                    }}
                    QMenu::item:selected {{
                        background-color: {Colors.PRIMARY};
                        color: white;
                    }}
                    QMenu::item:checked {{
                        background-color: {Colors.SUCCESS};
                        color: white;
                        font-weight: bold;
                    }}
                """)

                # Add "Disable Subtitles" option
                disable_action = QAction("🚫 Disable Subtitles", self)
                disable_action.setCheckable(True)
                disable_action.setChecked(current_track == -1)
                disable_action.triggered.connect(lambda: self.set_subtitle_track(-1))
                menu.addAction(disable_action)

                if subtitle_count > 0:
                    menu.addSeparator()

                    # Add each available subtitle track
                    for i in range(subtitle_count):
                        # Try to get track description
                        track_description = self.get_subtitle_track_description(i)
                        if track_description:
                            action_text = f"📝 Track {i + 1}: {track_description}"
                        else:
                            action_text = f"📝 Subtitle Track {i + 1}"

                        track_action = QAction(action_text, self)
                        track_action.setCheckable(True)
                        track_action.setChecked(current_track == i)
                        track_action.triggered.connect(lambda checked, track=i: self.set_subtitle_track(track))
                        menu.addAction(track_action)

                    # Show track count in status
                    self.status_label.setText(f"📝 {subtitle_count} subtitle tracks available")
                else:
                    # No embedded tracks available
                    no_tracks_action = QAction("📝 No embedded tracks", self)
                    no_tracks_action.setEnabled(False)
                    menu.addAction(no_tracks_action)
                    self.status_label.setText("📝 No embedded subtitle tracks")

                # Add separator and external file option
                menu.addSeparator()
                load_external_action = QAction("📁 Load External Subtitle File...", self)
                load_external_action.triggered.connect(self.load_subtitle_file)
                menu.addAction(load_external_action)

                # Show menu at button position
                button_pos = self.subtitle_btn.mapToGlobal(self.subtitle_btn.rect().bottomLeft())
                menu.exec(button_pos)

            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error showing subtitle menu: {e}")
            self.status_label.setText("❌ Error accessing subtitles")

    def get_subtitle_track_description(self, track_index):
        """Get description for a subtitle track"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Try to get track description from VLC
                track_descriptions = self.vlc_media_player.video_get_spu_description()
                if track_descriptions and len(track_descriptions) > track_index + 1:  # +1 because first entry is "Disable"
                    # track_descriptions format: [(id, name), ...]
                    track_info = track_descriptions[track_index + 1]  # Skip "Disable" entry
                    if len(track_info) > 1 and track_info[1]:
                        return track_info[1].decode('utf-8') if isinstance(track_info[1], bytes) else str(track_info[1])

                # Fallback: try to detect language from track
                return self.detect_subtitle_language(track_index)

        except Exception as e:
            logger.debug(f"Could not get subtitle track description: {e}")

        return None

    def detect_subtitle_language(self, track_index):
        """Try to detect subtitle language"""
        try:
            # Common language codes and names
            common_languages = {
                'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
                'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ar': 'Arabic',
                'zh': 'Chinese', 'ja': 'Japanese', 'ko': 'Korean', 'hi': 'Hindi',
                'tr': 'Turkish', 'pl': 'Polish', 'nl': 'Dutch', 'sv': 'Swedish',
                'da': 'Danish', 'no': 'Norwegian', 'fi': 'Finnish', 'he': 'Hebrew'
            }

            # This is a basic implementation - in a real scenario, you might
            # analyze the subtitle content or use VLC's language detection
            if track_index == 0:
                return "Default"
            elif track_index == 1:
                return "Secondary"
            else:
                return f"Track {track_index + 1}"

        except Exception:
            return None

    def set_subtitle_track(self, track_index):
        """Set specific subtitle track"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                self.vlc_media_player.video_set_spu(track_index)

                if track_index == -1:
                    self.subtitle_btn.setText("📝")
                    self.status_label.setText("📝 Subtitles disabled")
                    logger.info("Subtitles disabled")
                else:
                    self.subtitle_btn.setText("📝")
                    track_desc = self.get_subtitle_track_description(track_index)
                    if track_desc:
                        self.status_label.setText(f"📝 Active: {track_desc}")
                        logger.info(f"Subtitle track set to: {track_desc}")
                    else:
                        self.status_label.setText(f"📝 Active: Track {track_index + 1}")
                        logger.info(f"Subtitle track set to: {track_index + 1}")
            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error setting subtitle track: {e}")
            self.status_label.setText("❌ Error setting subtitle track")

    def load_subtitle_file(self):
        """Load external subtitle file"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            # Open file dialog for subtitle files
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Select Subtitle File",
                "",
                "Subtitle Files (*.srt *.vtt *.ass *.ssa *.sub *.idx);;All Files (*)"
            )

            if file_path:
                if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                    # Add subtitle file to VLC
                    result = self.vlc_media_player.video_set_subtitle_file(file_path)

                    if result == 0:  # Success
                        self.subtitle_btn.setText("📝")
                        self.status_label.setText(f"📝 Loaded: {os.path.basename(file_path)}")
                        logger.info(f"Loaded subtitle file: {file_path}")
                    else:
                        self.status_label.setText("❌ Failed to load subtitle file")
                        logger.error(f"Failed to load subtitle file: {file_path}")
                else:
                    self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error loading subtitle file: {e}")
            self.status_label.setText("❌ Error loading subtitle file")
