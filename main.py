import sys
import argparse
import os
from PyQt6.QtWidgets import <PERSON>App<PERSON>, QSplashScreen
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect, pyqtSignal
from PyQt6.QtGui import QPixmap, QPainter, QFont, QColor, QPen, QBrush
# GUI import will be done dynamically when needed

class MegaIPTVSplashScreen(QSplashScreen):
    """Mega IPTV Tools splash screen matching the tkinter version"""

    # Signal emitted when splash screen is completely finished
    splash_finished = pyqtSignal()

    def __init__(self, on_finished_callback=None):
        print("Creating MegaIPTVSplashScreen...")
        # Create a custom pixmap for the splash screen
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(240, 240, 240))  # Light gray background like tkinter
        super().__init__(pixmap)

        self.setWindowFlags(Qt.WindowType.SplashScreen | Qt.WindowType.FramelessWindowHint)

        # Store callback
        self.on_finished_callback = on_finished_callback

        # Center the splash screen
        self.center_on_screen()

        # Load logo
        self.logo_pixmap = None
        self.load_logo()

        # Progress animation
        self.progress_value = 0
        self.loading_text = "Loading..."

        # Setup animations
        self.setup_animations()

        # Timer for progress animation
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_timer.start(50)  # Update every 50ms
        print("Progress timer started - updating every 50ms")

        # Remove automatic close timer - let progress control the closing

    def center_on_screen(self):
        """Center the splash screen on the screen"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)

    def load_logo(self):
        """Load the mega-iptv.png logo"""
        try:
            logo_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mega-iptv.png")
            if os.path.exists(logo_path):
                self.logo_pixmap = QPixmap(logo_path)
                # Resize logo to 150x150 like in tkinter version
                self.logo_pixmap = self.logo_pixmap.scaled(150, 150, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        except Exception as e:
            print(f"Error loading splash screen logo: {e}")

    def setup_animations(self):
        """Setup fade in animation"""
        # Fade in animation
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        # Start fade in
        self.fade_animation.start()

    def update_progress(self):
        """Update progress animation"""
        if self.progress_value < 100:
            self.progress_value += 2  # Progress over 2.5 seconds
            # Print progress every 20%
            if self.progress_value % 20 == 0:
                print(f"Progress: {self.progress_value}%")
        elif self.progress_value >= 100 and self.progress_timer.isActive():
            # Progress complete, trigger finish (only once)
            print(f"Progress complete ({self.progress_value}%) - finishing splash...")
            self.progress_timer.stop()
            QTimer.singleShot(500, self.finish_animation)  # Small delay before closing

        # Trigger repaint
        self.update()

    def update_text(self, text):
        """Update the loading text"""
        self.loading_text = text
        self.update()

    def finish_animation(self):
        """Finish animation and close splash"""
        print("Finishing splash animation...")
        # Stop timers
        self.progress_timer.stop()

        # Update loading text
        self.loading_text = "Starting application..."
        self.update()

        # Short delay then emit signal and close
        QTimer.singleShot(300, self._finish_and_emit)

    def _finish_and_emit(self):
        """Emit signal and close splash"""
        print("Emitting splash_finished signal...")
        self.splash_finished.emit()  # Emit signal first

        # Also call callback if provided
        if self.on_finished_callback:
            print("Calling finished callback...")
            self.on_finished_callback()

        print("Signal emitted, closing splash in 100ms...")
        QTimer.singleShot(100, self.close)  # Then close after a short delay

    def paintEvent(self, _event):
        """Paint event to draw the splash screen content"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Fill background with light gray like tkinter
        painter.fillRect(self.rect(), QColor(240, 240, 240))

        # Draw logo if loaded
        if self.logo_pixmap:
            logo_x = (self.width() - self.logo_pixmap.width()) // 2
            logo_y = 20
            painter.drawPixmap(logo_x, logo_y, self.logo_pixmap)

        # Draw app name
        painter.setPen(QColor(0, 0, 0))
        title_font = QFont("Helvetica", 18, QFont.Weight.Bold)
        painter.setFont(title_font)
        title_rect = QRect(0, 180, self.width(), 30)
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, "Mega IPTV Tools")

        # Draw version
        version_font = QFont("Helvetica", 10, QFont.Weight.Normal)
        painter.setFont(version_font)
        version_rect = QRect(0, 205, self.width(), 20)
        painter.drawText(version_rect, Qt.AlignmentFlag.AlignCenter, "Version 1.1")

        # Draw loading text
        loading_font = QFont("Helvetica", 10, QFont.Weight.Normal)
        painter.setFont(loading_font)
        loading_rect = QRect(0, 235, self.width(), 20)
        painter.drawText(loading_rect, Qt.AlignmentFlag.AlignCenter, self.loading_text)

        # Draw progress bar
        self.draw_progress_bar(painter)

    def draw_progress_bar(self, painter):
        """Draw progress bar similar to tkinter ttk.Progressbar"""
        # Progress bar dimensions
        bar_width = 300
        bar_height = 20
        bar_x = (self.width() - bar_width) // 2
        bar_y = 260

        # Draw progress bar background
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawRect(bar_x, bar_y, bar_width, bar_height)

        # Draw progress bar fill (indeterminate style like tkinter)
        if self.progress_value < 100:
            # Create moving progress indicator
            indicator_width = 60
            position = int((self.progress_value % 100) * (bar_width - indicator_width) / 100)

            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(QBrush(QColor(0, 120, 215)))  # Windows-style blue
            painter.drawRect(bar_x + position, bar_y + 2, indicator_width, bar_height - 4)

def show_splash_and_launch_gui():
    """Show splash screen and automatically launch main GUI when done"""
    # Create QApplication if it doesn't exist
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
        app.setStyle('Fusion')

    # Variable to store main window reference
    main_window = None

    # Create a function that will be called when splash finishes
    def launch_main_gui():
        nonlocal main_window
        print("Splash finished - launching main GUI...")

        # Import and create the main window
        from gui import MainWindow
        from styles import Colors

        # Apply the same styling as the main GUI
        app.setStyleSheet(f"""
            QApplication {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
            }}
        """)

        # Create and show main window
        main_window = MainWindow()
        main_window.show()

        print("Main GUI launched successfully!")

    # Create splash screen with both signal and callback
    splash = MegaIPTVSplashScreen(on_finished_callback=launch_main_gui)

    # Also connect the signal as backup
    splash.splash_finished.connect(launch_main_gui)

    splash.show()

    # Start the event loop
    sys.exit(app.exec())

def run_cli():
    print("CLI mode not implemented yet")
    sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="AIO IPTV TOOL Suite V6.5")
    parser.add_argument("--mode", choices=["gui", "cli"], default="gui",
                      help="Choose the mode to run the application")
    args = parser.parse_args()

    if args.mode == "gui":
        # Show splash screen and automatically launch main GUI
        show_splash_and_launch_gui()
    else:
        run_cli()