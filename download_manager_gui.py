"""
Download Manager GUI - Professional interface for managing movie/series downloads
"""

import os
import sys
import json
import subprocess
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QProgressBar, QListWidget,
                            QListWidgetItem, QGroupBox, QComboBox, QSpinBox,
                            QLineEdit, QTextEdit, QCheckBox, QFileDialog,
                            QMessageBox, QTabWidget, QSplitter, QFrame,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView, QMenu, QApplication)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QIcon, QPixmap, QAction

from download_manager import DownloadItem, DownloadProgress, DownloadWorker
from styles import Colors

import logging
logger = logging.getLogger(__name__)

class DownloadManagerWidget(QWidget):
    """Main download manager interface"""
    
    def __init__(self):
        super().__init__()
        self.downloads: Dict[str, DownloadProgress] = {}
        self.workers: Dict[str, DownloadWorker] = {}
        self.download_queue: List[DownloadItem] = []
        self.settings = self.load_settings()
        
        self.init_ui()
        self.setup_timers()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("🎬 Professional Download Manager")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet(f"color: {Colors.PRIMARY}; margin: 10px;")
        layout.addWidget(title_label)
        
        # Create main splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Download queue and controls
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Download details and settings
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([600, 400])
        layout.addWidget(splitter)
        
        # Status bar
        self.status_bar = self.create_status_bar()
        layout.addWidget(self.status_bar)
        
        self.setLayout(layout)
        
    def create_left_panel(self) -> QWidget:
        """Create left panel with download queue"""
        panel = QWidget()
        layout = QVBoxLayout()
        
        # Controls section
        controls_group = QGroupBox("📥 Download Controls")
        controls_layout = QHBoxLayout()
        
        self.add_download_btn = QPushButton("➕ Add Download")
        self.add_download_btn.clicked.connect(self.add_download_dialog)
        controls_layout.addWidget(self.add_download_btn)
        
        self.start_all_btn = QPushButton("▶️ Start All")
        self.start_all_btn.clicked.connect(self.start_all_downloads)
        controls_layout.addWidget(self.start_all_btn)
        
        self.pause_all_btn = QPushButton("⏸️ Pause All")
        self.pause_all_btn.clicked.connect(self.pause_all_downloads)
        controls_layout.addWidget(self.pause_all_btn)
        
        self.clear_completed_btn = QPushButton("🗑️ Clear Completed")
        self.clear_completed_btn.clicked.connect(self.clear_completed_downloads)
        controls_layout.addWidget(self.clear_completed_btn)
        
        controls_group.setLayout(controls_layout)
        layout.addWidget(controls_group)
        
        # Download queue table
        queue_group = QGroupBox("📋 Download Queue")
        queue_layout = QVBoxLayout()
        
        self.download_table = QTableWidget()
        self.setup_download_table()
        queue_layout.addWidget(self.download_table)
        
        queue_group.setLayout(queue_layout)
        layout.addWidget(queue_group)
        
        panel.setLayout(layout)
        return panel
        
    def create_right_panel(self) -> QWidget:
        """Create right panel with details and settings"""
        panel = QWidget()
        layout = QVBoxLayout()
        
        # Create tabs for different sections
        tabs = QTabWidget()
        
        # Download details tab
        details_tab = self.create_details_tab()
        tabs.addTab(details_tab, "📊 Details")
        
        # Settings tab
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, "⚙️ Settings")
        
        # Statistics tab
        stats_tab = self.create_statistics_tab()
        tabs.addTab(stats_tab, "📈 Statistics")
        
        layout.addWidget(tabs)
        panel.setLayout(layout)
        return panel
        
    def create_details_tab(self) -> QWidget:
        """Create download details tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Selected download info
        info_group = QGroupBox("ℹ️ Download Information")
        info_layout = QGridLayout()
        
        self.detail_title = QLabel("No download selected")
        self.detail_title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        info_layout.addWidget(QLabel("Title:"), 0, 0)
        info_layout.addWidget(self.detail_title, 0, 1)
        
        self.detail_status = QLabel("-")
        info_layout.addWidget(QLabel("Status:"), 1, 0)
        info_layout.addWidget(self.detail_status, 1, 1)
        
        self.detail_progress = QProgressBar()
        info_layout.addWidget(QLabel("Progress:"), 2, 0)
        info_layout.addWidget(self.detail_progress, 2, 1)
        
        self.detail_speed = QLabel("-")
        info_layout.addWidget(QLabel("Speed:"), 3, 0)
        info_layout.addWidget(self.detail_speed, 3, 1)
        
        self.detail_eta = QLabel("-")
        info_layout.addWidget(QLabel("ETA:"), 4, 0)
        info_layout.addWidget(self.detail_eta, 4, 1)
        
        self.detail_size = QLabel("-")
        info_layout.addWidget(QLabel("Size:"), 5, 0)
        info_layout.addWidget(self.detail_size, 5, 1)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # Download actions
        actions_group = QGroupBox("🎮 Actions")
        actions_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("▶️ Start")
        self.start_btn.clicked.connect(self.start_selected_download)
        actions_layout.addWidget(self.start_btn)
        
        self.pause_btn = QPushButton("⏸️ Pause")
        self.pause_btn.clicked.connect(self.pause_selected_download)
        actions_layout.addWidget(self.pause_btn)
        
        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.clicked.connect(self.cancel_selected_download)
        actions_layout.addWidget(self.cancel_btn)
        
        self.remove_btn = QPushButton("🗑️ Remove")
        self.remove_btn.clicked.connect(self.remove_selected_download)
        actions_layout.addWidget(self.remove_btn)
        
        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)
        
        # Preview/thumbnail area
        preview_group = QGroupBox("🖼️ Preview")
        preview_layout = QVBoxLayout()
        
        self.preview_label = QLabel("No preview available")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setMinimumHeight(200)
        self.preview_label.setStyleSheet(f"border: 1px solid {Colors.SURFACE}; background: {Colors.BACKGROUND};")
        preview_layout.addWidget(self.preview_label)
        
        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)
        
        widget.setLayout(layout)
        return widget
        
    def create_settings_tab(self) -> QWidget:
        """Create settings tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Download settings
        download_group = QGroupBox("📁 Download Settings")
        download_layout = QGridLayout()
        
        # Output directory
        download_layout.addWidget(QLabel("Output Directory:"), 0, 0)
        self.output_dir_input = QLineEdit(self.settings.get('output_dir', './Downloads'))
        download_layout.addWidget(self.output_dir_input, 0, 1)
        
        browse_btn = QPushButton("📂 Browse")
        browse_btn.clicked.connect(self.browse_output_directory)
        download_layout.addWidget(browse_btn, 0, 2)
        
        # Concurrent downloads
        download_layout.addWidget(QLabel("Concurrent Downloads:"), 1, 0)
        self.concurrent_downloads = QSpinBox()
        self.concurrent_downloads.setRange(1, 10)
        self.concurrent_downloads.setValue(self.settings.get('concurrent_downloads', 3))
        download_layout.addWidget(self.concurrent_downloads, 1, 1)
        
        # Output format
        download_layout.addWidget(QLabel("Output Format:"), 2, 0)
        self.output_format = QComboBox()
        self.output_format.addItems(['mp4', 'mkv', 'avi', 'original'])
        self.output_format.setCurrentText(self.settings.get('output_format', 'mp4'))
        download_layout.addWidget(self.output_format, 2, 1)
        
        # Quality settings
        download_layout.addWidget(QLabel("Video Quality:"), 3, 0)
        self.video_quality = QComboBox()
        self.video_quality.addItems(['Original', 'High (1080p)', 'Medium (720p)', 'Low (480p)'])
        self.video_quality.setCurrentText(self.settings.get('video_quality', 'Original'))
        download_layout.addWidget(self.video_quality, 3, 1)
        
        download_group.setLayout(download_layout)
        layout.addWidget(download_group)
        
        # Advanced settings
        advanced_group = QGroupBox("🔧 Advanced Settings")
        advanced_layout = QGridLayout()
        
        # Auto-start downloads
        self.auto_start_check = QCheckBox("Auto-start downloads")
        self.auto_start_check.setChecked(self.settings.get('auto_start', True))
        advanced_layout.addWidget(self.auto_start_check, 0, 0, 1, 2)
        
        # Download subtitles
        self.download_subtitles_check = QCheckBox("Download subtitles when available")
        self.download_subtitles_check.setChecked(self.settings.get('download_subtitles', True))
        advanced_layout.addWidget(self.download_subtitles_check, 1, 0, 1, 2)
        
        # Create series folders
        self.series_folders_check = QCheckBox("Create organized folders for series")
        self.series_folders_check.setChecked(self.settings.get('series_folders', True))
        advanced_layout.addWidget(self.series_folders_check, 2, 0, 1, 2)
        
        # Retry failed downloads
        advanced_layout.addWidget(QLabel("Retry Failed Downloads:"), 3, 0)
        self.retry_count = QSpinBox()
        self.retry_count.setRange(0, 10)
        self.retry_count.setValue(self.settings.get('retry_count', 3))
        advanced_layout.addWidget(self.retry_count, 3, 1)
        
        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)
        
        # Save settings button
        save_settings_btn = QPushButton("💾 Save Settings")
        save_settings_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_settings_btn)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
        
    def create_statistics_tab(self) -> QWidget:
        """Create statistics tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        stats_group = QGroupBox("📊 Download Statistics")
        stats_layout = QGridLayout()
        
        self.total_downloads_label = QLabel("0")
        stats_layout.addWidget(QLabel("Total Downloads:"), 0, 0)
        stats_layout.addWidget(self.total_downloads_label, 0, 1)
        
        self.completed_downloads_label = QLabel("0")
        stats_layout.addWidget(QLabel("Completed:"), 1, 0)
        stats_layout.addWidget(self.completed_downloads_label, 1, 1)
        
        self.failed_downloads_label = QLabel("0")
        stats_layout.addWidget(QLabel("Failed:"), 2, 0)
        stats_layout.addWidget(self.failed_downloads_label, 2, 1)
        
        self.total_size_label = QLabel("0 MB")
        stats_layout.addWidget(QLabel("Total Downloaded:"), 3, 0)
        stats_layout.addWidget(self.total_size_label, 3, 1)
        
        self.avg_speed_label = QLabel("0 MB/s")
        stats_layout.addWidget(QLabel("Average Speed:"), 4, 0)
        stats_layout.addWidget(self.avg_speed_label, 4, 1)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def setup_download_table(self):
        """Setup the download queue table"""
        headers = ["Title", "Status", "Progress", "Speed", "ETA", "Size", "Type"]
        self.download_table.setColumnCount(len(headers))
        self.download_table.setHorizontalHeaderLabels(headers)

        # Set column widths
        header = self.download_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Title
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)    # Status
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)    # Progress
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)    # Speed
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)    # ETA
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)    # Size
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)    # Type

        self.download_table.setColumnWidth(1, 100)  # Status
        self.download_table.setColumnWidth(2, 120)  # Progress
        self.download_table.setColumnWidth(3, 100)  # Speed
        self.download_table.setColumnWidth(4, 80)   # ETA
        self.download_table.setColumnWidth(5, 100)  # Size
        self.download_table.setColumnWidth(6, 80)   # Type

        # Set selection behavior
        self.download_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.download_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Connect selection change
        self.download_table.itemSelectionChanged.connect(self.on_download_selected)

        # Setup context menu
        self.download_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.download_table.customContextMenuRequested.connect(self.show_context_menu)

    def create_status_bar(self) -> QWidget:
        """Create status bar"""
        status_widget = QFrame()
        status_widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout()

        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        layout.addStretch()

        self.active_downloads_label = QLabel("Active: 0")
        layout.addWidget(self.active_downloads_label)

        self.queue_size_label = QLabel("Queue: 0")
        layout.addWidget(self.queue_size_label)

        self.total_speed_label = QLabel("Speed: 0 MB/s")
        layout.addWidget(self.total_speed_label)

        status_widget.setLayout(layout)
        return status_widget

    def setup_timers(self):
        """Setup update timers"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # Update every second

    def load_settings(self) -> Dict:
        """Load settings from file"""
        settings_file = "download_settings.json"
        default_settings = {
            'output_dir': './Downloads',
            'concurrent_downloads': 3,
            'output_format': 'mp4',
            'video_quality': 'Original',
            'auto_start': True,
            'download_subtitles': True,
            'series_folders': True,
            'retry_count': 3
        }

        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    settings = json.load(f)
                    # Merge with defaults
                    default_settings.update(settings)
            return default_settings
        except Exception as e:
            logger.error(f"Error loading settings: {e}")
            return default_settings

    def save_settings(self):
        """Save current settings"""
        settings = {
            'output_dir': self.output_dir_input.text(),
            'concurrent_downloads': self.concurrent_downloads.value(),
            'output_format': self.output_format.currentText(),
            'video_quality': self.video_quality.currentText(),
            'auto_start': self.auto_start_check.isChecked(),
            'download_subtitles': self.download_subtitles_check.isChecked(),
            'series_folders': self.series_folders_check.isChecked(),
            'retry_count': self.retry_count.value()
        }

        try:
            with open("download_settings.json", 'w') as f:
                json.dump(settings, f, indent=2)
            self.settings = settings
            QMessageBox.information(self, "Settings", "Settings saved successfully!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save settings: {e}")

    def browse_output_directory(self):
        """Browse for output directory"""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Download Directory", self.output_dir_input.text()
        )
        if directory:
            self.output_dir_input.setText(directory)

    def add_download_dialog(self):
        """Show add download dialog"""
        # This would typically be called from the main player when user selects content to download
        QMessageBox.information(
            self, "Add Download",
            "Select content from the player to add downloads.\n"
            "This feature integrates with the Stalker and Xtream players."
        )

    def add_download_item(self, item: DownloadItem):
        """Add a download item to the queue"""
        # Create progress tracker
        progress = DownloadProgress(
            item_id=item.id,
            status="queued"
        )
        self.downloads[item.id] = progress
        self.download_queue.append(item)

        # Add to table
        self.add_table_row(item, progress)

        # Auto-start if enabled
        if self.settings.get('auto_start', True):
            self.start_download(item.id)

        self.update_status_bar()

    def add_table_row(self, item: DownloadItem, progress: DownloadProgress):
        """Add row to download table"""
        row = self.download_table.rowCount()
        self.download_table.insertRow(row)

        # Title
        title_item = QTableWidgetItem(item.title)
        title_item.setData(Qt.ItemDataRole.UserRole, item.id)
        self.download_table.setItem(row, 0, title_item)

        # Status
        status_item = QTableWidgetItem(progress.status.title())
        self.download_table.setItem(row, 1, status_item)

        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setValue(int(progress.progress))
        self.download_table.setCellWidget(row, 2, progress_bar)

        # Speed
        speed_item = QTableWidgetItem("0 MB/s")
        self.download_table.setItem(row, 3, speed_item)

        # ETA
        eta_item = QTableWidgetItem("-")
        self.download_table.setItem(row, 4, eta_item)

        # Size
        size_text = self.format_size(item.file_size) if item.file_size > 0 else "Unknown"
        size_item = QTableWidgetItem(size_text)
        self.download_table.setItem(row, 5, size_item)

        # Type
        type_item = QTableWidgetItem(item.content_type.title())
        self.download_table.setItem(row, 6, type_item)

    def start_download(self, item_id: str):
        """Start a specific download"""
        if item_id in self.workers:
            return  # Already running

        # Find the download item
        item = None
        for download_item in self.download_queue:
            if download_item.id == item_id:
                item = download_item
                break

        if not item:
            return

        # Check concurrent download limit
        active_count = sum(1 for w in self.workers.values() if w.isRunning())
        if active_count >= self.settings.get('concurrent_downloads', 3):
            self.status_label.setText("Maximum concurrent downloads reached")
            return

        # Create and start worker
        worker = DownloadWorker(item, self.settings['output_dir'], self.settings)
        worker.progress_updated.connect(self.on_progress_updated)
        worker.status_changed.connect(self.on_status_changed)
        worker.download_completed.connect(self.on_download_completed)
        worker.download_failed.connect(self.on_download_failed)

        self.workers[item_id] = worker
        worker.start()

        # Update status
        self.downloads[item_id].status = "downloading"
        self.downloads[item_id].start_time = datetime.now()
        self.update_table_row(item_id)
        self.update_status_bar()

    def pause_download(self, item_id: str):
        """Pause a specific download"""
        if item_id in self.workers:
            self.workers[item_id].pause()
            self.downloads[item_id].status = "paused"
            self.update_table_row(item_id)

    def resume_download(self, item_id: str):
        """Resume a specific download"""
        if item_id in self.workers:
            self.workers[item_id].resume()
            self.downloads[item_id].status = "downloading"
            self.update_table_row(item_id)

    def cancel_download(self, item_id: str):
        """Cancel a specific download"""
        if item_id in self.workers:
            self.workers[item_id].cancel()
            self.workers[item_id].wait()  # Wait for thread to finish
            del self.workers[item_id]

        self.downloads[item_id].status = "cancelled"
        self.downloads[item_id].end_time = datetime.now()
        self.update_table_row(item_id)
        self.update_status_bar()

    def remove_download(self, item_id: str):
        """Remove a download from the queue"""
        # Cancel if running
        if item_id in self.workers:
            self.cancel_download(item_id)

        # Remove from data structures
        if item_id in self.downloads:
            del self.downloads[item_id]

        self.download_queue = [item for item in self.download_queue if item.id != item_id]

        # Remove from table
        for row in range(self.download_table.rowCount()):
            item = self.download_table.item(row, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == item_id:
                self.download_table.removeRow(row)
                break

        self.update_status_bar()

    def start_all_downloads(self):
        """Start all queued downloads"""
        for item in self.download_queue:
            if self.downloads[item.id].status == "queued":
                self.start_download(item.id)

    def pause_all_downloads(self):
        """Pause all active downloads"""
        for item_id, worker in self.workers.items():
            if worker.isRunning():
                self.pause_download(item_id)

    def clear_completed_downloads(self):
        """Clear all completed downloads from the queue"""
        completed_ids = [
            item_id for item_id, progress in self.downloads.items()
            if progress.status in ["completed", "failed", "cancelled"]
        ]

        for item_id in completed_ids:
            self.remove_download(item_id)

    def start_selected_download(self):
        """Start the selected download"""
        selected_id = self.get_selected_download_id()
        if selected_id:
            self.start_download(selected_id)

    def pause_selected_download(self):
        """Pause the selected download"""
        selected_id = self.get_selected_download_id()
        if selected_id:
            self.pause_download(selected_id)

    def cancel_selected_download(self):
        """Cancel the selected download"""
        selected_id = self.get_selected_download_id()
        if selected_id:
            self.cancel_download(selected_id)

    def remove_selected_download(self):
        """Remove the selected download"""
        selected_id = self.get_selected_download_id()
        if selected_id:
            reply = QMessageBox.question(
                self, "Confirm", "Are you sure you want to remove this download?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.remove_download(selected_id)

    def get_selected_download_id(self) -> Optional[str]:
        """Get the ID of the currently selected download"""
        current_row = self.download_table.currentRow()
        if current_row >= 0:
            item = self.download_table.item(current_row, 0)
            if item:
                return item.data(Qt.ItemDataRole.UserRole)
        return None

    # Signal handlers
    def on_progress_updated(self, item_id: str, progress: float, downloaded: int, total: int, speed: float, eta: int):
        """Handle download progress updates"""
        if item_id in self.downloads:
            self.downloads[item_id].progress = progress
            self.downloads[item_id].downloaded_bytes = downloaded
            self.downloads[item_id].total_bytes = total
            self.downloads[item_id].speed = speed
            self.downloads[item_id].eta = eta

            self.update_table_row(item_id)

    def on_status_changed(self, item_id: str, status: str):
        """Handle download status changes"""
        if item_id in self.downloads:
            self.downloads[item_id].status = status
            self.update_table_row(item_id)

    def on_download_completed(self, item_id: str, output_path: str):
        """Handle download completion"""
        if item_id in self.downloads:
            self.downloads[item_id].status = "completed"
            self.downloads[item_id].end_time = datetime.now()
            self.downloads[item_id].output_path = output_path
            self.downloads[item_id].progress = 100.0

            # Remove worker
            if item_id in self.workers:
                del self.workers[item_id]

            self.update_table_row(item_id)
            self.update_status_bar()

            # Show notification
            self.status_label.setText(f"Download completed: {self.get_item_title(item_id)}")

    def on_download_failed(self, item_id: str, error_message: str):
        """Handle download failure"""
        if item_id in self.downloads:
            self.downloads[item_id].status = "failed"
            self.downloads[item_id].end_time = datetime.now()
            self.downloads[item_id].error_message = error_message

            # Remove worker
            if item_id in self.workers:
                del self.workers[item_id]

            self.update_table_row(item_id)
            self.update_status_bar()

            # Show error
            self.status_label.setText(f"Download failed: {error_message}")

    def on_download_selected(self):
        """Handle download selection change"""
        selected_id = self.get_selected_download_id()
        if selected_id and selected_id in self.downloads:
            progress = self.downloads[selected_id]
            item = self.get_download_item(selected_id)

            # Update details panel
            self.detail_title.setText(item.title if item else "Unknown")
            self.detail_status.setText(progress.status.title())
            self.detail_progress.setValue(int(progress.progress))

            if progress.speed > 0:
                self.detail_speed.setText(f"{self.format_size(progress.speed)}/s")
            else:
                self.detail_speed.setText("-")

            if progress.eta > 0:
                self.detail_eta.setText(self.format_time(progress.eta))
            else:
                self.detail_eta.setText("-")

            if progress.total_bytes > 0:
                downloaded_text = f"{self.format_size(progress.downloaded_bytes)} / {self.format_size(progress.total_bytes)}"
            else:
                downloaded_text = f"{self.format_size(progress.downloaded_bytes)}"
            self.detail_size.setText(downloaded_text)

            # Update button states
            self.update_button_states(progress.status)

    def show_context_menu(self, position):
        """Show context menu for download table"""
        if self.download_table.itemAt(position) is None:
            return

        menu = QMenu()

        selected_id = self.get_selected_download_id()
        if selected_id and selected_id in self.downloads:
            status = self.downloads[selected_id].status

            if status == "queued":
                menu.addAction("▶️ Start", self.start_selected_download)
            elif status == "downloading":
                menu.addAction("⏸️ Pause", self.pause_selected_download)
                menu.addAction("❌ Cancel", self.cancel_selected_download)
            elif status == "paused":
                menu.addAction("▶️ Resume", self.start_selected_download)
                menu.addAction("❌ Cancel", self.cancel_selected_download)

            menu.addSeparator()
            menu.addAction("🗑️ Remove", self.remove_selected_download)

            if status == "completed":
                menu.addAction("📂 Open Folder", self.open_download_folder)

        menu.exec(self.download_table.mapToGlobal(position))

    def open_download_folder(self):
        """Open the folder containing the downloaded file"""
        selected_id = self.get_selected_download_id()
        if selected_id and selected_id in self.downloads:
            output_path = self.downloads[selected_id].output_path
            if output_path and os.path.exists(output_path):
                folder_path = os.path.dirname(output_path)
                if sys.platform == "win32":
                    os.startfile(folder_path)
                elif sys.platform == "darwin":
                    subprocess.run(["open", folder_path])
                else:
                    subprocess.run(["xdg-open", folder_path])

    # Utility methods
    def update_table_row(self, item_id: str):
        """Update a specific row in the download table"""
        for row in range(self.download_table.rowCount()):
            item = self.download_table.item(row, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == item_id:
                progress = self.downloads[item_id]

                # Update status
                self.download_table.item(row, 1).setText(progress.status.title())

                # Update progress bar
                progress_bar = self.download_table.cellWidget(row, 2)
                if progress_bar:
                    progress_bar.setValue(int(progress.progress))

                # Update speed
                if progress.speed > 0:
                    speed_text = f"{self.format_size(progress.speed)}/s"
                else:
                    speed_text = "-"
                self.download_table.item(row, 3).setText(speed_text)

                # Update ETA
                if progress.eta > 0:
                    eta_text = self.format_time(progress.eta)
                else:
                    eta_text = "-"
                self.download_table.item(row, 4).setText(eta_text)

                break

    def update_status_bar(self):
        """Update the status bar"""
        active_count = sum(1 for w in self.workers.values() if w.isRunning())
        queue_count = len([p for p in self.downloads.values() if p.status == "queued"])
        total_speed = sum(p.speed for p in self.downloads.values() if p.status == "downloading")

        self.active_downloads_label.setText(f"Active: {active_count}")
        self.queue_size_label.setText(f"Queue: {queue_count}")
        self.total_speed_label.setText(f"Speed: {self.format_size(total_speed)}/s")

    def update_display(self):
        """Update the display (called by timer)"""
        self.update_status_bar()
        self.update_statistics()

    def update_statistics(self):
        """Update statistics display"""
        total = len(self.downloads)
        completed = len([p for p in self.downloads.values() if p.status == "completed"])
        failed = len([p for p in self.downloads.values() if p.status == "failed"])

        total_downloaded = sum(p.downloaded_bytes for p in self.downloads.values())
        speeds = [p.speed for p in self.downloads.values() if p.speed > 0]
        avg_speed = sum(speeds) / len(speeds) if speeds else 0

        self.total_downloads_label.setText(str(total))
        self.completed_downloads_label.setText(str(completed))
        self.failed_downloads_label.setText(str(failed))
        self.total_size_label.setText(self.format_size(total_downloaded))
        self.avg_speed_label.setText(f"{self.format_size(avg_speed)}/s")

    def update_button_states(self, status: str):
        """Update button states based on download status"""
        self.start_btn.setEnabled(status in ["queued", "paused"])
        self.pause_btn.setEnabled(status == "downloading")
        self.cancel_btn.setEnabled(status in ["downloading", "paused"])
        self.remove_btn.setEnabled(True)

    def get_download_item(self, item_id: str) -> Optional[DownloadItem]:
        """Get download item by ID"""
        for item in self.download_queue:
            if item.id == item_id:
                return item
        return None

    def get_item_title(self, item_id: str) -> str:
        """Get item title by ID"""
        item = self.get_download_item(item_id)
        return item.title if item else "Unknown"

    @staticmethod
    def format_size(bytes_size: float) -> str:
        """Format bytes to human readable size"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_size < 1024.0:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024.0
        return f"{bytes_size:.1f} PB"

    @staticmethod
    def format_time(seconds: int) -> str:
        """Format seconds to human readable time"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            return f"{seconds // 60}m {seconds % 60}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}h {minutes}m"
