#!/usr/bin/env python3
"""
Combined Python Application
Generated by ObfusLite Combiner
"""

# === All Imports ===
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor, QIcon, QAction
from PyQt6.QtWidgets import QApplication
from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QLineEdit, QTreeWidget, QTreeWidgetItem, QFileDialog, QSpinBox, QGroupBox, QMessageBox, QFrame, QCheckBox, QSystemTrayIcon, QMenu, QApplication
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Optional, Dict, List, Tuple
from typing import Dict, List, Optional
from typing import Dict, List, Optional, Any
from typing import Dict, Optional, List
from typing import List, Dict
from typing import Optional
from urllib.parse import urlparse
from urllib.parse import urlparse, parse_qs, urlencode
from urllib.parse import urlparse, unquote
import aiofiles
import aiohttp
import asyncio
import json
import logging
import os
import random
import re
import sys
import time


# === Code from utils.py ===




class QTreeWidget(QWidget):
    """QTreeWidget - auto-generated placeholder implementation"""

    def __init__(self, *args, **kwargs):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)

        label = QLabel("⚠️ QTreeWidget")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #212121; font-size: 16px; font-weight: bold; padding: 20px;")
        layout.addWidget(label)

        info_label = QLabel("This is a placeholder implementation.\nThe actual QTreeWidget class was not found in the combined files.")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("color: #212121; padding: 10px;")
        layout.addWidget(info_label)


class QWidget(QWidget):
    """QWidget - auto-generated placeholder implementation"""

    def __init__(self, *args, **kwargs):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)

        label = QLabel("⚠️ QWidget")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: #212121; font-size: 16px; font-weight: bold; padding: 20px;")
        layout.addWidget(label)

        info_label = QLabel("This is a placeholder implementation.\nThe actual QWidget class was not found in the combined files.")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("color: #212121; padding: 10px;")
        layout.addWidget(info_label)


def get_extension_from_url(url: str) -> str:
    """Extract file extension from URL or default to .mp4"""
    parsed = urlparse(unquote(url))
    path = parsed.path
    VIDEO_EXTENSIONS = ['.mp4', '.mkv', '.avi', '.mov', '.m4v', '.ts']
    ext = os.path.splitext(path)[1].lower()
    if ext in VIDEO_EXTENSIONS:
        return ext
    if '.mp4' in url.lower():
        return '.mp4'
    if '.mkv' in url.lower():
        return '.mkv'
    if '.ts' in url.lower():
        return '.ts'
    return '.mp4'
def format_speed(speed_bytes: float) -> str:
    """Format download speed in human readable format"""
    if speed_bytes < 1024:
        return f'{speed_bytes:.1f} B/s'
    elif speed_bytes < 1024 * 1024:
        return f'{speed_bytes / 1024:.1f} KB/s'
    else:
        return f'{speed_bytes / (1024 * 1024):.1f} MB/s'
def format_status(progress: float) -> str:
    """Format download status"""
    if progress >= 100:
        return 'Finished'
    else:
        return f'{progress:.1f}%'

# === Code from gui.py ===
class M3UDownloaderGUI(QMainWindow):

    def __init__(self):
        super().__init__()
        self.setWindowTitle('M3U Downloader')
        self.setMinimumSize(1200, 800)
        self.download_manager = DownloadManager(max_concurrent=3)
        self.entries: List[M3UEntry] = []
        self.active_downloads = {}
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_download_status)
        self.status_timer.start(500)
        self.setup_tray()
        self.setup_gui()

    def setup_gui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)
        file_group = QGroupBox('File Selection')
        file_layout = QVBoxLayout(file_group)
        m3u_frame = QFrame()
        m3u_layout = QHBoxLayout(m3u_frame)
        m3u_layout.setContentsMargins(0, 0, 0, 0)
        m3u_label = QLabel('M3U File:')
        self.m3u_path = QLineEdit()
        m3u_browse = QPushButton('Browse')
        m3u_browse.clicked.connect(self.browse_m3u)
        m3u_layout.addWidget(m3u_label)
        m3u_layout.addWidget(self.m3u_path)
        m3u_layout.addWidget(m3u_browse)
        output_frame = QFrame()
        output_layout = QHBoxLayout(output_frame)
        output_layout.setContentsMargins(0, 0, 0, 0)
        output_label = QLabel('Output Directory:')
        self.output_dir = QLineEdit()
        output_browse = QPushButton('Browse')
        output_browse.clicked.connect(self.browse_output)
        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_dir)
        output_layout.addWidget(output_browse)
        file_layout.addWidget(m3u_frame)
        file_layout.addWidget(output_frame)
        settings_group = QGroupBox('Download Settings')
        settings_layout = QHBoxLayout(settings_group)
        concurrent_label = QLabel('Concurrent Downloads:')
        self.concurrent_spin = QSpinBox()
        self.concurrent_spin.setRange(1, 10)
        self.concurrent_spin.setValue(3)
        self.concurrent_spin.setToolTip('Number of files to download simultaneously')
        chunks_container = QWidget()
        chunks_layout = QHBoxLayout(chunks_container)
        chunks_layout.setContentsMargins(0, 0, 0, 0)
        self.chunked_checkbox = QCheckBox('Enable Chunked Downloads')
        self.chunked_checkbox.setChecked(True)
        self.chunked_checkbox.setToolTip('Enable splitting files into multiple chunks for faster downloads (requires server support)')
        self.chunked_checkbox.stateChanged.connect(self.toggle_chunks_enabled)
        chunks_label = QLabel('Chunks per File:')
        self.chunks_spin = QSpinBox()
        self.chunks_spin.setRange(1, 8)
        self.chunks_spin.setValue(4)
        self.chunks_spin.setToolTip('Number of chunks to split each file into for parallel downloading')
        chunks_layout.addWidget(self.chunked_checkbox)
        chunks_layout.addWidget(chunks_label)
        chunks_layout.addWidget(self.chunks_spin)
        speed_limit_label = QLabel('Speed Limit (MB/s):')
        self.speed_limit_spin = QSpinBox()
        self.speed_limit_spin.setRange(0, 100)
        self.speed_limit_spin.setValue(0)
        self.speed_limit_spin.setToolTip('0 = No limit, otherwise limit in MB/s')
        self.speed_limit_spin.setSpecialValueText('No Limit')
        self.resume_checkbox = QCheckBox('Enable Resume')
        self.resume_checkbox.setChecked(True)
        self.resume_checkbox.setToolTip('Enable resuming interrupted downloads')
        settings_layout.addWidget(concurrent_label)
        settings_layout.addWidget(self.concurrent_spin)
        settings_layout.addSpacing(20)
        settings_layout.addWidget(chunks_container)
        settings_layout.addSpacing(20)
        settings_layout.addWidget(speed_limit_label)
        settings_layout.addWidget(self.speed_limit_spin)
        settings_layout.addSpacing(20)
        settings_layout.addWidget(self.resume_checkbox)
        settings_layout.addStretch()
        list_group = QGroupBox('Files to Download')
        list_layout = QVBoxLayout(list_group)
        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(['Title', 'URL', 'Status', 'Speed'])
        self.tree.setColumnWidth(0, 350)
        self.tree.setColumnWidth(1, 450)
        self.tree.setColumnWidth(2, 100)
        self.tree.setColumnWidth(3, 100)
        self.tree.setSelectionMode(QTreeWidget.SelectionMode.ExtendedSelection)
        list_layout.addWidget(self.tree)
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 0, 0, 0)
        load_btn = QPushButton('Load M3U')
        download_selected_btn = QPushButton('Download Selected')
        download_all_btn = QPushButton('Download All')
        resume_btn = QPushButton('Resume Downloads')
        self.pause_btn = QPushButton('Pause')
        self.continue_btn = QPushButton('Continue')
        self.stop_btn = QPushButton('Stop')
        self.minimize_btn = QPushButton('Minimize to Tray')
        load_btn.clicked.connect(self.load_m3u)
        download_selected_btn.clicked.connect(self.download_selected)
        download_all_btn.clicked.connect(self.download_all)
        resume_btn.clicked.connect(self.resume_downloads)
        self.pause_btn.clicked.connect(self.pause_downloads)
        self.continue_btn.clicked.connect(self.continue_downloads)
        self.stop_btn.clicked.connect(self.stop_downloads)
        self.minimize_btn.clicked.connect(self.minimize_to_tray)
        resume_btn.setToolTip('Resume any previously interrupted downloads')
        self.pause_btn.setToolTip('Pause current downloads')
        self.continue_btn.setToolTip('Continue paused downloads')
        self.stop_btn.setToolTip('Stop all downloads')
        self.minimize_btn.setToolTip('Minimize application to system tray')
        self.pause_btn.setEnabled(False)
        self.continue_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(load_btn)
        button_layout.addWidget(download_selected_btn)
        button_layout.addWidget(download_all_btn)
        button_layout.addWidget(resume_btn)
        button_layout.addSpacing(20)
        button_layout.addWidget(self.pause_btn)
        button_layout.addWidget(self.continue_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addSpacing(20)
        button_layout.addWidget(self.minimize_btn)
        button_layout.addStretch()
        self.statusBar().showMessage('Ready')
        main_layout.addWidget(file_group)
        main_layout.addWidget(settings_group)
        main_layout.addWidget(list_group)
        main_layout.addWidget(button_frame)
        self.apply_styles()

    def apply_styles(self):
        button_style = '\n            QPushButton {\n                padding: 8px 16px;\n                background-color: #0078D4;\n                color: white;\n                border: none;\n                border-radius: 4px;\n            }\n            QPushButton:hover {\n                background-color: #106EBE;\n            }\n            QPushButton:pressed {\n                background-color: #005A9E;\n            }\n        '
        group_style = '\n            QGroupBox {\n                font-weight: bold;\n                padding-top: 20px;\n                margin-top: 5px;\n            }\n            QGroupBox::title {\n                subcontrol-origin: margin;\n                left: 10px;\n                padding: 0 5px 0 5px;\n            }\n        '
        self.setStyleSheet(button_style + group_style)

    def browse_m3u(self):
        filename, _ = QFileDialog.getOpenFileName(self, 'Select M3U File', '', 'M3U Files (*.m3u);;All Files (*.*)')
        if filename:
            self.m3u_path.setText(filename)

    def browse_output(self):
        directory = QFileDialog.getExistingDirectory(self, 'Select Output Directory')
        if directory:
            self.output_dir.setText(directory)

    def load_m3u(self):
        m3u_file = self.m3u_path.text()
        if not m3u_file:
            QMessageBox.warning(self, 'Error', 'Please select an M3U file first')
            return
        try:
            self.entries = M3UParser.parse(m3u_file)
            self.tree.clear()
            for entry in self.entries:
                item = QTreeWidgetItem([entry.title, entry.url, 'Pending', ''])
                self.tree.addTopLevelItem(item)
            self.statusBar().showMessage(f'Loaded {len(self.entries)} items')
        except Exception as e:
            QMessageBox.critical(self, 'Error', str(e))

    def download_selected(self):
        selected_items = self.tree.selectedItems()
        if not selected_items:
            QMessageBox.information(self, 'Info', 'Please select items to download')
            return
        self._start_download(selected_items)

    def download_all(self):
        all_items = [self.tree.topLevelItem(i) for i in range(self.tree.topLevelItemCount())]
        if not all_items:
            QMessageBox.information(self, 'Info', 'No items to download')
            return
        self._start_download(all_items)

    def _create_download_manager(self):
        """Create a download manager with current settings."""
        try:
            max_concurrent = self.concurrent_spin.value()
            enable_chunked = self.chunked_checkbox.isChecked()
            max_chunks = self.chunks_spin.value() if enable_chunked else 1
            speed_limit_mb = self.speed_limit_spin.value()
            speed_limit = speed_limit_mb * 1024 * 1024 if speed_limit_mb > 0 else None
            enable_resume = self.resume_checkbox.isChecked()
            self.download_manager = DownloadManager(max_concurrent=max_concurrent, max_chunks=max_chunks, max_speed_limit=speed_limit, enable_resume=enable_resume, enable_chunked=enable_chunked)
            speed_str = f'{speed_limit_mb} MB/s' if speed_limit_mb > 0 else 'unlimited'
            chunked_str = f'{max_chunks} chunks per file' if enable_chunked else 'single chunk mode'
            resume_str = 'resume enabled' if enable_resume else 'resume disabled'
            self.statusBar().showMessage(f'Download settings: {max_concurrent} concurrent files, {chunked_str}, speed limit: {speed_str}, {resume_str}')
            return True
        except ValueError:
            QMessageBox.warning(self, 'Error', 'Invalid download settings')
            return False

    def resume_downloads(self):
        """Resume any previously interrupted downloads."""
        output_dir = self.output_dir.text()
        if not output_dir:
            QMessageBox.warning(self, 'Error', 'Please select an output directory')
            return
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        if not self._create_download_manager():
            return

        def update_progress(filename: str, progress: float, speed: Optional[str]=None):
            found = False
            for i in range(self.tree.topLevelItemCount()):
                item = self.tree.topLevelItem(i)
                if item.text(0) == filename:
                    status_text = format_status(progress)
                    item.setText(2, status_text)
                    if progress >= 100:
                        item.setForeground(2, QColor('green'))
                        item.setText(3, '')
                    else:
                        item.setForeground(2, QColor('black'))
                        if speed:
                            item.setText(3, speed)
                    found = True
                    break
            if not found:
                item = QTreeWidgetItem([filename, 'Resuming...', format_status(progress), speed or ''])
                self.tree.addTopLevelItem(item)
                if progress >= 100:
                    item.setForeground(2, QColor('green'))
        incomplete = self.download_manager.get_incomplete_downloads()
        if not incomplete:
            QMessageBox.information(self, 'Info', 'No incomplete downloads to resume')
            return
        confirm = QMessageBox.question(self, 'Resume Downloads', f'Found {len(incomplete)} incomplete downloads. Resume them?', QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if confirm == QMessageBox.StandardButton.Yes:
            try:
                self.download_manager.resume_all_downloads(progress_callback=update_progress)
                self.statusBar().showMessage(f'Resuming {len(incomplete)} downloads...')
                self.pause_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)
                self.continue_btn.setEnabled(False)
            except Exception as e:
                QMessageBox.critical(self, 'Resume Error', f'Failed to resume downloads: {str(e)}')

    def _start_download(self, items):
        output_dir = self.output_dir.text()
        if not output_dir:
            QMessageBox.warning(self, 'Error', 'Please select an output directory')
            return
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        if not self._create_download_manager():
            return
        downloads = []
        for item in items:
            url = item.text(1)
            filename = f'{item.text(0)}{get_extension_from_url(url)}'
            filepath = ensure_unique_filename(output_dir, filename)
            downloads.append((url, filepath))
            item.setText(2, 'Queued')
            item.setText(3, '')

        def update_progress(filename: str, progress: float, speed: Optional[str]=None):
            for i in range(self.tree.topLevelItemCount()):
                item = self.tree.topLevelItem(i)
                if item.text(0) == filename:
                    status_text = format_status(progress)
                    item.setText(2, status_text)
                    if progress >= 100:
                        item.setForeground(2, QColor('green'))
                        item.setText(3, '')
                    else:
                        item.setForeground(2, QColor('black'))
                        if speed:
                            item.setText(3, speed)
                    break
        try:
            self.download_manager.start_downloads(downloads, progress_callback=update_progress)
            self.statusBar().showMessage(f'Downloading {len(downloads)} files...')
            self.pause_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)
            self.continue_btn.setEnabled(False)
        except Exception as e:
            QMessageBox.critical(self, 'Download Error', f'Failed to start downloads: {str(e)}')

    def toggle_chunks_enabled(self, state):
        """Enable or disable the chunks spinbox based on checkbox state."""
        self.chunks_spin.setEnabled(state)
        if not state:
            self.chunks_spin.setValue(1)

    def update_download_status(self):
        """Update the status and speed of active downloads in the UI."""
        active_downloads = self.download_manager.get_active_downloads()
        self.update_control_buttons()
        if not active_downloads:
            return
        for filepath, download_info in active_downloads.items():
            filename = os.path.basename(filepath)
            for i in range(self.tree.topLevelItemCount()):
                item = self.tree.topLevelItem(i)
                if item.text(0) == filename or filename.startswith(item.text(0)):
                    if item.text(2) == 'Queued':
                        item.setText(2, 'Downloading...')
                        item.setForeground(2, QColor('black'))
                    if 'paused' in download_info and download_info['paused'] and (item.text(2) != 'Paused'):
                        item.setText(2, 'Paused')
                        item.setForeground(2, QColor('orange'))
                    if item.text(2) == 'Finished':
                        item.setForeground(2, QColor('green'))
                    if 'speed' in download_info and download_info['speed'] > 0 and (not download_info.get('paused', False)):
                        speed_str = format_speed(download_info['speed'])
                        item.setText(3, speed_str)
                    elif download_info.get('paused', False):
                        item.setText(3, '')
                    break

    def setup_tray(self):
        """Setup system tray icon and menu."""
        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(QIcon.fromTheme('download', QIcon.fromTheme('go-down')))
        tray_menu = QMenu()
        show_action = QAction('Show', self)
        show_action.triggered.connect(self.show)
        quit_action = QAction('Quit', self)
        quit_action.triggered.connect(self.close)
        tray_menu.addAction(show_action)
        tray_menu.addAction(quit_action)
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        self.tray_icon.show()

    def tray_icon_activated(self, reason):
        """Handle tray icon activation."""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show()
            self.setWindowState(self.windowState() & ~Qt.WindowState.WindowMinimized | Qt.WindowState.WindowActive)
            self.activateWindow()

    def minimize_to_tray(self):
        """Minimize the application to system tray."""
        self.hide()
        self.tray_icon.showMessage('M3U Downloader', 'Application minimized to tray. Double-click to restore.', QSystemTrayIcon.MessageIcon.Information, 2000)

    def pause_downloads(self):
        """Pause all active downloads."""
        if self.download_manager.pause_downloads():
            self.statusBar().showMessage('Downloads paused')
            self.pause_btn.setEnabled(False)
            self.continue_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)

    def continue_downloads(self):
        """Continue paused downloads."""
        if self.download_manager.continue_downloads():
            self.statusBar().showMessage('Downloads continuing...')
            self.pause_btn.setEnabled(True)
            self.continue_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)

    def stop_downloads(self):
        """Stop all active downloads."""
        if self.download_manager.stop_downloads():
            self.statusBar().showMessage('Downloads stopped')
            self.pause_btn.setEnabled(False)
            self.continue_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

    def update_control_buttons(self):
        """Update the state of control buttons based on download status."""
        has_active = self.download_manager.has_active_downloads()
        is_paused = self.download_manager.is_paused()
        self.pause_btn.setEnabled(has_active and (not is_paused))
        self.continue_btn.setEnabled(has_active and is_paused)
        self.stop_btn.setEnabled(has_active)

    def closeEvent(self, event):
        """Handle window close event."""
        self.status_timer.stop()
        self.download_manager.shutdown()
        self.tray_icon.hide()
        event.accept()

# === Code from async_downloader.py ===
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('async_downloader')
class AsyncDownloader:

    def __init__(self, max_concurrent: int=3, max_chunks: int=4, max_speed_limit: Optional[int]=None, enable_resume: bool=True, enable_chunked: bool=True):
        self.max_concurrent = max_concurrent
        self.max_chunks = max_chunks
        self.optimizer = DownloadOptimizer(max_speed_limit=max_speed_limit)
        self.connection_pool = ConnectionPool(max_connections=max_concurrent * 2, max_per_host=max_concurrent)
        self.session = None
        self.retry_count = 3
        self.authenticator = IPTVAuthenticator()
        self.chunk_download_tasks = {}
        self.enable_resume = enable_resume
        self.enable_chunked = enable_chunked
        self.download_state = DownloadState() if enable_resume else None
        self.active_downloads = {}

    async def __aenter__(self):
        timeout = aiohttp.ClientTimeout(total=None, connect=60, sock_read=60)
        conn = aiohttp.TCPConnector(limit=self.max_concurrent * 2, force_close=True)
        self.session = aiohttp.ClientSession(timeout=timeout, connector=conn)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.authenticator:
            await self.authenticator.close()
        if self.session:
            await self.session.close()

    async def _refresh_token(self, url: str) -> str:
        from urllib.parse import urlparse, parse_qs, urlencode
        parsed = urlparse(url)
        params = parse_qs(parsed.query)
        if 'play_token' in params:
            base_url = f'{parsed.scheme}://{parsed.netloc}/player_api.php'
            refresh_params = {'mac': params.get('mac', [''])[0], 'type': params.get('type', [''])[0], 'stream': params.get('stream', [''])[0], 'refresh': '1'}
            async with self.session.get(f'{base_url}?{urlencode(refresh_params)}') as response:
                if response.status == 200:
                    data = await response.json()
                    if 'play_token' in data:
                        params['play_token'] = [data['play_token']]
                        return parsed._replace(query=urlencode(params, doseq=True)).geturl()
        return url

    def set_speed_limit(self, limit_bytes_per_sec: Optional[int]) -> None:
        """Set a global speed limit for all downloads in bytes per second."""
        self.optimizer.set_speed_limit(limit_bytes_per_sec)

    async def _download_chunk(self, url: str, filepath: str, start: Optional[int]=None, end: Optional[int]=None, chunk_id: int=0, total_chunks: int=1, total_size: int=0, progress_callback: Optional[Callable]=None, resume_from: int=0) -> int:
        """
        Download a specific chunk of a file with resume support.

        Args:
            url: URL to download from
            filepath: Path to save the file
            start: Start byte position for range request
            end: End byte position for range request
            chunk_id: ID of this chunk
            total_chunks: Total number of chunks
            total_size: Total file size
            progress_callback: Callback for progress updates
            resume_from: Byte position to resume from (for partial downloads)

        Returns:
            Number of bytes downloaded
        """
        temp_filepath = f'{filepath}.part{chunk_id}'
        bytes_downloaded = resume_from
        retries = 0
        download_key = f'{filepath}_{chunk_id}'
        self.active_downloads[download_key] = {'url': url, 'filepath': filepath, 'chunk_id': chunk_id, 'bytes_downloaded': bytes_downloaded, 'total_size': end - start + 1 if end is not None and start is not None else total_size}
        file_mode = 'ab' if resume_from > 0 and os.path.exists(temp_filepath) else 'wb'
        while retries < self.retry_count:
            try:
                await self.connection_pool.acquire(url)
                headers = {'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16', 'Accept': '*/*', 'Connection': 'keep-alive'}
                if start is not None:
                    adjusted_start = start + resume_from
                    range_header = f'bytes={adjusted_start}-'
                    if end is not None:
                        range_header = f'bytes={adjusted_start}-{end}'
                    headers['Range'] = range_header
                elif resume_from > 0:
                    headers['Range'] = f'bytes={resume_from}-'
                chunk_size = 65536
                await self.optimizer.apply_rate_limit(url, chunk_size)
                async with self.session.get(url, headers=headers, allow_redirects=True) as response:
                    if response.status == 458:
                        if retries < self.retry_count - 1:
                            url = await self.authenticator.authenticate(url)
                            retries += 1
                            await asyncio.sleep(2)
                            continue
                    supports_resume = response.status == 206
                    if (start is not None or resume_from > 0) and (not supports_resume):
                        if chunk_id != 0:
                            raise Exception(f"Server doesn't support range requests (HTTP {response.status})")
                        logger.warning(f"Server doesn't support range requests, falling back to full download")
                        if resume_from > 0:
                            logger.warning(f"Server doesn't support resume, starting from beginning")
                            bytes_downloaded = 0
                            file_mode = 'wb'
                    if response.status not in (200, 206):
                        self.optimizer.handle_server_error(url)
                        raise Exception(f'HTTP {response.status}: {response.reason}')
                    os.makedirs(os.path.dirname(filepath), exist_ok=True)
                    start_time = time.time()
                    last_update = start_time
                    speed_samples = []
                    last_save_state = start_time
                    async with aiofiles.open(temp_filepath, file_mode) as f:
                        async for chunk in response.content.iter_chunked(chunk_size):
                            await f.write(chunk)
                            bytes_downloaded += len(chunk)
                            speed_samples.append((time.time(), len(chunk)))
                            self.active_downloads[download_key]['bytes_downloaded'] = bytes_downloaded
                            current_time = time.time()
                            if self.enable_resume and current_time - last_save_state >= 5:
                                if self.download_state and filepath in self.active_downloads:
                                    downloaded_chunks = {}
                                    chunk_ranges = []
                                    for key, data in self.active_downloads.items():
                                        if key.startswith(f'{filepath}_'):
                                            chunk_id = data['chunk_id']
                                            downloaded_chunks[chunk_id] = data['bytes_downloaded']
                                    self.download_state.save_state(filepath=filepath, url=url, downloaded_chunks=downloaded_chunks, total_size=total_size, chunk_ranges=[(start, end) for _, (start, end) in enumerate(self.active_downloads)])
                                    last_save_state = current_time
                            await self.optimizer.apply_rate_limit(url, chunk_size)
                            if current_time - last_update >= 0.5 and progress_callback:
                                speed_samples = [(t, s) for t, s in speed_samples if current_time - t <= 2]
                                if speed_samples:
                                    duration = current_time - speed_samples[0][0]
                                    if duration > 0:
                                        speed = sum((s for _, s in speed_samples)) / duration
                                        speed_str = format_speed(speed)
                                        self.optimizer.update_speed(url, sum((s for _, s in speed_samples)), duration)
                                        if total_size > 0:
                                            chunk_size_bytes = end - start + 1 if end is not None and start is not None else total_size
                                            chunk_progress = bytes_downloaded / chunk_size_bytes
                                            overall_progress = chunk_id / total_chunks + chunk_progress / total_chunks
                                            progress = overall_progress * 100
                                            progress_callback(os.path.basename(filepath), progress, speed_str)
                                last_update = current_time
                    if download_key in self.active_downloads:
                        del self.active_downloads[download_key]
                    return bytes_downloaded
            except Exception as e:
                logger.error(f'Chunk {chunk_id} download error for {url}: {str(e)}')
                if not self.enable_resume and os.path.exists(temp_filepath):
                    os.remove(temp_filepath)
                if retries >= self.retry_count - 1:
                    if not self.enable_resume and os.path.exists(temp_filepath):
                        os.remove(temp_filepath)
                    raise
                retries += 1
                await asyncio.sleep(2 * retries)
            finally:
                self.connection_pool.release(url)
        return bytes_downloaded

    async def _merge_chunks(self, filepath: str, chunk_count: int) -> None:
        """Merge downloaded chunks into the final file."""
        try:
            async with aiofiles.open(filepath, 'wb') as outfile:
                for i in range(chunk_count):
                    chunk_path = f'{filepath}.part{i}'
                    if os.path.exists(chunk_path):
                        async with aiofiles.open(chunk_path, 'rb') as infile:
                            while True:
                                chunk = await infile.read(8192)
                                if not chunk:
                                    break
                                await outfile.write(chunk)
                        os.remove(chunk_path)
                    else:
                        logger.warning(f'Chunk file {chunk_path} not found during merge')
        except Exception as e:
            logger.error(f'Error merging chunks for {filepath}: {str(e)}')
            raise

    async def download_file(self, url: str, filepath: str, progress_callback: Optional[Callable[[str, float, Optional[str]], None]]=None) -> None:
        """Download a file with support for chunked downloading and resume capability."""
        retries = 0
        resume_state = None
        if self.enable_resume and self.download_state:
            resume_state = self.download_state.load_state(filepath)
            if resume_state:
                logger.info(f'Found resume state for {filepath}, attempting to resume download')
        while retries < self.retry_count:
            try:
                if 'play_token' in url:
                    url = await self.authenticator.authenticate(url)
                headers = {'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16', 'Accept': '*/*'}
                supports_range = False
                file_size = 0
                await self.connection_pool.acquire(url)
                try:
                    async with self.session.head(url, headers=headers, allow_redirects=True) as head_response:
                        if head_response.status == 200:
                            file_size = int(head_response.headers.get('content-length', 0))
                            supports_range = 'accept-ranges' in head_response.headers and head_response.headers['accept-ranges'] == 'bytes'
                            if 'accept-ranges' in head_response.headers:
                                if head_response.headers['accept-ranges'] == 'bytes':
                                    supports_range = True
                finally:
                    self.connection_pool.release(url)
                if self.enable_chunked and supports_range and (file_size > 0):
                    chunks = self.optimizer.calculate_optimal_chunks(url, file_size, self.max_chunks)
                    downloaded_chunks = {}
                    if resume_state and resume_state['total_size'] == file_size and (resume_state['url'] == url):
                        downloaded_chunks = resume_state['downloaded_chunks']
                        logger.info(f'Resuming download of {filepath} with {sum(downloaded_chunks.values())} bytes already downloaded')
                    logger.info(f'Downloading {url} in {len(chunks)} chunks')
                    tasks = []
                    for i, (start, end) in enumerate(chunks):
                        resume_from = 0
                        if str(i) in downloaded_chunks:
                            resume_from = downloaded_chunks[str(i)]
                        task = asyncio.create_task(self._download_chunk(url=url, filepath=filepath, start=start, end=end, chunk_id=i, total_chunks=len(chunks), total_size=file_size, progress_callback=progress_callback, resume_from=resume_from))
                        tasks.append(task)
                    await asyncio.gather(*tasks)
                    await self._merge_chunks(filepath, len(chunks))
                    if self.enable_resume and self.download_state:
                        self.download_state.clear_state(filepath)
                else:
                    if not self.enable_chunked:
                        logger.info(f'Chunked downloading is disabled, using single download')
                    elif not supports_range:
                        logger.info(f"Server doesn't support range requests, using single download")
                    elif file_size <= 0:
                        logger.info(f'File size unknown, using single download')
                    resume_from = 0
                    if resume_state and os.path.exists(f'{filepath}.part0'):
                        resume_from = os.path.getsize(f'{filepath}.part0')
                        if resume_from > 0:
                            logger.info(f'Resuming single-chunk download from byte {resume_from}')
                    await self._download_chunk(url=url, filepath=filepath, progress_callback=progress_callback, resume_from=resume_from)
                    if os.path.exists(f'{filepath}.part0'):
                        os.rename(f'{filepath}.part0', filepath)
                    if self.enable_resume and self.download_state:
                        self.download_state.clear_state(filepath)
                return
            except Exception as e:
                logger.error(f'Download error for {url}: {str(e)}')
                if not self.enable_resume:
                    for i in range(self.max_chunks):
                        chunk_path = f'{filepath}.part{i}'
                        if os.path.exists(chunk_path):
                            os.remove(chunk_path)
                    if os.path.exists(filepath):
                        os.remove(filepath)
                if retries >= self.retry_count - 1:
                    raise
                retries += 1
                await asyncio.sleep(2 ** retries)
                self.optimizer.handle_server_error(url)
class DownloadManager:

    def __init__(self, max_concurrent: int=3, max_chunks: int=4, max_speed_limit: Optional[int]=None, enable_resume: bool=True, enable_chunked: bool=True):
        """
        Initialize the download manager.

        Args:
            max_concurrent: Maximum number of concurrent downloads
            max_chunks: Maximum number of chunks per file
            max_speed_limit: Optional speed limit in bytes per second
            enable_resume: Whether to enable resumable downloads
            enable_chunked: Whether to enable chunked downloading
        """
        self.max_concurrent = max_concurrent
        self.max_chunks = max_chunks if enable_chunked else 1
        self.max_speed_limit = max_speed_limit
        self.enable_resume = enable_resume
        self.enable_chunked = enable_chunked
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent)
        self.download_state = DownloadState() if enable_resume else None
        self.active_downloader = None
        self.paused = False
        self.current_downloads = []
        self.future = None

    def set_speed_limit(self, limit_bytes_per_sec: Optional[int]) -> None:
        """Set a global speed limit for all downloads in bytes per second."""
        self.max_speed_limit = limit_bytes_per_sec

    def get_incomplete_downloads(self) -> List[Dict]:
        """Get a list of all incomplete downloads that can be resumed."""
        if self.enable_resume and self.download_state:
            return self.download_state.get_incomplete_downloads()
        return []

    def resume_all_downloads(self, progress_callback: Optional[Callable]=None):
        """Resume all incomplete downloads."""
        if not self.enable_resume:
            logger.warning('Resume feature is disabled')
            return
        incomplete = self.get_incomplete_downloads()
        if not incomplete:
            logger.info('No incomplete downloads to resume')
            return
        downloads = [(state['url'], state['filepath']) for state in incomplete]
        logger.info(f'Resuming {len(downloads)} incomplete downloads')
        self.start_downloads(downloads, progress_callback)

    def start_downloads(self, downloads: list, progress_callback: Optional[Callable]=None):
        """
        Start downloading a list of files.

        Args:
            downloads: List of (url, filepath) tuples
            progress_callback: Callback function for progress updates
        """
        self.current_downloads = downloads
        self.paused = False

        async def run_downloads():
            downloader = AsyncDownloader(self.max_concurrent, self.max_chunks, self.max_speed_limit, self.enable_resume, self.enable_chunked)
            self.active_downloader = downloader
            async with downloader:
                tasks = []
                for url, filepath in downloads:
                    task = asyncio.create_task(downloader.download_file(url, filepath, progress_callback))
                    tasks.append(task)
                try:
                    await asyncio.gather(*tasks, return_exceptions=True)
                except asyncio.CancelledError:
                    logger.info('Downloads were cancelled')
                    if self.paused and self.enable_resume and self.download_state:
                        for key, info in downloader.active_downloads.items():
                            filepath = info['filepath']
                            url = info['url']
                            if filepath and url:
                                downloaded_chunks = {}
                                for k, data in downloader.active_downloads.items():
                                    if k.startswith(f'{filepath}_'):
                                        chunk_id = data['chunk_id']
                                        downloaded_chunks[chunk_id] = data['bytes_downloaded']
                                if downloaded_chunks:
                                    self.download_state.save_state(filepath=filepath, url=url, downloaded_chunks=downloaded_chunks, total_size=info.get('total_size', 0), chunk_ranges=[])
                    for task in tasks:
                        if not task.done():
                            task.cancel()
                    await asyncio.gather(*tasks, return_exceptions=True)
            self.active_downloader = None

        def run_async_downloads():
            try:
                asyncio.run(run_downloads())
            except Exception as e:
                logger.error(f'Error in download thread: {str(e)}')
        self.future = self.executor.submit(run_async_downloads)

    def pause_downloads(self):
        """Pause all active downloads."""
        if self.active_downloader and (not self.paused):
            logger.info('Pausing downloads...')
            self.paused = True
            if self.future:
                self.future.cancel()
            return True
        return False

    def continue_downloads(self):
        """Continue paused downloads."""
        if self.paused and self.current_downloads:
            logger.info('Continuing downloads...')
            self.paused = False
            self.start_downloads(self.current_downloads)
            return True
        return False

    def stop_downloads(self):
        """Stop all active downloads."""
        if self.active_downloader:
            logger.info('Stopping downloads...')
            self.paused = False
            if self.future:
                self.future.cancel()
            self.current_downloads = []
            return True
        return False

    def get_active_downloads(self) -> Dict[str, Dict]:
        """
        Get information about active downloads including speed.

        Returns:
            Dictionary mapping filepath to download info including speed
        """
        result = {}
        if hasattr(self, 'active_downloader') and self.active_downloader:
            for key, info in self.active_downloader.active_downloads.items():
                filepath = info['filepath']
                speed = 0
                if hasattr(self.active_downloader, 'optimizer'):
                    speed = self.active_downloader.optimizer.get_download_speed(info['url']) or 0
                if filepath in result:
                    result[filepath]['bytes_downloaded'] += info['bytes_downloaded']
                    if speed > result[filepath].get('speed', 0):
                        result[filepath]['speed'] = speed
                else:
                    result[filepath] = {'url': info['url'], 'bytes_downloaded': info['bytes_downloaded'], 'total_size': info.get('total_size', 0), 'speed': speed, 'paused': self.paused}
        return result

    def is_paused(self) -> bool:
        """Check if downloads are currently paused."""
        return self.paused

    def has_active_downloads(self) -> bool:
        """Check if there are any active downloads."""
        return bool(self.active_downloader and self.active_downloader.active_downloads)

    def shutdown(self):
        """Shutdown the download manager and cancel any pending downloads."""
        self.stop_downloads()
        self.executor.shutdown(wait=False)

# === Code from download_optimizer.py ===
class DownloadOptimizer:

    def __init__(self, min_chunk_size: int=65536, max_chunk_size: int=4194304, max_speed_limit: Optional[int]=None):
        self.speed_history: Dict[str, list] = {}
        self.chunk_sizes: Dict[str, int] = {}
        self.last_download_time: Dict[str, float] = {}
        self.min_chunk_size = min_chunk_size
        self.max_chunk_size = max_chunk_size
        self.max_speed_limit = max_speed_limit
        self.rate_limit_tokens: Dict[str, float] = {}
        self.last_token_update: Dict[str, float] = {}
        self.backoff_factors: Dict[str, float] = {}

    def get_optimal_chunk_size(self, url: str) -> int:
        """Get optimal chunk size based on download history."""
        return self.chunk_sizes.get(url, self.min_chunk_size)

    def set_speed_limit(self, limit_bytes_per_sec: Optional[int]) -> None:
        """Set a global speed limit for all downloads in bytes per second."""
        self.max_speed_limit = limit_bytes_per_sec

    def update_speed(self, url: str, bytes_downloaded: int, duration: float) -> None:
        """Update download speed statistics."""
        if duration > 0:
            speed = bytes_downloaded / duration
            if url not in self.speed_history:
                self.speed_history[url] = []
            self.speed_history[url].append(speed)
            if len(self.speed_history[url]) > 5:
                self.speed_history[url].pop(0)
            avg_speed = sum(self.speed_history[url]) / len(self.speed_history[url])
            new_chunk_size = min(max(int(avg_speed / 4), self.min_chunk_size), self.max_chunk_size)
            self.chunk_sizes[url] = new_chunk_size
            if url in self.backoff_factors and self.backoff_factors[url] > 1.0:
                self.backoff_factors[url] = max(1.0, self.backoff_factors[url] * 0.9)

    def get_download_speed(self, url: str) -> Optional[float]:
        """Get current download speed in bytes per second."""
        if url in self.speed_history and self.speed_history[url]:
            return sum(self.speed_history[url]) / len(self.speed_history[url])
        return None

    async def apply_rate_limit(self, url: str, bytes_to_download: int) -> None:
        """Apply rate limiting to avoid server bans."""
        current_time = time.time()
        if url not in self.rate_limit_tokens:
            self.rate_limit_tokens[url] = bytes_to_download
            self.last_token_update[url] = current_time
            self.backoff_factors[url] = 1.0
            return
        time_elapsed = current_time - self.last_token_update[url]
        self.last_token_update[url] = current_time
        effective_limit = self.max_speed_limit
        if effective_limit is None:
            avg_speed = self.get_download_speed(url)
            if avg_speed:
                effective_limit = int(avg_speed * 1.2)
            else:
                effective_limit = 5 * 1024 * 1024
        effective_limit = effective_limit / self.backoff_factors.get(url, 1.0)
        tokens_to_add = time_elapsed * effective_limit
        self.rate_limit_tokens[url] = min(self.rate_limit_tokens[url] + tokens_to_add, self.max_chunk_size * 2)
        if bytes_to_download > self.rate_limit_tokens[url]:
            deficit = bytes_to_download - self.rate_limit_tokens[url]
            sleep_time = deficit / effective_limit
            sleep_time *= 1.0 + random.uniform(0, 0.1)
            await asyncio.sleep(sleep_time)
            self.rate_limit_tokens[url] = 0
        else:
            self.rate_limit_tokens[url] -= bytes_to_download

    def handle_server_error(self, url: str) -> None:
        """Handle server errors by increasing backoff factor."""
        if url not in self.backoff_factors:
            self.backoff_factors[url] = 1.0
        self.backoff_factors[url] = min(8.0, self.backoff_factors[url] * 1.5)

    def calculate_optimal_chunks(self, url: str, file_size: int, max_chunks: int=8) -> List[tuple]:
        """Calculate optimal chunk ranges for parallel downloading."""
        if file_size <= 0:
            return [(0, None)]
        if file_size < 1024 * 1024:
            num_chunks = 1
        elif file_size < 10 * 1024 * 1024:
            num_chunks = min(2, max_chunks)
        elif file_size < 100 * 1024 * 1024:
            num_chunks = min(4, max_chunks)
        else:
            num_chunks = max_chunks
        chunk_size = file_size // num_chunks
        chunks = []
        for i in range(num_chunks):
            start = i * chunk_size
            end = None if i == num_chunks - 1 else (i + 1) * chunk_size - 1
            chunks.append((start, end))
        return chunks
class ConnectionPool:

    def __init__(self, max_connections: int=10, max_per_host: int=4):
        self.semaphore = asyncio.Semaphore(max_connections)
        self.active_connections: Dict[str, int] = {}
        self.host_connections: Dict[str, int] = {}
        self.host_semaphores: Dict[str, asyncio.Semaphore] = {}
        self.max_per_host = max_per_host

    def _get_host(self, url: str) -> str:
        """Extract host from URL."""
        from urllib.parse import urlparse
        return urlparse(url).netloc

    async def acquire(self, url: str):
        """Acquire a connection from the pool with per-host limiting."""
        host = self._get_host(url)
        if host not in self.host_semaphores:
            self.host_semaphores[host] = asyncio.Semaphore(self.max_per_host)
        await self.semaphore.acquire()
        await self.host_semaphores[host].acquire()
        self.active_connections[url] = self.active_connections.get(url, 0) + 1
        self.host_connections[host] = self.host_connections.get(host, 0) + 1

    def release(self, url: str):
        """Release a connection back to the pool."""
        host = self._get_host(url)
        if url in self.active_connections:
            self.active_connections[url] -= 1
            if self.active_connections[url] <= 0:
                del self.active_connections[url]
        if host in self.host_connections:
            self.host_connections[host] -= 1
            if self.host_connections[host] <= 0:
                del self.host_connections[host]
        if host in self.host_semaphores:
            self.host_semaphores[host].release()
        self.semaphore.release()

    def get_active_connections(self, url: str) -> int:
        """Get number of active connections for a URL."""
        return self.active_connections.get(url, 0)

    def get_host_connections(self, url: str) -> int:
        """Get number of active connections for a host."""
        host = self._get_host(url)
        return self.host_connections.get(host, 0)

# === Code from download_state.py ===
class DownloadState:
    """Class to manage download state persistence for resumable downloads."""

    def __init__(self, state_dir: str='.download_state'):
        """
        Initialize the download state manager.
        
        Args:
            state_dir: Directory to store download state files
        """
        self.state_dir = state_dir
        os.makedirs(state_dir, exist_ok=True)

    def _get_state_path(self, filepath: str) -> str:
        """Get the path to the state file for a download."""
        safe_name = filepath.replace('/', '_').replace('\\', '_').replace(':', '_')
        return os.path.join(self.state_dir, f'{safe_name}.state')

    def save_state(self, filepath: str, url: str, downloaded_chunks: Dict[int, int], total_size: int, chunk_ranges: List[tuple]) -> None:
        """
        Save the download state for resuming later.
        
        Args:
            filepath: Path to the file being downloaded
            url: URL being downloaded
            downloaded_chunks: Dict mapping chunk_id to bytes downloaded
            total_size: Total size of the file
            chunk_ranges: List of (start, end) tuples for each chunk
        """
        state = {'filepath': filepath, 'url': url, 'downloaded_chunks': downloaded_chunks, 'total_size': total_size, 'chunk_ranges': chunk_ranges, 'timestamp': time.time()}
        state_path = self._get_state_path(filepath)
        with open(state_path, 'w') as f:
            json.dump(state, f)

    def load_state(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        Load the download state for a file.
        
        Args:
            filepath: Path to the file
            
        Returns:
            Download state dict or None if no state exists
        """
        state_path = self._get_state_path(filepath)
        if not os.path.exists(state_path):
            return None
        try:
            with open(state_path, 'r') as f:
                state = json.load(f)
            required_keys = ['filepath', 'url', 'downloaded_chunks', 'total_size', 'chunk_ranges']
            if all((key in state for key in required_keys)):
                return state
        except (json.JSONDecodeError, IOError) as e:
            print(f'Error loading download state: {e}')
        return None

    def clear_state(self, filepath: str) -> None:
        """
        Clear the download state for a file.
        
        Args:
            filepath: Path to the file
        """
        state_path = self._get_state_path(filepath)
        if os.path.exists(state_path):
            os.remove(state_path)

    def get_incomplete_downloads(self) -> List[Dict[str, Any]]:
        """
        Get a list of all incomplete downloads.
        
        Returns:
            List of download state dicts
        """
        incomplete = []
        if not os.path.exists(self.state_dir):
            return incomplete
        for filename in os.listdir(self.state_dir):
            if filename.endswith('.state'):
                try:
                    with open(os.path.join(self.state_dir, filename), 'r') as f:
                        state = json.load(f)
                        incomplete.append(state)
                except (json.JSONDecodeError, IOError):
                    continue
        return incomplete

# === Code from file_utils.py ===
def sanitize_filename(filename: str) -> str:
    """Remove invalid characters from filename."""
    filename = re.sub('[<>:"/\\\\|?*]', '', filename)
    filename = ''.join((char for char in filename if ord(char) >= 32))
    return filename.strip()
def get_extension_from_url(url: str) -> str:
    """Get file extension from URL or content type."""
    VIDEO_EXTENSIONS = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.m4v', '.ts'}
    parsed_url = urlparse(url)
    path = parsed_url.path
    ext = os.path.splitext(path)[1].lower()
    if ext in VIDEO_EXTENSIONS:
        return ext
    return '.mp4'
def ensure_unique_filename(base_path: str, filename: str) -> str:
    """Ensure filename is unique by adding number if necessary."""
    name, ext = os.path.splitext(filename)
    counter = 1
    final_path = os.path.join(base_path, filename)
    while os.path.exists(final_path):
        new_filename = f'{name}_{counter}{ext}'
        final_path = os.path.join(base_path, new_filename)
        counter += 1
    return final_path

# === Code from iptv_auth.py ===
class IPTVAuthenticator:

    def __init__(self):
        self.session = None

    async def authenticate(self, url: str) -> str:
        """Authenticate and get fresh token for IPTV stream"""
        parsed = urlparse(url)
        params = parse_qs(parsed.query)
        mac = params.get('mac', [''])[0]
        stream_id = params.get('stream', [''])[0]
        content_type = params.get('type', [''])[0]
        if not all([mac, stream_id, content_type]):
            return url
        auth_url = f'{parsed.scheme}://{parsed.netloc}/player_api.php'
        auth_params = {'username': mac, 'password': mac, 'action': 'get_link', 'stream_id': stream_id, 'type': content_type}
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            async with self.session.post(f'{auth_url}?{urlencode(auth_params)}') as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, dict) and 'token' in data:
                        params['play_token'] = [data['token']]
                        return parsed._replace(query=urlencode(params, doseq=True)).geturl()
        except Exception as e:
            print(f'Authentication error: {str(e)}')
        return url

    async def close(self):
        if self.session:
            await self.session.close()

# === Code from m3u_parser.py ===
class M3UEntry:

    def __init__(self, title: str, url: str, filename: str):
        self.title = title
        self.url = url
        self.filename = filename
class M3UParser:

    @staticmethod
    def parse(file_path: str) -> List[M3UEntry]:
        entries = []
        current_title = None
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('#EXTINF:'):
                        parts = line.split(',', 1)
                        if len(parts) > 1:
                            current_title = parts[1]
                    elif line and (not line.startswith('#')):
                        title = current_title or f'Video_{len(entries) + 1}'
                        filename = sanitize_filename(title) + get_extension_from_url(line)
                        entries.append(M3UEntry(title, line, filename))
                        current_title = None
        except Exception as e:
            raise Exception(f'Failed to parse M3U file: {str(e)}')
        return entries

# === Main code from main.py ===
if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = M3UDownloaderGUI()
    window.show()
    sys.exit(app.exec())