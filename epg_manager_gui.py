#!/usr/bin/env python3
"""
EPG Manager GUI Module
PyQt6 interface for EPG management and configuration
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTextEdit, QProgressBar,
                            QGroupBox, QGridLayout, QSlider, QCheckBox,
                            QTabWidget, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QFileDialog, QSpinBox,
                            QComboBox, QFrame, QScrollArea, QStackedWidget)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPalette
import json
import os
from datetime import datetime, timedelta
from epg_manager import EPGManager, ChannelMatch
class EPGUpdateThread(QThread):
    """Thread for EPG update operations"""

    progress_signal = pyqtSignal(str, int)  # message, progress
    finished_signal = pyqtSignal(bool, str)  # success, message

    def __init__(self, epg_manager: EPGManager):
        super().__init__()
        self.epg_manager = epg_manager

    def run(self):
        try:
            success = self.epg_manager.download_external_epg(
                progress_callback=self.progress_signal.emit
            )

            if success:
                self.finished_signal.emit(True, "EPG updated successfully!")
            else:
                self.finished_signal.emit(False, "EPG update failed!")

        except Exception as e:
            self.finished_signal.emit(False, f"EPG update error: {str(e)}")

class EPGManagerWidget(QWidget):
    """Main widget for EPG management"""

    def __init__(self):
        super().__init__()
        self.epg_manager = EPGManager()
        self.update_thread = None
        self.auto_update_timer = QTimer()
        self.auto_update_timer.timeout.connect(self.auto_update_epg)
        self.init_ui()
        self.load_current_settings()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()

        # Title
        title = QLabel("📺 EPG Manager")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Main tabs
        self.tabs = QTabWidget()

        # Configuration tab
        self.config_tab = QWidget()
        self.setup_config_tab()
        self.tabs.addTab(self.config_tab, "⚙️ Config")

        # Channel Matching tab
        self.matching_tab = QWidget()
        self.setup_matching_tab()
        self.tabs.addTab(self.matching_tab, "🔍 Matching")

        # Statistics tab
        self.stats_tab = QWidget()
        self.setup_stats_tab()
        self.tabs.addTab(self.stats_tab, "📊 Stats")

        # Program Guide tab
        self.guide_tab = QWidget()
        self.setup_guide_tab()
        self.tabs.addTab(self.guide_tab, "📋 Guide")

        layout.addWidget(self.tabs)

        self.setLayout(layout)

    def setup_config_tab(self):
        """Setup the configuration tab"""
        layout = QVBoxLayout()

        # EPG Source Selection
        source_group = QGroupBox("📡 EPG Source Selection")
        source_layout = QVBoxLayout()

        # Source type selection
        source_type_layout = QHBoxLayout()
        source_type_layout.addWidget(QLabel("EPG Source:"))

        self.epg_source_combo = QComboBox()
        self.epg_source_combo.addItems([
            "🌐 External EPG URL (XML/XMLTV)",
            "📺 Stalker Portal Server",
            "🌐 Xtream API Server"
        ])
        self.epg_source_combo.currentIndexChanged.connect(self.on_epg_source_changed)
        source_type_layout.addWidget(self.epg_source_combo)
        source_layout.addLayout(source_type_layout)

        # Stacked widget for different source configurations
        self.epg_config_stack = QStackedWidget()

        # External EPG configuration
        external_widget = QWidget()
        external_layout = QGridLayout()

        external_layout.addWidget(QLabel("EPG URL:"), 0, 0)
        self.epg_url_input = QLineEdit()
        self.epg_url_input.setPlaceholderText("http://example.com/epg.xml or http://example.com/epg.xml.gz")
        self.epg_url_input.textChanged.connect(self.on_epg_url_changed)
        external_layout.addWidget(self.epg_url_input, 0, 1, 1, 2)

        self.validate_url_button = QPushButton("✓ Validate URL")
        self.validate_url_button.clicked.connect(self.validate_epg_url)
        external_layout.addWidget(self.validate_url_button, 0, 3)

        # URL validation status
        self.url_status_label = QLabel("Enter EPG URL to validate")
        self.url_status_label.setStyleSheet("color: gray; font-style: italic;")
        external_layout.addWidget(self.url_status_label, 1, 1, 1, 3)

        # Help text for external EPG
        help_text = QLabel("💡 External EPG sources:\n• XMLTV format files (.xml or .xml.gz)\n• Public EPG providers\n• Custom EPG URLs")
        help_text.setStyleSheet("color: #666; font-size: 11px; padding: 5px; background-color: #f0f0f0; border-radius: 4px;")
        external_layout.addWidget(help_text, 2, 0, 1, 4)

        external_widget.setLayout(external_layout)
        self.epg_config_stack.addWidget(external_widget)

        # Stalker Portal configuration
        stalker_widget = QWidget()
        stalker_layout = QGridLayout()

        stalker_layout.addWidget(QLabel("Portal URL:"), 0, 0)
        self.stalker_url_input = QLineEdit()
        self.stalker_url_input.setPlaceholderText("http://example.com:8080/stalker_portal")
        stalker_layout.addWidget(self.stalker_url_input, 0, 1, 1, 2)

        stalker_layout.addWidget(QLabel("MAC Address:"), 1, 0)
        self.stalker_mac_input = QLineEdit()
        self.stalker_mac_input.setPlaceholderText("00:1A:79:XX:XX:XX")
        stalker_layout.addWidget(self.stalker_mac_input, 1, 1, 1, 2)

        self.test_stalker_button = QPushButton("🔍 Test Connection")
        self.test_stalker_button.clicked.connect(self.test_stalker_connection)
        stalker_layout.addWidget(self.test_stalker_button, 0, 3)

        self.download_stalker_epg_button = QPushButton("📥 Download EPG")
        self.download_stalker_epg_button.clicked.connect(self.download_stalker_epg)
        stalker_layout.addWidget(self.download_stalker_epg_button, 1, 3)

        # Stalker status
        self.stalker_status_label = QLabel("Enter Stalker portal details")
        self.stalker_status_label.setStyleSheet("color: gray; font-style: italic;")
        stalker_layout.addWidget(self.stalker_status_label, 2, 1, 1, 3)

        # Help text for Stalker
        stalker_help = QLabel("💡 Stalker Portal EPG:\n• Uses portal's built-in EPG data\n• Automatically matches channels\n• Requires valid MAC address")
        stalker_help.setStyleSheet("color: #666; font-size: 11px; padding: 5px; background-color: #f0f0f0; border-radius: 4px;")
        stalker_layout.addWidget(stalker_help, 3, 0, 1, 4)

        stalker_widget.setLayout(stalker_layout)
        self.epg_config_stack.addWidget(stalker_widget)

        # Xtream API configuration
        xtream_widget = QWidget()
        xtream_layout = QGridLayout()

        xtream_layout.addWidget(QLabel("Server URL:"), 0, 0)
        self.xtream_url_input = QLineEdit()
        self.xtream_url_input.setPlaceholderText("http://example.com:8080")
        xtream_layout.addWidget(self.xtream_url_input, 0, 1, 1, 2)

        xtream_layout.addWidget(QLabel("Username:"), 1, 0)
        self.xtream_username_input = QLineEdit()
        self.xtream_username_input.setPlaceholderText("username")
        xtream_layout.addWidget(self.xtream_username_input, 1, 1)

        xtream_layout.addWidget(QLabel("Password:"), 1, 2)
        self.xtream_password_input = QLineEdit()
        self.xtream_password_input.setPlaceholderText("password")
        self.xtream_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        xtream_layout.addWidget(self.xtream_password_input, 1, 3)

        self.test_xtream_button = QPushButton("🔍 Test Connection")
        self.test_xtream_button.clicked.connect(self.test_xtream_connection)
        xtream_layout.addWidget(self.test_xtream_button, 0, 3)

        self.download_xtream_epg_button = QPushButton("📥 Download EPG")
        self.download_xtream_epg_button.clicked.connect(self.download_xtream_epg)
        xtream_layout.addWidget(self.download_xtream_epg_button, 2, 3)

        # Xtream status
        self.xtream_status_label = QLabel("Enter Xtream API credentials")
        self.xtream_status_label.setStyleSheet("color: gray; font-style: italic;")
        xtream_layout.addWidget(self.xtream_status_label, 2, 1, 1, 2)

        # Help text for Xtream
        xtream_help = QLabel("💡 Xtream API EPG:\n• Downloads EPG from Xtream server\n• Includes channel and program data\n• Requires valid credentials")
        xtream_help.setStyleSheet("color: #666; font-size: 11px; padding: 5px; background-color: #f0f0f0; border-radius: 4px;")
        xtream_layout.addWidget(xtream_help, 3, 0, 1, 4)

        xtream_widget.setLayout(xtream_layout)
        self.epg_config_stack.addWidget(xtream_widget)

        source_layout.addWidget(self.epg_config_stack)
        source_group.setLayout(source_layout)
        layout.addWidget(source_group)

        # Confidence settings
        confidence_group = QGroupBox("🎯 Matching Configuration")
        confidence_layout = QGridLayout()

        confidence_layout.addWidget(QLabel("Confidence Threshold:"), 0, 0)
        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(0, 100)
        self.confidence_slider.setValue(70)
        self.confidence_slider.valueChanged.connect(self.on_confidence_changed)
        confidence_layout.addWidget(self.confidence_slider, 0, 1)

        self.confidence_label = QLabel("70%")
        self.confidence_label.setMinimumWidth(50)
        confidence_layout.addWidget(self.confidence_label, 0, 2)

        # Confidence explanation
        self.confidence_explanation = QLabel("70%: Balanced matching - good accuracy with reasonable coverage")
        self.confidence_explanation.setStyleSheet("color: #666; font-size: 11px;")
        confidence_layout.addWidget(self.confidence_explanation, 1, 0, 1, 3)

        # Prefer internal EPG
        self.prefer_internal_check = QCheckBox("Prefer IPTV Provider EPG First")
        self.prefer_internal_check.setChecked(True)
        self.prefer_internal_check.toggled.connect(self.on_prefer_internal_changed)
        confidence_layout.addWidget(self.prefer_internal_check, 2, 0, 1, 3)

        prefer_explanation = QLabel("When enabled, searches IPTV provider EPG first, then external EPG if not found")
        prefer_explanation.setStyleSheet("color: #666; font-size: 11px;")
        confidence_layout.addWidget(prefer_explanation, 3, 0, 1, 3)

        confidence_group.setLayout(confidence_layout)
        layout.addWidget(confidence_group)

        # Update controls
        update_group = QGroupBox("🔄 EPG Updates")
        update_layout = QGridLayout()

        self.update_button = QPushButton("📥 Update EPG Now")
        self.update_button.clicked.connect(self.update_epg)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        update_layout.addWidget(self.update_button, 0, 0)

        self.auto_update_check = QCheckBox("Auto-update every")
        self.auto_update_check.toggled.connect(self.on_auto_update_toggled)
        update_layout.addWidget(self.auto_update_check, 0, 1)

        self.auto_update_hours = QSpinBox()
        self.auto_update_hours.setRange(1, 24)
        self.auto_update_hours.setValue(6)
        self.auto_update_hours.setSuffix(" hours")
        update_layout.addWidget(self.auto_update_hours, 0, 2)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        update_layout.addWidget(self.progress_bar, 1, 0, 1, 3)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        update_layout.addWidget(self.status_label, 2, 0, 1, 3)

        update_group.setLayout(update_layout)
        layout.addWidget(update_group)

        # Import/Export section
        io_group = QGroupBox("💾 Import/Export")
        io_layout = QHBoxLayout()

        self.export_button = QPushButton("📤 Export Settings")
        self.export_button.clicked.connect(self.export_settings)
        io_layout.addWidget(self.export_button)

        self.import_button = QPushButton("📥 Import Settings")
        self.import_button.clicked.connect(self.import_settings)
        io_layout.addWidget(self.import_button)

        self.clear_cache_button = QPushButton("🗑️ Clear Cache")
        self.clear_cache_button.clicked.connect(self.clear_cache)
        io_layout.addWidget(self.clear_cache_button)

        io_group.setLayout(io_layout)
        layout.addWidget(io_group)

        self.config_tab.setLayout(layout)

    def setup_matching_tab(self):
        """Setup the channel matching test tab"""
        layout = QVBoxLayout()

        # Test section
        test_group = QGroupBox("🧪 Test Channel Matching")
        test_layout = QGridLayout()

        test_layout.addWidget(QLabel("Channel Name:"), 0, 0)
        self.test_channel_input = QLineEdit()
        self.test_channel_input.setPlaceholderText("Enter channel name to test matching...")
        test_layout.addWidget(self.test_channel_input, 0, 1)

        self.test_match_button = QPushButton("🔍 Find Match")
        self.test_match_button.clicked.connect(self.test_channel_matching)
        test_layout.addWidget(self.test_match_button, 0, 2)

        test_group.setLayout(test_layout)
        layout.addWidget(test_group)

        # Results section
        results_group = QGroupBox("📋 Matching Results")
        results_layout = QVBoxLayout()

        self.match_results_text = QTextEdit()
        self.match_results_text.setMaximumHeight(200)
        self.match_results_text.setPlaceholderText("Channel matching results will appear here...")
        results_layout.addWidget(self.match_results_text)

        results_group.setLayout(results_layout)
        layout.addWidget(results_group)

        # Channel list
        channels_group = QGroupBox("📺 Available EPG Channels")
        channels_layout = QVBoxLayout()

        self.channels_table = QTableWidget()
        self.channels_table.setColumnCount(3)
        self.channels_table.setHorizontalHeaderLabels(["Channel ID", "Display Name", "Programs"])

        # Set column widths
        header = self.channels_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)

        channels_layout.addWidget(self.channels_table)

        # Refresh button
        self.refresh_channels_button = QPushButton("🔄 Refresh")
        self.refresh_channels_button.clicked.connect(self.refresh_channels_list)
        channels_layout.addWidget(self.refresh_channels_button)

        channels_group.setLayout(channels_layout)
        layout.addWidget(channels_group)

        self.matching_tab.setLayout(layout)

    def setup_stats_tab(self):
        """Setup the statistics tab"""
        layout = QVBoxLayout()

        # Statistics display
        stats_group = QGroupBox("📊 Statistics")
        self.stats_layout = QGridLayout()
        stats_group.setLayout(self.stats_layout)
        layout.addWidget(stats_group)

        # Refresh button
        self.refresh_stats_button = QPushButton("🔄 Refresh")
        self.refresh_stats_button.clicked.connect(self.refresh_statistics)
        layout.addWidget(self.refresh_stats_button)

        self.stats_tab.setLayout(layout)

    def setup_guide_tab(self):
        """Setup the program guide tab"""
        layout = QVBoxLayout()

        # Channel selection
        selection_group = QGroupBox("📺 Channel Selection")
        selection_layout = QHBoxLayout()

        selection_layout.addWidget(QLabel("Channel:"))
        self.guide_channel_input = QLineEdit()
        self.guide_channel_input.setPlaceholderText("Enter channel name...")
        selection_layout.addWidget(self.guide_channel_input)

        self.show_guide_button = QPushButton("📋 Show")
        self.show_guide_button.clicked.connect(self.show_program_guide)
        selection_layout.addWidget(self.show_guide_button)

        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)

        # Program guide table
        guide_group = QGroupBox("📅 Programs")
        guide_layout = QVBoxLayout()

        self.guide_table = QTableWidget()
        self.guide_table.setColumnCount(4)
        self.guide_table.setHorizontalHeaderLabels(["Time", "Title", "Description", "Category"])

        # Set column widths
        guide_header = self.guide_table.horizontalHeader()
        guide_header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        guide_header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        guide_header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        guide_header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        guide_layout.addWidget(self.guide_table)

        guide_group.setLayout(guide_layout)
        layout.addWidget(guide_group)

        self.guide_tab.setLayout(layout)

    def load_current_settings(self):
        """Load current EPG settings into UI"""
        stats = self.epg_manager.get_statistics()

        self.epg_url_input.setText(stats.get('external_epg_url', ''))
        self.confidence_slider.setValue(stats.get('confidence_threshold', 70))
        self.prefer_internal_check.setChecked(stats.get('prefer_internal', True))

        self.refresh_statistics()
        self.refresh_channels_list()

    def on_epg_url_changed(self):
        """Handle EPG URL change"""
        url = self.epg_url_input.text().strip()

        if not url:
            self.url_status_label.setText("Enter EPG URL to validate")
            self.url_status_label.setStyleSheet("color: gray; font-style: italic;")
            return

        # Basic validation
        if self.epg_manager.set_external_epg_url(url):
            self.url_status_label.setText("✓ URL format is valid")
            self.url_status_label.setStyleSheet("color: green;")
        else:
            self.url_status_label.setText("❌ Invalid URL format (must end with .xml or .xml.gz)")
            self.url_status_label.setStyleSheet("color: red;")

    def validate_epg_url(self):
        """Validate EPG URL by attempting to download"""
        url = self.epg_url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "Warning", "Please enter an EPG URL first")
            return

        try:
            import requests
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                self.url_status_label.setText("✓ URL is accessible")
                self.url_status_label.setStyleSheet("color: green;")
                QMessageBox.information(self, "Success", "EPG URL is valid and accessible!")
            else:
                self.url_status_label.setText(f"❌ HTTP {response.status_code}")
                self.url_status_label.setStyleSheet("color: red;")
                QMessageBox.warning(self, "Warning", f"URL returned HTTP {response.status_code}")
        except Exception as e:
            self.url_status_label.setText("❌ URL not accessible")
            self.url_status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "Error", f"Failed to access URL:\n{str(e)}")

    def on_confidence_changed(self, value):
        """Handle confidence threshold change"""
        self.confidence_label.setText(f"{value}%")
        self.epg_manager.set_confidence_threshold(value)

        # Update explanation
        if value == 0:
            explanation = "0%: Only exact matches - very strict but often no results"
        elif value <= 30:
            explanation = f"{value}%: Very strict matching - high accuracy, low coverage"
        elif value <= 50:
            explanation = f"{value}%: Strict matching - good accuracy, moderate coverage"
        elif value <= 70:
            explanation = f"{value}%: Balanced matching - good accuracy with reasonable coverage"
        elif value <= 90:
            explanation = f"{value}%: Loose matching - moderate accuracy, high coverage"
        else:
            explanation = "100%: Very loose matching - always finds a match but may be inaccurate"

        self.confidence_explanation.setText(explanation)

    def on_prefer_internal_changed(self, checked):
        """Handle prefer internal EPG change"""
        self.epg_manager.set_prefer_internal(checked)

    def on_auto_update_toggled(self, checked):
        """Handle auto-update toggle"""
        if checked:
            hours = self.auto_update_hours.value()
            self.auto_update_timer.start(hours * 60 * 60 * 1000)  # Convert to milliseconds
            self.status_label.setText(f"Auto-update enabled (every {hours} hours)")
        else:
            self.auto_update_timer.stop()
            self.status_label.setText("Auto-update disabled")

    def update_epg(self):
        """Start EPG update"""
        if not self.epg_manager.external_epg_url:
            QMessageBox.warning(self, "Warning", "Please set an external EPG URL first")
            return

        if self.update_thread and self.update_thread.isRunning():
            QMessageBox.information(self, "Info", "EPG update is already in progress")
            return

        # Start update
        self.update_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        self.update_thread = EPGUpdateThread(self.epg_manager)
        self.update_thread.progress_signal.connect(self.on_update_progress)
        self.update_thread.finished_signal.connect(self.on_update_finished)
        self.update_thread.start()

    def auto_update_epg(self):
        """Automatic EPG update"""
        if not self.update_thread or not self.update_thread.isRunning():
            self.update_epg()

    def on_update_progress(self, message, progress):
        """Handle update progress"""
        self.status_label.setText(message)
        self.progress_bar.setValue(progress)

    def on_update_finished(self, success, message):
        """Handle update completion"""
        self.update_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: green;")
            self.refresh_statistics()
            self.refresh_channels_list()
            QMessageBox.information(self, "Success", message)
        else:
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "Error", message)

    def test_channel_matching(self):
        """Test channel matching"""
        channel_name = self.test_channel_input.text().strip()
        if not channel_name:
            QMessageBox.warning(self, "Warning", "Please enter a channel name to test")
            return

        match = self.epg_manager.find_channel_match(channel_name)

        if match:
            result_text = f"✅ MATCH FOUND:\n"
            result_text += f"{'='*50}\n"
            result_text += f"Input Channel: {channel_name}\n"
            result_text += f"Matched EPG Channel: {match.epg_channel.display_name}\n"
            result_text += f"EPG Channel ID: {match.epg_channel.id}\n"
            result_text += f"Confidence: {match.confidence:.1f}%\n"
            result_text += f"Match Reason: {match.match_reason}\n"
            result_text += f"Programs Available: {len(match.epg_channel.programs)}\n"

            # Show current program if available
            current_program = self.epg_manager.get_current_program(channel_name)
            if current_program:
                result_text += f"\n📺 CURRENT PROGRAM:\n"
                result_text += f"Title: {current_program.title}\n"
                result_text += f"Time: {current_program.start.strftime('%H:%M')} - {current_program.stop.strftime('%H:%M')}\n"
                if current_program.description:
                    result_text += f"Description: {current_program.description[:100]}...\n"
            else:
                result_text += f"\n📺 No current program information available\n"

        else:
            result_text = f"❌ NO MATCH FOUND:\n"
            result_text += f"{'='*50}\n"
            result_text += f"Input Channel: {channel_name}\n"
            result_text += f"No EPG channel found with confidence >= {self.epg_manager.confidence_threshold}%\n"
            result_text += f"\n💡 SUGGESTIONS:\n"
            result_text += f"• Lower the confidence threshold\n"
            result_text += f"• Check if the channel name is correct\n"
            result_text += f"• Verify the EPG contains this channel\n"
            result_text += f"• Try a simpler channel name (remove HD, FHD, etc.)\n"

        self.match_results_text.setPlainText(result_text)

    def refresh_channels_list(self):
        """Refresh the channels list"""
        channels = self.epg_manager.epg_channels
        self.channels_table.setRowCount(len(channels))

        for row, (channel_id, channel) in enumerate(channels.items()):
            self.channels_table.setItem(row, 0, QTableWidgetItem(channel_id))
            self.channels_table.setItem(row, 1, QTableWidgetItem(channel.display_name))
            self.channels_table.setItem(row, 2, QTableWidgetItem(str(len(channel.programs))))

    def refresh_statistics(self):
        """Refresh EPG statistics"""
        stats = self.epg_manager.get_statistics()

        # Clear existing stats
        for i in reversed(range(self.stats_layout.count())):
            self.stats_layout.itemAt(i).widget().setParent(None)

        # Add statistics
        stat_items = [
            ("Total Channels", stats.get('total_channels', 0)),
            ("Total Programs", stats.get('total_programs', 0)),
            ("Current Programs", stats.get('current_programs', 0)),
            ("Future Programs", stats.get('future_programs', 0)),
            ("Last Update", stats.get('last_update', 'Never')),
            ("External EPG URL", stats.get('external_epg_url', 'Not set')),
            ("Confidence Threshold", f"{stats.get('confidence_threshold', 70)}%"),
            ("Prefer Internal EPG", "Yes" if stats.get('prefer_internal', True) else "No")
        ]

        for i, (label, value) in enumerate(stat_items):
            self.stats_layout.addWidget(QLabel(f"{label}:"), i // 2, (i % 2) * 2)
            value_label = QLabel(str(value))
            value_label.setStyleSheet("font-weight: bold; color: #2196F3;")
            self.stats_layout.addWidget(value_label, i // 2, (i % 2) * 2 + 1)

    def show_program_guide(self):
        """Show program guide for selected channel"""
        channel_name = self.guide_channel_input.text().strip()
        if not channel_name:
            QMessageBox.warning(self, "Warning", "Please enter a channel name")
            return

        programs = self.epg_manager.get_programs_for_channel(channel_name, hours=24)

        if not programs:
            QMessageBox.information(self, "Info", f"No program information found for '{channel_name}'")
            return

        self.guide_table.setRowCount(len(programs))

        for row, program in enumerate(programs):
            # Time
            time_str = f"{program.start.strftime('%H:%M')} - {program.stop.strftime('%H:%M')}"
            self.guide_table.setItem(row, 0, QTableWidgetItem(time_str))

            # Title
            self.guide_table.setItem(row, 1, QTableWidgetItem(program.title))

            # Description
            description = program.description[:100] + "..." if len(program.description) > 100 else program.description
            self.guide_table.setItem(row, 2, QTableWidgetItem(description))

            # Category
            self.guide_table.setItem(row, 3, QTableWidgetItem(program.category))

            # Highlight current program
            now = datetime.now()
            if program.start <= now <= program.stop:
                for col in range(4):
                    item = self.guide_table.item(row, col)
                    if item:
                        item.setBackground(QColor(255, 255, 0, 100))  # Light yellow

    def export_settings(self):
        """Export EPG settings"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export EPG Settings",
            f"epg_settings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            try:
                settings = {
                    'external_epg_url': self.epg_manager.external_epg_url,
                    'confidence_threshold': self.epg_manager.confidence_threshold,
                    'prefer_internal': self.epg_manager.prefer_internal,
                    'auto_update_enabled': self.auto_update_check.isChecked(),
                    'auto_update_hours': self.auto_update_hours.value(),
                    'export_timestamp': datetime.now().isoformat()
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "Success", f"Settings exported to:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export settings:\n{str(e)}")

    def import_settings(self):
        """Import EPG settings"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import EPG Settings",
            "",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # Apply settings
                if 'external_epg_url' in settings:
                    self.epg_url_input.setText(settings['external_epg_url'])
                    self.epg_manager.set_external_epg_url(settings['external_epg_url'])

                if 'confidence_threshold' in settings:
                    self.confidence_slider.setValue(settings['confidence_threshold'])

                if 'prefer_internal' in settings:
                    self.prefer_internal_check.setChecked(settings['prefer_internal'])

                if 'auto_update_enabled' in settings:
                    self.auto_update_check.setChecked(settings['auto_update_enabled'])

                if 'auto_update_hours' in settings:
                    self.auto_update_hours.setValue(settings['auto_update_hours'])

                QMessageBox.information(self, "Success", "Settings imported successfully!")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to import settings:\n{str(e)}")

    def clear_cache(self):
        """Clear EPG cache"""
        reply = QMessageBox.question(
            self,
            "Confirm",
            "Are you sure you want to clear the EPG cache?\nThis will remove all downloaded EPG data.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                import shutil
                if os.path.exists(self.epg_manager.cache_dir):
                    shutil.rmtree(self.epg_manager.cache_dir)
                    os.makedirs(self.epg_manager.cache_dir, exist_ok=True)

                # Reset manager
                self.epg_manager = EPGManager()
                self.refresh_statistics()
                self.refresh_channels_list()

                QMessageBox.information(self, "Success", "EPG cache cleared successfully!")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to clear cache:\n{str(e)}")

    def on_epg_source_changed(self, index):
        """Handle EPG source selection change"""
        self.epg_config_stack.setCurrentIndex(index)

        # Update status based on selected source
        if index == 0:  # External EPG
            self.status_label.setText("External EPG source selected")
        elif index == 1:  # Stalker Portal
            self.status_label.setText("Stalker Portal EPG source selected")
        elif index == 2:  # Xtream API
            self.status_label.setText("Xtream API EPG source selected")

    def test_stalker_connection(self):
        """Test Stalker portal connection"""
        portal_url = self.stalker_url_input.text().strip()
        mac_address = self.stalker_mac_input.text().strip()

        if not portal_url or not mac_address:
            QMessageBox.warning(self, "Warning", "Please enter both Portal URL and MAC address")
            return

        try:
            import requests
            import json

            # Test connection to Stalker portal
            base_url = portal_url.replace('/stalker_portal', '')
            headers = {
                'User-Agent': 'Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3',
                'X-User-Agent': 'Model: MAG250; Link: WiFi',
                'Cookie': f'mac={mac_address}; stb_lang=en; timezone=Europe/London'
            }

            # Test handshake
            handshake_url = f'{base_url}/portal.php?type=stb&action=handshake&JsHttpRequest=1-xml'
            response = requests.get(handshake_url, headers=headers, timeout=10)

            if response.status_code == 200:
                self.stalker_status_label.setText("✅ Connection successful!")
                self.stalker_status_label.setStyleSheet("color: green;")
                QMessageBox.information(self, "Success", "Stalker portal connection successful!")
            else:
                self.stalker_status_label.setText(f"❌ HTTP {response.status_code}")
                self.stalker_status_label.setStyleSheet("color: red;")
                QMessageBox.warning(self, "Warning", f"Connection failed with HTTP {response.status_code}")

        except Exception as e:
            self.stalker_status_label.setText("❌ Connection failed")
            self.stalker_status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "Error", f"Failed to connect to Stalker portal:\n{str(e)}")

    def test_xtream_connection(self):
        """Test Xtream API connection"""
        server_url = self.xtream_url_input.text().strip()
        username = self.xtream_username_input.text().strip()
        password = self.xtream_password_input.text().strip()

        if not server_url or not username or not password:
            QMessageBox.warning(self, "Warning", "Please enter Server URL, Username, and Password")
            return

        try:
            import requests

            # Test connection to Xtream API
            auth_url = f"{server_url}/player_api.php?username={username}&password={password}"
            response = requests.get(auth_url, timeout=10)

            if response.status_code == 200:
                user_info = response.json()
                if user_info.get('user_info', {}).get('status') == 'Active':
                    self.xtream_status_label.setText("✅ Connection successful!")
                    self.xtream_status_label.setStyleSheet("color: green;")
                    QMessageBox.information(self, "Success", "Xtream API connection successful!")
                else:
                    self.xtream_status_label.setText("❌ Account not active")
                    self.xtream_status_label.setStyleSheet("color: red;")
                    QMessageBox.warning(self, "Warning", "Account is not active")
            else:
                self.xtream_status_label.setText(f"❌ HTTP {response.status_code}")
                self.xtream_status_label.setStyleSheet("color: red;")
                QMessageBox.warning(self, "Warning", f"Connection failed with HTTP {response.status_code}")

        except Exception as e:
            self.xtream_status_label.setText("❌ Connection failed")
            self.xtream_status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "Error", f"Failed to connect to Xtream API:\n{str(e)}")

    def download_stalker_epg(self):
        """Download EPG from Stalker portal"""
        portal_url = self.stalker_url_input.text().strip()
        mac_address = self.stalker_mac_input.text().strip()

        if not portal_url or not mac_address:
            QMessageBox.warning(self, "Warning", "Please enter both Portal URL and MAC address")
            return

        QMessageBox.information(self, "Info",
                               "Stalker EPG download functionality will be implemented.\n"
                               "This will fetch EPG data directly from the Stalker portal\n"
                               "and integrate it with the channel matching system.")

    def download_xtream_epg(self):
        """Download EPG from Xtream API"""
        server_url = self.xtream_url_input.text().strip()
        username = self.xtream_username_input.text().strip()
        password = self.xtream_password_input.text().strip()

        if not server_url or not username or not password:
            QMessageBox.warning(self, "Warning", "Please enter Server URL, Username, and Password")
            return

        try:
            import requests

            # Get EPG URL from Xtream API
            epg_url = f"{server_url}/xmltv.php?username={username}&password={password}"

            # Test if EPG is available
            response = requests.head(epg_url, timeout=10)
            if response.status_code == 200:
                # Set the EPG URL and download
                self.epg_manager.set_external_epg_url(epg_url)
                self.epg_url_input.setText(epg_url)
                self.epg_source_combo.setCurrentIndex(0)  # Switch to external EPG
                self.update_epg()
            else:
                QMessageBox.warning(self, "Warning", f"EPG not available from this Xtream server (HTTP {response.status_code})")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to download Xtream EPG:\n{str(e)}")
