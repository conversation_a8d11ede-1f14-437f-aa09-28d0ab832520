#!/usr/bin/env python3
"""
Player Module for Stalker and Xtream Content
Integrated player for browsing and playing IPTV content
"""

import sys
import logging
import subprocess
import platform
import os
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, QProcess, QEvent
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QListWidget, QListWidgetItem, QTabWidget, QProgressBar, QMessageBox,
    QSplitter, QTextEdit, QComboBox, QFrame, QGroupBox, QSlider, QMenu,
    QApplication
)
from PyQt6.QtGui import QFont, QIcon
from PyQt6.QtCore import Qt
import requests
from stalker import StalkerPortal
from styles import Colors

# Download integration imports
try:
    from download_integration_gui import DownloadButtonWidget
    from download_manager_gui import DownloadManagerWidget
    DOWNLOAD_AVAILABLE = True
except ImportError:
    DOWNLOAD_AVAILABLE = False
    class DownloadButtonWidget:
        """Dummy class when download manager is not available"""
        pass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VLCPlayer:
    """VLC Media Player integration"""

    def __init__(self):
        self.vlc_process = None
        self.current_url = None

    def find_vlc_path(self):
        """Find VLC executable path"""
        possible_paths = [
            r"C:\Program Files\VideoLAN\VLC\vlc.exe",
            r"C:\Program Files (x86)\VideoLAN\VLC\vlc.exe",
            "/usr/bin/vlc",
            "/Applications/VLC.app/Contents/MacOS/VLC",
            "vlc"  # If in PATH
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        # Try to find in PATH
        try:
            subprocess.run(["vlc", "--version"], capture_output=True, check=True)
            return "vlc"
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass

        return None

    def play(self, url):
        """Play URL in VLC"""
        vlc_path = self.find_vlc_path()
        if not vlc_path:
            raise Exception("VLC Media Player not found. Please install VLC.")

        # Stop current playback if any
        self.stop()

        try:
            # Start VLC with the URL
            self.vlc_process = subprocess.Popen([
                vlc_path,
                url,
                "--intf", "dummy",  # No interface
                "--extraintf", "http",  # HTTP interface for control
                "--http-password", "vlc"
            ])
            self.current_url = url
            logger.info(f"Started VLC playback: {url}")
            return True
        except Exception as e:
            logger.error(f"Failed to start VLC: {e}")
            return False

    def stop(self):
        """Stop VLC playback (non-blocking)"""
        if self.vlc_process:
            try:
                # Terminate the process
                self.vlc_process.terminate()

                # Try to wait for a short time (non-blocking)
                try:
                    self.vlc_process.wait(timeout=2)  # Reduced timeout
                except subprocess.TimeoutExpired:
                    # If it doesn't stop gracefully, force kill
                    try:
                        self.vlc_process.kill()
                        self.vlc_process.wait(timeout=1)  # Short wait after kill
                    except (subprocess.TimeoutExpired, Exception):
                        # If even kill fails, just continue
                        logger.warning("VLC process did not terminate cleanly")

            except Exception as e:
                logger.error(f"Error stopping VLC: {e}")
            finally:
                self.vlc_process = None
                self.current_url = None
                logger.info("VLC playback stopped")

    def is_playing(self):
        """Check if VLC is currently playing"""
        return self.vlc_process is not None and self.vlc_process.poll() is None

class StalkerPlayerThread(QThread):
    """Thread for handling Stalker portal operations"""
    categories_loaded = pyqtSignal(dict)
    channels_loaded = pyqtSignal(list)
    seasons_loaded = pyqtSignal(list)
    episodes_loaded = pyqtSignal(list)
    progress_updated = pyqtSignal(int)
    error_occurred = pyqtSignal(str)
    status_updated = pyqtSignal(str)

    def __init__(self, portal_url, mac_address, operation="categories", category_type=None, category_id=None, movie_id=None, season_id=None, season_data=None):
        super().__init__()
        self.portal_url = portal_url
        self.mac_address = mac_address
        self.operation = operation
        self.category_type = category_type
        self.category_id = category_id
        self.movie_id = movie_id
        self.season_id = season_id
        self.season_data = season_data  # Add season_data parameter
        self.portal = None

    def run(self):
        try:
            if self.operation == "categories":
                self.fetch_categories()
            elif self.operation == "channels":
                self.fetch_channels()
            elif self.operation == "seasons":
                self.fetch_seasons()
            elif self.operation == "episodes":
                self.fetch_episodes(self.season_data)
            else:
                self.error_occurred.emit(f"Unknown operation: {self.operation}")

        except Exception as e:
            error_msg = f"Error: {str(e)}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            self.progress_updated.emit(0)

    def fetch_categories(self):
        """Fetch categories using hybrid approach"""
        self.status_updated.emit("🔗 Connecting to portal...")
        self.progress_updated.emit(10)

        # Try both Stalker portal and regular portal approaches
        categories = {}

        # First try: StalkerPortal approach (for /stalker_portal/ URLs)
        try:
            self.status_updated.emit("🔄 Trying Stalker portal method...")
            self.progress_updated.emit(20)

            self.portal = StalkerPortal(
                portal_url=self.portal_url,
                mac=self.mac_address,
                timeout=15,
                retries=2
            )

            self.status_updated.emit("🤝 Performing handshake...")
            self.progress_updated.emit(30)
            self.portal.handshake()

            self.status_updated.emit("👤 Getting profile...")
            self.progress_updated.emit(40)
            self.portal.get_profile()

            # Fetch categories using StalkerPortal
            categories = self.fetch_stalker_categories()

            if any(categories.values()):  # If we got any categories
                self.status_updated.emit("✅ Stalker portal method successful!")
                self.progress_updated.emit(100)
                self.categories_loaded.emit(categories)
                return

        except Exception as e:
            logger.warning(f"Stalker portal method failed: {e}")
            self.status_updated.emit("⚠️ Stalker method failed, trying alternative...")
            self.progress_updated.emit(50)

        # Second try: Regular portal approach (for /portal.php URLs)
        try:
            self.status_updated.emit("🔄 Trying regular portal method...")
            self.progress_updated.emit(60)

            categories = self.fetch_regular_portal_categories()

            if any(categories.values()):  # If we got any categories
                self.status_updated.emit("✅ Regular portal method successful!")
                self.progress_updated.emit(100)
                self.categories_loaded.emit(categories)
                return

        except Exception as e:
            logger.warning(f"Regular portal method failed: {e}")
            self.status_updated.emit("⚠️ Regular method failed...")
            self.progress_updated.emit(70)

        # If both methods failed, return empty categories
        self.status_updated.emit("❌ Both methods failed - no categories found")
        self.progress_updated.emit(100)
        self.categories_loaded.emit({"Live": [], "Movies": [], "Series": []})

    def fetch_channels(self):
        """Fetch channels/content for a specific category"""
        self.status_updated.emit(f"📺 Loading {self.category_type} content...")
        self.progress_updated.emit(20)

        # Try StalkerPortal method first
        try:
            if not self.portal:
                self.portal = StalkerPortal(
                    portal_url=self.portal_url,
                    mac=self.mac_address,
                    timeout=15,
                    retries=2
                )
                self.portal.handshake()
                self.portal.get_profile()

            channels = []
            if self.category_type == "Live":
                channels = self.portal.get_channels_in_category(self.category_id)
            elif self.category_type == "Movies":
                channels = self.portal.get_vod_in_category(self.category_id)
            elif self.category_type == "Series":
                channels = self.portal.get_series_in_category(self.category_id)

            self.status_updated.emit(f"✅ Loaded {len(channels)} items")
            self.progress_updated.emit(100)
            self.channels_loaded.emit(channels)
            return

        except Exception as e:
            logger.warning(f"StalkerPortal channels fetch failed: {e}")

        # Try regular portal method
        try:
            channels = self.fetch_regular_portal_channels()
            self.status_updated.emit(f"✅ Loaded {len(channels)} items")
            self.progress_updated.emit(100)
            self.channels_loaded.emit(channels)

        except Exception as e:
            logger.error(f"Regular portal channels fetch failed: {e}")
            self.error_occurred.emit(f"Failed to load content: {str(e)}")

    def fetch_seasons(self):
        """Fetch seasons using the working method from STALKER PLAYER.py"""
        self.status_updated.emit("📺 Loading seasons...")
        self.progress_updated.emit(20)

        try:
            # Use the working method from STALKER PLAYER.py
            session = requests.Session()

            # Get token using the working method
            token = self.get_token_working_method(session)
            if not token:
                self.error_occurred.emit("Failed to retrieve token")
                return

            self.status_updated.emit("🔗 Connected to portal...")
            self.progress_updated.emit(40)

            # Use the working portal.php endpoint from STALKER PLAYER.py
            self.status_updated.emit("🔍 Fetching seasons...")
            self.progress_updated.emit(60)

            seasons = self.get_seasons_working_method(session, token)

            if seasons:
                self.status_updated.emit(f"✅ Loaded {len(seasons)} seasons")
                self.progress_updated.emit(100)
                self.seasons_loaded.emit(seasons)
            else:
                # If no seasons found, emit error to trigger fallback
                self.error_occurred.emit("No seasons found for this series")

        except Exception as e:
            logger.error(f"Failed to fetch seasons: {e}")
            self.error_occurred.emit(f"Failed to load seasons: {str(e)}")

    def get_token_working_method(self, session):
        """Get token using the working method from STALKER PLAYER.py"""
        try:
            handshake_url = f"{self.portal_url}/portal.php?type=stb&action=handshake&token=&JsHttpRequest=1-xml"

            cookies = {
                "mac": self.mac_address,
                "stb_lang": "en",
                "timezone": "Europe/London",
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) "
                              "AppleWebKit/533.3 (KHTML, like Gecko) "
                              "MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
            }

            response = session.get(handshake_url, cookies=cookies, headers=headers, timeout=15)
            response.raise_for_status()
            token = response.json().get("js", {}).get("token")
            if token:
                logger.debug(f"Token retrieved: {token}")
                return token
            else:
                logger.error("Token not found in handshake response.")
                return None
        except Exception as e:
            logger.error(f"Error getting token: {e}")
            return None

    def get_seasons_working_method(self, session, token):
        """Get seasons using the exact method from STALKER PLAYER.py"""
        try:
            cookies = {
                "mac": self.mac_address,
                "stb_lang": "en",
                "timezone": "Europe/London",
                "token": token,
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) "
                              "AppleWebKit/533.3 (KHTML, like Gecko) "
                              "MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
                "Authorization": f"Bearer {token}",
            }

            all_seasons = []
            page_number = 0

            while True:
                # Use the exact same URL format as STALKER PLAYER.py
                seasons_url = f"{self.portal_url}/portal.php?type=series&action=get_ordered_list&movie_id={self.movie_id}&season_id=0&episode_id=0&JsHttpRequest=1-xml&p={page_number}"
                logger.debug(f"Fetching seasons URL: {seasons_url}")

                response = session.get(seasons_url, cookies=cookies, headers=headers, timeout=10)
                logger.debug(f"Seasons response: {response.text}")

                if response.status_code == 200:
                    seasons_data = response.json().get("js", {}).get("data", [])
                    if not seasons_data:
                        break

                    # Process seasons exactly like STALKER PLAYER.py
                    for season in seasons_data:
                        # Ensure season_id is a string (exact copy from STALKER PLAYER.py)
                        season_id_raw = season.get("id", "")
                        season_id = str(season_id_raw)
                        logger.debug(f"Processing season_id: {season_id_raw} (type: {type(season_id_raw)}) converted to string: {season_id}")

                        season_number_extracted = None
                        if season_id.startswith("season"):
                            import re
                            match = re.match(r"season(\d+)", season_id)
                            if match:
                                season_number_extracted = int(match.group(1))
                            else:
                                logger.error(f"Unexpected season id format: {season_id}")
                        else:
                            import re
                            match = re.match(r"\d+:(\d+)", season_id)
                            if match:
                                season_number_extracted = int(match.group(1))
                            else:
                                logger.error(f"Unexpected season id format: {season_id}")

                        # Add season_number and item_type to the season data (exact copy)
                        season["season_number"] = season_number_extracted
                        season["item_type"] = "season"
                        season["movie_id"] = self.movie_id  # Add movie_id for reference

                    # Extend all_seasons with the processed seasons_data (exact copy)
                    all_seasons.extend(seasons_data)

                    # Check total_items to know when to stop (exact copy from STALKER PLAYER.py)
                    total_items = response.json().get("js", {}).get("total_items", len(all_seasons))
                    logger.debug(f"Fetched {len(all_seasons)} seasons out of {total_items}.")

                    if len(all_seasons) >= total_items:
                        break
                    page_number += 1
                else:
                    logger.error(f"Failed to fetch seasons for page {page_number} with status code {response.status_code}")
                    break

            if all_seasons:
                # Sort seasons by season_number (exact copy from STALKER PLAYER.py)
                all_seasons.sort(key=lambda x: x.get('season_number', 0))
                logger.info(f"Total seasons found: {len(all_seasons)}")
                return all_seasons
            else:
                logger.info("No seasons found")
                return []

        except Exception as e:
            logger.error(f"Error fetching seasons: {e}")
            return []

    def fetch_episodes(self, season_data=None):
        """Fetch episodes using the exact method from STALKER PLAYER.py"""
        self.status_updated.emit("📺 Loading episodes...")
        self.progress_updated.emit(20)

        try:
            # First try: Use series data from season if available (preferred method from STALKER PLAYER.py)
            if season_data and season_data.get("series"):
                self.status_updated.emit("🔍 Loading episodes from season data...")
                self.progress_updated.emit(60)

                episodes = self.get_episodes_from_series_data(season_data)

                if episodes:
                    self.status_updated.emit(f"✅ Loaded {len(episodes)} episodes")
                    self.progress_updated.emit(100)
                    self.episodes_loaded.emit(episodes)
                    return

            # Fallback: Use API method if series data not available
            session = requests.Session()

            # Get token using the working method
            token = self.get_token_working_method(session)
            if not token:
                self.error_occurred.emit("Failed to retrieve token")
                return

            self.status_updated.emit("🔗 Connected to portal...")
            self.progress_updated.emit(40)

            # Use the working portal.php endpoint from STALKER PLAYER.py
            self.status_updated.emit("🔍 Fetching episodes via API...")
            self.progress_updated.emit(60)

            episodes = self.get_episodes_working_method(session, token)

            if episodes:
                self.status_updated.emit(f"✅ Loaded {len(episodes)} episodes")
                self.progress_updated.emit(100)
                self.episodes_loaded.emit(episodes)
            else:
                # If no episodes found, emit error to trigger fallback
                self.error_occurred.emit("No episodes found for this season")

        except Exception as e:
            logger.error(f"Failed to fetch episodes: {e}")
            self.error_occurred.emit(f"Failed to load episodes: {str(e)}")

    def get_episodes_from_series_data(self, season_data):
        """Get episodes from series data stored in season (exact method from STALKER PLAYER.py)"""
        try:
            # This method uses the series data from the season context (exact copy from STALKER PLAYER.py)
            series_list = season_data.get("series", [])
            if not series_list:
                logger.info("No episodes found in this season.")
                return []

            logger.debug(f"Series episodes found: {series_list}")
            all_episodes = []

            # Create episodes from series list (exact copy from STALKER PLAYER.py)
            for episode_number in series_list:
                episode = {
                    "id": f"{self.movie_id}:{episode_number}",
                    "series_id": self.movie_id,
                    "season_number": season_data.get("season_number"),
                    "episode_number": episode_number,
                    "name": f"Episode {episode_number}",
                    "item_type": "episode",
                    "cmd": season_data.get("cmd"),
                    "movie_id": self.movie_id,
                    "season_id": self.season_id,
                }
                logger.debug(f"Episode details: {episode}")
                all_episodes.append(episode)

            if all_episodes:
                # Process and sort episodes (exact copy from STALKER PLAYER.py)
                processed_episodes = self.process_and_sort_episodes(all_episodes)
                return processed_episodes
            else:
                logger.info("No episodes found.")
                return []

        except Exception as e:
            logger.error(f"Error in alternative episode method: {e}")
            return []

    def process_and_sort_episodes(self, episodes):
        """
        Process and sort episodes (exact copy from STALKER PLAYER.py)
        """
        for ep in episodes:
            # Ensure episode_number is an integer
            try:
                ep["episode_number"] = int(ep.get("episode_number", 0))
            except ValueError:
                logger.warning(f"Invalid episode_number for episode: {ep}")
                ep["episode_number"] = 0  # Default or handle as needed

            ep["item_type"] = "episode"

        # Log episodes before sorting
        logger.debug(f"Episodes before sorting: {episodes}")

        # Sort episodes by episode_number in ascending order
        episodes.sort(key=lambda x: x.get("episode_number", 0))

        # Log episodes after sorting
        logger.debug(f"Episodes after sorting: {episodes}")

        return episodes

    def get_episodes_working_method(self, session, token):
        """Get episodes using the improved method from STALKER PLAYER.py"""
        try:
            cookies = {
                "mac": self.mac_address,
                "stb_lang": "en",
                "timezone": "Europe/London",
                "token": token,
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) "
                              "AppleWebKit/533.3 (KHTML, like Gecko) "
                              "MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
                "Authorization": f"Bearer {token}",
            }

            all_episodes = []
            page_number = 0

            while True:
                # Use the working portal.php endpoint from STALKER PLAYER.py
                episodes_url = f"{self.portal_url}/portal.php?type=series&action=get_ordered_list&movie_id={self.movie_id}&season_id={self.season_id}&episode_id=0&JsHttpRequest=1-xml&p={page_number}"
                logger.debug(f"Fetching episodes URL: {episodes_url}")

                response = session.get(episodes_url, cookies=cookies, headers=headers, timeout=10)
                response.raise_for_status()

                response_json = response.json()
                logger.debug(f"Episodes response: {response_json}")

                if not isinstance(response_json, dict) or "js" not in response_json:
                    logger.error("Unexpected response structure for episodes.")
                    break

                episodes_data = response_json.get("js", {}).get("data", [])
                if not episodes_data:
                    logger.debug("No more episodes data found.")
                    break

                for episode in episodes_data:
                    # Extract episode number using improved logic from STALKER PLAYER.py
                    episode_id = episode.get("id", "")
                    episode_number = None

                    # Try to extract episode number from various sources
                    if ":" in str(episode_id):
                        # Format like "123:456" where 456 is episode number
                        try:
                            episode_number = int(str(episode_id).split(":")[-1])
                        except (ValueError, IndexError):
                            pass

                    if episode_number is None:
                        # Try to extract from title
                        title = episode.get("title", "")
                        import re
                        episode_match = re.search(r"[Ee]pisode\s*(\d+)", title)
                        if episode_match:
                            episode_number = int(episode_match.group(1))
                        else:
                            # Try to find any number in title
                            number_match = re.search(r"(\d+)", title)
                            if number_match:
                                episode_number = int(number_match.group(1))
                            else:
                                # Use position as fallback
                                episode_number = len(all_episodes) + 1

                    episode_info = {
                        "id": episode.get("id"),
                        "episode_id": episode.get("id"),
                        "movie_id": self.movie_id,
                        "season_id": self.season_id,
                        "episode_number": episode_number,
                        "name": episode.get("title", f"Episode {episode_number}"),
                        "item_type": "episode",
                        "cmd": episode.get("cmd"),
                    }
                    all_episodes.append(episode_info)
                    logger.debug(f"Episode found: {episode_info}")

                page_number += 1
                if page_number > 20:  # Safety limit for episodes
                    break

            # Sort episodes by episode_number like in STALKER PLAYER.py
            all_episodes.sort(key=lambda x: x.get('episode_number', 0))
            logger.info(f"Total episodes found: {len(all_episodes)}")
            return all_episodes

        except Exception as e:
            logger.error(f"Error fetching episodes: {e}")
            return []

    def fetch_regular_portal_channels(self):
        """Fetch channels using regular portal.php endpoints"""
        import requests

        # Create session and get token
        session = requests.Session()
        token = self.get_token(session)

        if not token:
            raise Exception("Failed to get token from regular portal")

        cookies = {
            "mac": self.mac_address,
            "stb_lang": "en",
            "timezone": "Europe/London",
            "token": token,
        }
        headers = {
            "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) "
                          "AppleWebKit/533.3 (KHTML, like Gecko) "
                          "MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
            "Authorization": f"Bearer {token}",
        }

        if self.category_type == "Live":
            # Fetch Live channels
            channels_url = f"{self.portal_url}/portal.php?type=itv&action=get_ordered_list&genre={self.category_id}&JsHttpRequest=1-xml&p=0"
        elif self.category_type == "Movies":
            # Fetch Movies
            channels_url = f"{self.portal_url}/portal.php?type=vod&action=get_ordered_list&category={self.category_id}&JsHttpRequest=1-xml&p=0"
        elif self.category_type == "Series":
            # Fetch Series
            channels_url = f"{self.portal_url}/portal.php?type=series&action=get_ordered_list&category={self.category_id}&p=0&JsHttpRequest=1-xml"
        else:
            raise Exception(f"Unknown category type: {self.category_type}")

        response = session.get(channels_url, cookies=cookies, headers=headers, timeout=10)
        response.raise_for_status()
        response_json = response.json()

        channels_data = response_json.get("js", {}).get("data", [])
        for channel in channels_data:
            # Set item type based on category
            if self.category_type == "Live":
                channel["item_type"] = "channel"
            elif self.category_type == "Movies":
                channel["item_type"] = "vod"
            elif self.category_type == "Series":
                channel["item_type"] = "series"
                # For series, we need movie_id for navigation
                channel["movie_id"] = channel.get("id") or channel.get("video_id")

        return channels_data

    def fetch_stalker_categories(self):
        """Fetch categories using StalkerPortal class"""
        categories = {}

        try:
            self.status_updated.emit("📺 Loading Live categories...")
            categories["Live"] = self.portal.get_itv_categories()
            self.progress_updated.emit(75)
        except Exception as e:
            logger.error(f"Error fetching Live categories: {e}")
            categories["Live"] = []

        try:
            self.status_updated.emit("🎬 Loading Movies categories...")
            categories["Movies"] = self.portal.get_vod_categories()
            self.progress_updated.emit(85)
        except Exception as e:
            logger.error(f"Error fetching Movies categories: {e}")
            categories["Movies"] = []

        try:
            self.status_updated.emit("📺 Loading Series categories...")
            categories["Series"] = self.portal.get_series_categories()
            self.progress_updated.emit(95)
        except Exception as e:
            logger.error(f"Error fetching Series categories: {e}")
            categories["Series"] = []

        return categories

    def fetch_regular_portal_categories(self):
        """Fetch categories using regular portal.php endpoints"""
        import requests

        categories = {"Live": [], "Movies": [], "Series": []}

        # Create session and get token
        session = requests.Session()
        token = self.get_token(session)

        if not token:
            raise Exception("Failed to get token from regular portal")

        cookies = {
            "mac": self.mac_address,
            "stb_lang": "en",
            "timezone": "Europe/London",
            "token": token,
        }
        headers = {
            "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) "
                          "AppleWebKit/533.3 (KHTML, like Gecko) "
                          "MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
            "Authorization": f"Bearer {token}",
        }

        # Fetch Live categories
        try:
            self.status_updated.emit("📺 Loading Live categories...")
            genres_url = f"{self.portal_url}/portal.php?type=itv&action=get_genres&JsHttpRequest=1-xml"
            response = session.get(genres_url, cookies=cookies, headers=headers, timeout=10)
            response.raise_for_status()
            genre_data = response.json().get("js", [])
            if genre_data:
                categories["Live"] = [
                    {
                        "title": item["title"],
                        "name": item["title"],
                        "id": item["id"],
                        "category_type": "IPTV",
                        "category_id": item["id"],
                    }
                    for item in genre_data
                ]
            self.progress_updated.emit(75)
        except Exception as e:
            logger.error(f"Error fetching Live categories via portal.php: {e}")

        # Fetch Movies categories
        try:
            self.status_updated.emit("🎬 Loading Movies categories...")
            vod_url = f"{self.portal_url}/portal.php?type=vod&action=get_categories&JsHttpRequest=1-xml"
            response = session.get(vod_url, cookies=cookies, headers=headers, timeout=10)
            response.raise_for_status()
            categories_data = response.json().get("js", [])
            if categories_data:
                categories["Movies"] = [
                    {
                        "title": category["title"],
                        "name": category["title"],
                        "id": category["id"],
                        "category_type": "VOD",
                        "category_id": category["id"],
                    }
                    for category in categories_data
                ]
            self.progress_updated.emit(85)
        except Exception as e:
            logger.error(f"Error fetching Movies categories via portal.php: {e}")

        # Fetch Series categories
        try:
            self.status_updated.emit("📺 Loading Series categories...")
            series_url = f"{self.portal_url}/portal.php?type=series&action=get_categories&JsHttpRequest=1-xml"
            response = session.get(series_url, cookies=cookies, headers=headers, timeout=10)
            response.raise_for_status()
            categories_data = response.json().get("js", [])
            if categories_data:
                categories["Series"] = [
                    {
                        "title": category["title"],
                        "name": category["title"],
                        "id": category["id"],
                        "category_type": "Series",
                        "category_id": category["id"],
                    }
                    for category in categories_data
                ]
            self.progress_updated.emit(95)
        except Exception as e:
            logger.error(f"Error fetching Series categories via portal.php: {e}")

        return categories

    def get_token(self, session):
        """Get token using regular portal handshake"""
        try:
            handshake_url = f"{self.portal_url}/portal.php?type=stb&action=handshake&JsHttpRequest=1-xml"
            cookies = {
                "mac": self.mac_address,
                "stb_lang": "en",
                "timezone": "Europe/London",
            }
            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) "
                              "AppleWebKit/533.3 (KHTML, like Gecko) "
                              "MAG200 stbapp ver: 2 rev: 250 Safari/533.3"
            }
            response = session.get(handshake_url, cookies=cookies, headers=headers, timeout=15)
            response.raise_for_status()
            token = response.json().get("js", {}).get("token")
            if token:
                logger.debug(f"Token retrieved: {token}")
                return token
            else:
                logger.error("Token not found in handshake response.")
                return None
        except Exception as e:
            logger.error(f"Error getting token: {e}")
            return None

class PlayerWidget(QWidget):
    """Main player widget with tabs for Stalker and Xtream"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def set_download_manager(self, download_manager):
        """Set download manager for both Stalker and Xtream players"""
        try:
            # Set download manager for Stalker player
            if hasattr(self, 'stalker_player') and hasattr(self.stalker_player, 'set_download_manager'):
                self.stalker_player.set_download_manager(download_manager)

            # Set download manager for Xtream player (if it exists in this widget)
            if hasattr(self, 'xtream_player') and hasattr(self.xtream_player, 'set_download_manager'):
                self.xtream_player.set_download_manager(download_manager)

        except Exception as e:
            logger.error(f"Error setting download manager: {e}")

    def init_ui(self):
        """Initialize the tabbed player interface"""
        layout = QVBoxLayout(self)

        # Create tab widget for different player types
        self.player_tabs = QTabWidget()
        self.player_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {Colors.PRIMARY};
                background-color: {Colors.SURFACE};
            }}
            QTabBar::tab {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: #E0E0E0;
            }}
        """)

        # Add Stalker player tab
        self.stalker_player = StalkerPlayerWidget()
        self.player_tabs.addTab(self.stalker_player, "📺 Stalker Portal")

        # Add Xtream player tab (coming soon)
        from xtream_player_module import XtreamPlayerWidget
        self.xtream_player = XtreamPlayerWidget()
        self.player_tabs.addTab(self.xtream_player, "🌐 Xtream Server")

        layout.addWidget(self.player_tabs)

class StalkerPlayerWidget(QWidget, DownloadButtonWidget):
    """Stalker player widget for browsing and playing Stalker content"""

    def __init__(self):
        super().__init__()
        if DOWNLOAD_AVAILABLE:
            DownloadButtonWidget.__init__(self)
        self.current_portal = None
        self.current_categories = {}

        # Navigation state
        self.navigation_stack = []
        self.current_view = "categories"  # categories, channels, seasons, episodes
        self.current_category = None
        self.current_series = None
        self.current_season = None
        self.current_content = []

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface using design.py layout structure"""
        # Main container - horizontal split like design.py
        main_container = QSplitter(Qt.Orientation.Horizontal)
        main_container.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {Colors.PRIMARY};
                width: 3px;
            }}
        """)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.addWidget(main_container)

        # Left panel (weight=1) - Connection fields and content lists
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(5)
        left_layout.setContentsMargins(5, 5, 5, 5)

        # Connection section (vertical layout for better display)
        connection_frame = QFrame()
        connection_frame.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {Colors.PRIMARY};
                border-radius: 6px;
                padding: 5px;
                background-color: {Colors.SURFACE};
                margin-bottom: 3px;
            }}
        """)
        connection_layout = QVBoxLayout(connection_frame)
        connection_layout.setSpacing(2)  # Reduced spacing for more compact layout

        # Portal URL input (compact)
        url_label = QLabel("🌐 URL:")
        url_label.setStyleSheet(f"""
            QLabel {{
                color: {Colors.TEXT};
                font-weight: bold;
                border: none;
                font-size: 11px;
                min-width: 40px;
            }}
        """)
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("http://example.com:8080")
        self.url_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 4px;
                color: {Colors.TEXT};
                font-size: 11px;
            }}
            QLineEdit:focus {{
                border: 1px solid {Colors.PRIMARY};
            }}
        """)
        # Clear URL button
        self.clear_url_btn = QPushButton("🗑️")
        self.clear_url_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.ERROR};
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px;
                font-weight: bold;
                max-width: 28px;
                max-height: 28px;
            }}
            QPushButton:hover {{
                background-color: #c82333;
            }}
        """)
        self.clear_url_btn.clicked.connect(lambda: self.url_input.clear())
        self.clear_url_btn.setToolTip("Clear URL field")

        # URL row layout
        url_row_layout = QHBoxLayout()
        url_row_layout.addWidget(url_label)
        url_row_layout.addWidget(self.url_input)
        url_row_layout.addWidget(self.clear_url_btn)
        connection_layout.addLayout(url_row_layout)

        # MAC address input (vertical layout)
        mac_label = QLabel("🔧 MAC Address:")
        mac_label.setStyleSheet(f"""
            QLabel {{
                color: {Colors.TEXT};
                font-weight: bold;
                border: none;
                font-size: 11px;
                min-width: 40px;
            }}
        """)
        self.mac_input = QLineEdit()
        self.mac_input.setPlaceholderText("00:1A:79:XX:XX:XX")
        self.mac_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 4px;
                color: {Colors.TEXT};
                font-size: 11px;
                font-family: monospace;
            }}
            QLineEdit:focus {{
                border: 1px solid {Colors.PRIMARY};
            }}
        """)
        # Clear MAC button
        self.clear_mac_btn = QPushButton("🗑️")
        self.clear_mac_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.ERROR};
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px;
                font-weight: bold;
                max-width: 28px;
                max-height: 28px;
            }}
            QPushButton:hover {{
                background-color: #c82333;
            }}
        """)
        self.clear_mac_btn.clicked.connect(lambda: self.mac_input.clear())
        self.clear_mac_btn.setToolTip("Clear MAC field")

        # MAC row layout
        mac_row_layout = QHBoxLayout()
        mac_row_layout.addWidget(mac_label)
        mac_row_layout.addWidget(self.mac_input)
        mac_row_layout.addWidget(self.clear_mac_btn)
        connection_layout.addLayout(mac_row_layout)

        # Connect button with better styling
        self.connect_btn = QPushButton("🔗 Connect to Portal")
        self.connect_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {Colors.SUCCESS}, stop:1 #28a745);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #218838);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #218838, stop:1 #1e7e34);
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """)
        self.connect_btn.clicked.connect(self.connect_to_portal)
        connection_layout.addWidget(self.connect_btn)

        # Set size policy to keep connection frame compact and always visible
        from PyQt6.QtWidgets import QSizePolicy
        connection_frame.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        connection_frame.setMaximumHeight(120)  # Ensure it stays compact

        left_layout.addWidget(connection_frame)

        # Content area (initially hidden) - moved to left panel
        self.content_widget = QWidget()
        self.setup_content_area_left()
        self.content_widget.setVisible(False)

        # Add content widget with stretch factor so it takes remaining space
        # while keeping connection frame fixed at the top
        left_layout.addWidget(self.content_widget, 1)  # Stretch factor 1

        # Add left panel to main container
        main_container.addWidget(left_panel)

        # Right panel (weight=3) - Large video player like design.py
        right_panel = QWidget()
        self.setup_video_player_right(right_panel)
        main_container.addWidget(right_panel)

        # Set proportions like design.py (left=1, right=3)
        main_container.setSizes([300, 900])

    def setup_content_area_left(self):
        """Setup the content browsing area for left panel (like design.py)"""
        layout = QVBoxLayout(self.content_widget)
        layout.setSpacing(5)
        layout.setContentsMargins(5, 5, 5, 5)

        # Categories section (like design.py notebook)
        categories_label = QLabel("📂 Categories")
        categories_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        categories_label.setStyleSheet(f"color: {Colors.TEXT}; padding: 5px;")
        layout.addWidget(categories_label)

        # Category tabs (like design.py notebook)
        self.category_tabs = QTabWidget()
        self.category_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {Colors.PRIMARY};
                background-color: {Colors.SURFACE};
            }}
            QTabBar::tab {{
                background-color: {Colors.BACKGROUND};
                color: {Colors.TEXT};
                padding: 8px 16px;
                margin-right: 2px;
                font-size: 11px;
            }}
            QTabBar::tab:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
        """)
        layout.addWidget(self.category_tabs)

        # Content list (like design.py channels_list)
        content_label = QLabel("📺 Content")
        content_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        content_label.setStyleSheet(f"color: {Colors.TEXT}; padding: 5px;")
        layout.addWidget(content_label)

        # Content list (like design.py channels_list)
        self.content_list = QListWidget()
        self.content_list.setStyleSheet(f"""
            QListWidget {{
                background-color: {Colors.SURFACE};
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                color: {Colors.TEXT};
                font-size: 12px;
            }}
            QListWidget::item {{
                padding: 10px;
                border-bottom: 1px solid #EEEEEE;
            }}
            QListWidget::item:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QListWidget::item:hover {{
                background-color: #F0F0F0;
            }}
        """)
        self.content_list.itemDoubleClicked.connect(self.on_content_item_clicked)

        # Context menu removed - no longer needed

        layout.addWidget(self.content_list)



        # Test button for controls (temporary for debugging)
        self.test_controls_btn = QPushButton("🎮 Show Controls")
        self.test_controls_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.WARNING};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {Colors.WARNING}DD;
            }}
        """)
        self.test_controls_btn.clicked.connect(self.test_show_controls)
        layout.addWidget(self.test_controls_btn)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                text-align: center;
                color: {Colors.TEXT};
                max-height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.PRIMARY};
                border-radius: 3px;
            }}
        """)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setStyleSheet(f"color: {Colors.TEXT}; border: none; padding: 5px; font-size: 11px;")
        layout.addWidget(self.status_label)

        # Initialize VLC player
        self.vlc_player = VLCPlayer()

    def setup_video_player_right(self, right_panel):
        """Setup the large video player for right panel with auto-hide controls"""
        player_layout = QVBoxLayout(right_panel)
        player_layout.setContentsMargins(5, 5, 5, 5)

        # Create a container for video player with overlay controls
        self.video_container = QWidget()
        self.video_container.setStyleSheet("QWidget { background-color: #1a1a1a; }")

        # Override resize event to reposition controls
        original_resize = self.video_container.resizeEvent
        def resize_event_handler(event):
            original_resize(event)
            # Reposition controls after resize
            if hasattr(self, 'controls_frame') and self.controls_frame.isVisible():
                self.position_controls()
        self.video_container.resizeEvent = resize_event_handler

        player_layout.addWidget(self.video_container)

        # Use absolute positioning for overlay controls
        container_layout = QVBoxLayout(self.video_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # Large VLC Player widget (like design.py video_frame) - NO transparency here
        self.vlc_widget = QWidget()
        self.vlc_widget.setStyleSheet(f"""
            QWidget {{
                background-color: black;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                min-height: 500px;
            }}
        """)

        # Enable mouse tracking for auto-hide functionality
        self.vlc_widget.setMouseTracking(True)
        self.video_container.setMouseTracking(True)
        right_panel.setMouseTracking(True)

        container_layout.addWidget(self.vlc_widget)

        # Player controls - Overlay with auto-hide functionality
        self.controls_frame = QFrame(self.video_container)
        self.controls_frame.setStyleSheet(f"""
            QFrame {{
                background-color: rgba(45, 45, 45, 0.9);
                border: 1px solid rgba(0, 123, 255, 0.5);
                border-radius: 25px;
                padding: 4px 8px;
                max-height: 50px;
            }}
        """)

        # Position controls as overlay at bottom center (wider for new controls)
        self.controls_frame.setFixedSize(400, 60)
        self.controls_frame.move(
            (self.video_container.width() - 400) // 2,
            self.video_container.height() - 80
        )

        # Initially hide controls
        self.controls_frame.hide()

        controls_layout = QHBoxLayout(self.controls_frame)
        controls_layout.setSpacing(6)
        controls_layout.setContentsMargins(8, 4, 8, 4)

        # Control buttons - Compact and transparent design
        self.play_btn = QPushButton("▶️")
        self.pause_btn = QPushButton("⏸️")
        self.stop_btn = QPushButton("⏹️")

        # Style the control buttons - Smaller and more transparent
        button_style = f"""
            QPushButton {{
                background-color: rgba(0, 123, 255, 0.7);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                padding: 4px;
                font-weight: bold;
                font-size: 14px;
                min-width: 40px;
                max-width: 40px;
                min-height: 40px;
                max-height: 40px;
            }}
            QPushButton:hover {{
                background-color: rgba(0, 123, 255, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }}
            QPushButton:pressed {{
                background-color: rgba(0, 123, 255, 1.0);
            }}
            QPushButton:disabled {{
                background-color: rgba(204, 204, 204, 0.5);
                color: rgba(102, 102, 102, 0.7);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """

        self.play_btn.setStyleSheet(button_style)
        self.pause_btn.setStyleSheet(button_style)
        self.stop_btn.setStyleSheet(button_style)

        # Connect control buttons
        self.play_btn.clicked.connect(self.play_content)
        self.pause_btn.clicked.connect(self.pause_content)
        self.stop_btn.clicked.connect(self.stop_content)

        # Initially disable controls
        self.play_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)

        # Volume controls
        self.volume_btn = QPushButton("🔊")
        self.volume_btn.setStyleSheet(button_style)
        self.volume_btn.setFixedSize(30, 30)
        self.volume_btn.clicked.connect(self.toggle_mute)
        self.volume_btn.setToolTip("Toggle Mute")

        # Volume slider
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)  # Default volume 80%
        self.volume_slider.setFixedWidth(60)
        self.volume_slider.valueChanged.connect(self.change_volume)
        self.volume_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {Colors.PRIMARY};
                height: 4px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 2px;
            }}
            QSlider::handle:horizontal {{
                background: {Colors.PRIMARY};
                border: 1px solid {Colors.PRIMARY};
                width: 12px;
                height: 12px;
                border-radius: 6px;
                margin: -4px 0;
            }}
            QSlider::sub-page:horizontal {{
                background: {Colors.PRIMARY};
                border-radius: 2px;
            }}
        """)

        # Subtitle controls
        self.subtitle_btn = QPushButton("📝")
        self.subtitle_btn.setStyleSheet(button_style)
        self.subtitle_btn.setFixedSize(30, 30)
        self.subtitle_btn.clicked.connect(self.toggle_subtitles)
        self.subtitle_btn.setToolTip("Toggle Subtitles")

        # Subtitle file button
        self.subtitle_file_btn = QPushButton("📁")
        self.subtitle_file_btn.setStyleSheet(button_style)
        self.subtitle_file_btn.setFixedSize(30, 30)
        self.subtitle_file_btn.clicked.connect(self.load_subtitle_file)
        self.subtitle_file_btn.setToolTip("Load Subtitle File")

        controls_layout.addWidget(self.play_btn)
        controls_layout.addWidget(self.pause_btn)
        controls_layout.addWidget(self.stop_btn)

        # Add separator
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        separator1.setStyleSheet("color: rgba(255, 255, 255, 0.3);")
        controls_layout.addWidget(separator1)

        # Add volume controls
        controls_layout.addWidget(self.volume_btn)
        controls_layout.addWidget(self.volume_slider)

        # Add separator
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        separator2.setStyleSheet("color: rgba(255, 255, 255, 0.3);")
        controls_layout.addWidget(separator2)

        # Add subtitle controls
        controls_layout.addWidget(self.subtitle_btn)
        controls_layout.addWidget(self.subtitle_file_btn)

        # Setup auto-hide timer
        from PyQt6.QtCore import QTimer
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_controls)
        self.hide_timer.setSingleShot(True)

        # Enable mouse tracking for the video area
        self.video_container.setMouseTracking(True)
        self.vlc_widget.setMouseTracking(True)

        # Install event filters to capture mouse events properly
        self.video_container.installEventFilter(self)
        self.vlc_widget.installEventFilter(self)

        # Setup VLC integration now that vlc_widget is created
        self.setup_vlc_integration()

    def eventFilter(self, obj, event):
        """Event filter to handle mouse events on video widgets"""
        # Check if the event is from our video widgets
        if obj in [self.video_container, self.vlc_widget]:
            if event.type() == QEvent.Type.Enter:
                # Mouse entered video area
                logger.debug("Mouse entered video area")
                self.show_controls()
                return False
            elif event.type() == QEvent.Type.Leave:
                # Mouse left video area - start hide timer
                logger.debug("Mouse left video area")
                self.hide_timer.start(2000)  # Hide after 2 seconds
                return False
            elif event.type() == QEvent.Type.MouseMove:
                # Mouse moved in video area
                logger.debug("Mouse moved in video area")
                self.show_controls()
                return False

        # Pass the event to the parent class
        return super().eventFilter(obj, event)

    def position_controls(self):
        """Position the controls at the bottom center of the video container"""
        if hasattr(self, 'controls_frame') and self.controls_frame is not None:
            container_width = self.video_container.width()
            container_height = self.video_container.height()
            controls_width = 400  # Wider for new controls
            controls_height = 60

            if container_width > 0 and container_height > 0:
                x = max(0, (container_width - controls_width) // 2)
                y = max(0, container_height - controls_height - 20)

                self.controls_frame.move(x, y)
                self.controls_frame.resize(controls_width, controls_height)
                logger.debug(f"Positioned controls at: {x}, {y} (container: {container_width}x{container_height})")

    def show_controls(self):
        """Show the overlay controls"""
        if hasattr(self, 'controls_frame') and self.controls_frame is not None:
            logger.debug("Attempting to show controls")

            # Position controls first
            self.position_controls()

            # Make sure controls are visible with high z-order
            self.controls_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: rgba(0, 0, 0, 220);
                    border-radius: 8px;
                    border: 2px solid {Colors.PRIMARY};
                }}
                QPushButton {{
                    background-color: {Colors.PRIMARY};
                    color: white;
                    border: none;
                    border-radius: 20px;
                    padding: 4px;
                    font-weight: bold;
                    font-size: 14px;
                    min-width: 40px;
                    max-width: 40px;
                    min-height: 40px;
                    max-height: 40px;
                }}
                QPushButton:hover {{
                    background-color: {Colors.PRIMARY}DD;
                }}
                QPushButton:pressed {{
                    background-color: {Colors.PRIMARY}BB;
                }}
            """)

            # Show and raise the controls with maximum z-order
            self.controls_frame.show()
            self.controls_frame.raise_()
            self.controls_frame.setVisible(True)

            # Ensure it's on top
            self.controls_frame.activateWindow()
            self.controls_frame.repaint()

            logger.debug("Controls should now be visible")

            # Stop hide timer if running
            if self.hide_timer.isActive():
                self.hide_timer.stop()

    def hide_controls(self):
        """Hide the overlay controls"""
        if hasattr(self, 'controls_frame'):
            self.controls_frame.hide()

    def test_show_controls(self):
        """Test method to manually show controls for debugging"""
        logger.info("Test button clicked - attempting to show controls")
        if hasattr(self, 'controls_frame'):
            logger.info(f"Controls frame exists: {self.controls_frame}")
            logger.info(f"Controls frame parent: {self.controls_frame.parent()}")
            logger.info(f"Controls frame visible: {self.controls_frame.isVisible()}")
            logger.info(f"Video container size: {self.video_container.size()}")

            # Force show controls
            self.show_controls()

            # Also try to show them manually
            self.controls_frame.setVisible(True)
            self.controls_frame.show()
            self.controls_frame.raise_()

            logger.info(f"After manual show - visible: {self.controls_frame.isVisible()}")
        else:
            logger.error("Controls frame does not exist!")

    def setup_vlc_integration(self):
        """Setup VLC integration with the embedded widget"""
        try:
            import vlc

            # Create VLC instance
            self.vlc_instance = vlc.Instance()
            self.vlc_media_player = self.vlc_instance.media_player_new()

            # Set the widget as the video output
            if hasattr(self.vlc_widget, 'winId'):
                # Windows
                self.vlc_media_player.set_hwnd(self.vlc_widget.winId())
            elif hasattr(self.vlc_widget, 'winId'):
                # Linux
                self.vlc_media_player.set_xwindow(self.vlc_widget.winId())
            elif hasattr(self.vlc_widget, 'winId'):
                # macOS
                self.vlc_media_player.set_nsobject(int(self.vlc_widget.winId()))

            logger.info("VLC integration setup successful")

        except ImportError:
            logger.warning("VLC not available, using external player")
            self.vlc_instance = None
            self.vlc_media_player = None
        except Exception as e:
            logger.error(f"VLC integration setup failed: {e}")
            self.vlc_instance = None
            self.vlc_media_player = None

    def connect_to_portal(self):
        """Connect to the Stalker portal"""
        portal_url = self.url_input.text().strip()
        mac_address = self.mac_input.text().strip()

        if not portal_url or not mac_address:
            QMessageBox.warning(self, "Error", "Please enter both Portal URL and MAC address")
            return

        # Prepare URL for display and for StalkerPortal class
        display_url = portal_url
        stalker_portal_url = portal_url

        # For display: ensure it shows /stalker_portal
        if not display_url.endswith('/stalker_portal'):
            if '/stalker_portal' not in display_url:
                if display_url.endswith('/'):
                    display_url = display_url + 'stalker_portal'
                else:
                    display_url = display_url + '/stalker_portal'

        # For StalkerPortal class: ensure it does NOT have /stalker_portal
        # because StalkerPortal adds it automatically
        if stalker_portal_url.endswith('/stalker_portal'):
            stalker_portal_url = stalker_portal_url.replace('/stalker_portal', '')

        # Update the input field to show the display URL
        self.url_input.setText(display_url)

        # Validate MAC format
        import re
        if not re.match(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$', mac_address):
            QMessageBox.warning(self, "Error", "Invalid MAC address format")
            return

        # Start connection
        self.connect_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Create and start worker thread
        self.worker_thread = StalkerPlayerThread(stalker_portal_url, mac_address, "categories")
        self.worker_thread.categories_loaded.connect(self.on_categories_loaded)
        self.worker_thread.progress_updated.connect(self.progress_bar.setValue)
        self.worker_thread.error_occurred.connect(self.on_error)
        self.worker_thread.status_updated.connect(self.status_label.setText)
        self.worker_thread.finished.connect(self.on_connection_finished)
        self.worker_thread.start()

    def on_categories_loaded(self, categories):
        """Handle loaded categories"""
        self.current_categories = categories
        self.populate_categories(categories)
        self.content_widget.setVisible(True)

        # Store portal reference for stream URL generation
        if hasattr(self, 'worker_thread') and hasattr(self.worker_thread, 'portal'):
            self.portal = self.worker_thread.portal

    def populate_categories(self, categories):
        """Populate the category tabs"""
        self.category_tabs.clear()

        # Debug: Print categories info
        logger.info(f"Populating categories: {categories}")

        total_categories = 0
        for tab_name, category_list in categories.items():
            logger.info(f"Tab '{tab_name}': {len(category_list) if category_list else 0} categories")

            # Add tab even if empty, but show message
            list_widget = QListWidget()
            list_widget.setStyleSheet(f"""
                QListWidget {{
                    background-color: {Colors.SURFACE};
                    border: 1px solid #CCCCCC;
                    border-radius: 4px;
                    color: {Colors.TEXT};
                    padding: 5px;
                }}
                QListWidget::item {{
                    padding: 8px;
                    border-bottom: 1px solid #EEEEEE;
                    border-radius: 2px;
                    margin: 1px;
                }}
                QListWidget::item:selected {{
                    background-color: {Colors.PRIMARY};
                    color: white;
                }}
                QListWidget::item:hover {{
                    background-color: #F0F0F0;
                }}
            """)

            if category_list and len(category_list) > 0:
                for category in category_list:
                    # Get category name with fallbacks
                    cat_name = category.get('title') or category.get('name') or category.get('category_name') or 'Unknown'
                    cat_id = category.get('id') or category.get('category_id')

                    # Create display name with emoji
                    if tab_name == "Live":
                        display_name = f"📺 {cat_name}"
                    elif tab_name == "Movies":
                        display_name = f"🎬 {cat_name}"
                    elif tab_name == "Series":
                        display_name = f"📺 {cat_name}"
                    else:
                        display_name = f"📂 {cat_name}"

                    item = QListWidgetItem(display_name)
                    item.setData(Qt.ItemDataRole.UserRole, category)
                    list_widget.addItem(item)
                    logger.debug(f"Added category: {cat_name} (ID: {cat_id})")

                total_categories += len(category_list)
                list_widget.itemDoubleClicked.connect(lambda item, t=tab_name: self.on_category_item_clicked(item, t))
            else:
                # Add empty message
                empty_item = QListWidgetItem(f"❌ No {tab_name} categories found")
                empty_item.setFlags(Qt.ItemFlag.NoItemFlags)  # Make it non-selectable
                list_widget.addItem(empty_item)

            # Add tab with emoji
            if tab_name == "Live":
                tab_label = f"📺 Live ({len(category_list) if category_list else 0})"
            elif tab_name == "Movies":
                tab_label = f"🎬 Movies ({len(category_list) if category_list else 0})"
            elif tab_name == "Series":
                tab_label = f"📺 Series ({len(category_list) if category_list else 0})"
            else:
                tab_label = f"📂 {tab_name} ({len(category_list) if category_list else 0})"

            self.category_tabs.addTab(list_widget, tab_label)

        # Update status
        if total_categories > 0:
            self.status_label.setText(f"✅ Loaded {total_categories} categories successfully!")
        else:
            self.status_label.setText("⚠️ No categories found - check portal URL and MAC")

    def on_category_item_clicked(self, item, tab_type):
        """Handle category item click - navigate to content"""
        category_data = item.data(Qt.ItemDataRole.UserRole)
        if not category_data:
            return

        # Store navigation state
        self.navigation_stack.append({
            'view': self.current_view,
            'category': self.current_category,
            'series': self.current_series,
            'season': self.current_season,
            'content': self.current_content.copy()
        })

        # Update current state
        self.current_view = "channels"
        self.current_category = category_data

        # Load category content
        self.load_category_content(tab_type, category_data)

    def on_content_item_clicked(self, item):
        """Handle content item click - navigate or play based on type"""
        content_data = item.data(Qt.ItemDataRole.UserRole)
        item_type = content_data.get('item_type', 'unknown')

        if item_type == "Go Back":
            self.go_back()
        elif item_type == "series":
            # Navigate to seasons
            self.navigate_to_seasons(content_data)
        elif item_type == "season":
            # Navigate to episodes
            self.navigate_to_episodes(content_data)
        elif item_type in ["channel", "vod", "episode"]:
            # Play content
            self.play_content_item(content_data)
        else:
            logger.warning(f"Unknown item type: {item_type}")

    def load_category_content(self, tab_type, category_data):
        """Load content for selected category"""
        category_id = category_data.get('id') or category_data.get('category_id')

        if not category_id:
            QMessageBox.warning(self, "Error", "No category ID found")
            return

        # Clear current content and add go back button
        self.content_list.clear()
        self.add_go_back_button()

        # Show loading
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText(f"Loading {tab_type} content...")

        # Start loading content
        portal_url = self.url_input.text().strip()
        mac_address = self.mac_input.text().strip()

        # Prepare URL for StalkerPortal class (remove /stalker_portal if present)
        stalker_portal_url = portal_url
        if stalker_portal_url.endswith('/stalker_portal'):
            stalker_portal_url = stalker_portal_url.replace('/stalker_portal', '')

        self.content_thread = StalkerPlayerThread(
            stalker_portal_url, mac_address, "channels", tab_type, category_id
        )
        self.content_thread.channels_loaded.connect(self.on_channels_loaded)
        self.content_thread.progress_updated.connect(self.progress_bar.setValue)
        self.content_thread.error_occurred.connect(self.on_error)
        self.content_thread.status_updated.connect(self.status_label.setText)
        self.content_thread.finished.connect(self.on_content_finished)
        self.content_thread.start()

    def navigate_to_seasons(self, series_data):
        """Navigate to seasons for a series"""
        movie_id = series_data.get('movie_id') or series_data.get('id')

        if not movie_id:
            QMessageBox.warning(self, "Error", "No movie ID found for series")
            return

        # Store navigation state
        self.navigation_stack.append({
            'view': self.current_view,
            'category': self.current_category,
            'series': self.current_series,
            'season': self.current_season,
            'content': self.current_content.copy()
        })

        # Update current state
        self.current_view = "seasons"
        self.current_series = series_data

        # Clear content and add go back button
        self.content_list.clear()
        self.add_go_back_button()

        # Show loading
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("Loading seasons...")

        # Start loading seasons
        portal_url = self.url_input.text().strip()
        mac_address = self.mac_input.text().strip()
        stalker_portal_url = portal_url
        if stalker_portal_url.endswith('/stalker_portal'):
            stalker_portal_url = stalker_portal_url.replace('/stalker_portal', '')

        self.content_thread = StalkerPlayerThread(
            stalker_portal_url, mac_address, "seasons", movie_id=movie_id
        )
        self.content_thread.seasons_loaded.connect(self.on_seasons_loaded)
        self.content_thread.progress_updated.connect(self.progress_bar.setValue)
        self.content_thread.error_occurred.connect(self.on_seasons_error)
        self.content_thread.status_updated.connect(self.status_label.setText)
        self.content_thread.finished.connect(self.on_content_finished)
        self.content_thread.start()

    def navigate_to_episodes(self, season_data):
        """Navigate to episodes for a season"""
        movie_id = season_data.get('movie_id')
        season_id = season_data.get('season_id') or season_data.get('id')

        if not movie_id or not season_id:
            QMessageBox.warning(self, "Error", "Missing movie ID or season ID")
            return

        # Store navigation state
        self.navigation_stack.append({
            'view': self.current_view,
            'category': self.current_category,
            'series': self.current_series,
            'season': self.current_season,
            'content': self.current_content.copy()
        })

        # Update current state
        self.current_view = "episodes"
        self.current_season = season_data

        # Clear content and add go back button
        self.content_list.clear()
        self.add_go_back_button()

        # Show loading
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("Loading episodes...")

        # Start loading episodes
        portal_url = self.url_input.text().strip()
        mac_address = self.mac_input.text().strip()
        stalker_portal_url = portal_url
        if stalker_portal_url.endswith('/stalker_portal'):
            stalker_portal_url = stalker_portal_url.replace('/stalker_portal', '')

        self.content_thread = StalkerPlayerThread(
            stalker_portal_url, mac_address, "episodes", movie_id=movie_id, season_id=season_id, season_data=season_data
        )
        self.content_thread.episodes_loaded.connect(self.on_episodes_loaded)
        self.content_thread.progress_updated.connect(self.progress_bar.setValue)
        self.content_thread.error_occurred.connect(self.on_episodes_error)
        self.content_thread.status_updated.connect(self.status_label.setText)
        self.content_thread.finished.connect(self.on_content_finished)
        self.content_thread.start()

    def add_go_back_button(self):
        """Add go back button to content list"""
        if self.navigation_stack:
            go_back_item = QListWidgetItem("⬅️ Go Back")
            go_back_item.setData(Qt.ItemDataRole.UserRole, {"item_type": "Go Back"})
            go_back_item.setData(Qt.ItemDataRole.UserRole + 1, "Go Back")
            self.content_list.addItem(go_back_item)

    def go_back(self):
        """Navigate back to previous view"""
        if not self.navigation_stack:
            return

        # Restore previous state
        previous_state = self.navigation_stack.pop()
        self.current_view = previous_state['view']
        self.current_category = previous_state['category']
        self.current_series = previous_state['series']
        self.current_season = previous_state['season']
        self.current_content = previous_state['content']

        # Restore content list
        self.content_list.clear()
        if self.navigation_stack:  # Add go back if there are more levels
            self.add_go_back_button()

        # Restore content
        for content in self.current_content:
            self.add_content_item(content)

        self.status_label.setText(f"📂 Returned to {self.current_view}")

    def add_content_item(self, content):
        """Add a content item to the list with appropriate icon"""
        item_type = content.get('item_type', 'unknown')
        name = content.get('name', content.get('title', 'Unknown'))

        # Add emoji based on item type
        if item_type == "channel":
            display_name = f"📺 {name}"
        elif item_type == "vod":
            display_name = f"🎬 {name}"
        elif item_type == "series":
            display_name = f"📺 {name} (Series)"
        elif item_type == "season":
            display_name = f"📁 {name}"
        elif item_type == "episode":
            episode_num = content.get('episode_number', content.get('series_number', '?'))
            display_name = f"▶️ Episode {episode_num}: {name}"
        else:
            display_name = f"📄 {name}"

        item = QListWidgetItem(display_name)
        item.setData(Qt.ItemDataRole.UserRole, content)
        item.setData(Qt.ItemDataRole.UserRole + 1, item_type)
        self.content_list.addItem(item)

    def on_channels_loaded(self, content_list):
        """Handle loaded channels/content"""
        self.current_content = content_list

        # Clear and add go back button
        self.content_list.clear()
        if self.navigation_stack:
            self.add_go_back_button()

        # Add content items
        for content in content_list:
            self.add_content_item(content)

        # Enable play button if content is available
        if len(content_list) > 0:
            self.play_btn.setEnabled(True)

    def on_seasons_loaded(self, seasons_list):
        """Handle loaded seasons"""
        self.current_content = seasons_list

        # Clear and add go back button
        self.content_list.clear()
        if self.navigation_stack:
            self.add_go_back_button()

        # Add season items
        for season in seasons_list:
            self.add_content_item(season)

        self.status_label.setText(f"📁 Loaded {len(seasons_list)} seasons")

    def on_episodes_loaded(self, episodes_list):
        """Handle loaded episodes"""
        self.current_content = episodes_list

        # Clear and add go back button
        self.content_list.clear()
        if self.navigation_stack:
            self.add_go_back_button()

        # Add episode items
        for episode in episodes_list:
            self.add_content_item(episode)

        # Enable play button if episodes are available
        if len(episodes_list) > 0:
            self.play_btn.setEnabled(True)

        self.status_label.setText(f"▶️ Loaded {len(episodes_list)} episodes")

    def on_content_finished(self):
        """Handle content loading finished"""
        self.progress_bar.setVisible(False)

    def play_content_item(self, content_data):
        """Play specific content item in integrated VLC player"""
        content_name = content_data.get('name', content_data.get('title', 'Unknown'))
        item_type = content_data.get('item_type', 'unknown')

        try:
            # Get stream URL using hybrid approach
            stream_url = self.get_stream_url(content_data)
            if stream_url:
                # Try integrated VLC player first
                if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                    try:
                        # Create media and play in embedded player
                        media = self.vlc_instance.media_new(stream_url)
                        self.vlc_media_player.set_media(media)
                        self.vlc_media_player.play()

                        self.status_label.setText(f"🎬 Playing: {content_name}")
                        self.play_btn.setEnabled(False)
                        self.pause_btn.setEnabled(True)
                        self.stop_btn.setEnabled(True)

                        # Show appropriate message based on content type
                        if item_type == "episode":
                            episode_num = content_data.get('episode_number', '?')
                            season_name = self.current_season.get('name', 'Unknown Season') if self.current_season else 'Unknown Season'
                            message = f"📺 Now playing Episode {episode_num}\n\n{season_name}\n{content_name}"
                        elif item_type == "channel":
                            message = f"📺 Now playing Live TV\n\n{content_name}"
                        elif item_type == "vod":
                            message = f"🎬 Now playing Movie\n\n{content_name}"
                        else:
                            message = f"▶️ Now playing\n\n{content_name}"

                        # Update player widget background to show it's active
                        self.vlc_widget.setStyleSheet(f"""
                            QWidget {{
                                background-color: #1a1a1a;
                                border: 2px solid {Colors.SUCCESS};
                                border-radius: 8px;
                                min-height: 300px;
                            }}
                        """)

                        self.status_label.setText(f"🎬 Playing: {content_name} in integrated player")
                        return

                    except Exception as vlc_error:
                        logger.warning(f"Integrated VLC failed: {vlc_error}, falling back to external player")

                # Fallback to external VLC player
                success = self.vlc_player.play(stream_url)
                if success:
                    self.status_label.setText(f"🎬 Playing: {content_name}")
                    self.play_btn.setEnabled(False)
                    self.pause_btn.setEnabled(True)
                    self.stop_btn.setEnabled(True)

                    # Show appropriate message based on content type
                    if item_type == "episode":
                        episode_num = content_data.get('episode_number', '?')
                        season_name = self.current_season.get('name', 'Unknown Season') if self.current_season else 'Unknown Season'
                        message = f"📺 Now playing Episode {episode_num}\n\n{season_name}\n{content_name}"
                    elif item_type == "channel":
                        message = f"📺 Now playing Live TV\n\n{content_name}"
                    elif item_type == "vod":
                        message = f"🎬 Now playing Movie\n\n{content_name}"
                    else:
                        message = f"▶️ Now playing\n\n{content_name}"

                    QMessageBox.information(
                        self,
                        "Playing",
                        f"{message}\n\nExternal VLC Media Player has been launched.\n"
                        f"Use the VLC controls below to manage playback."
                    )
                else:
                    QMessageBox.critical(self, "Error", "Failed to start VLC player")
            else:
                QMessageBox.warning(self, "Error", "Could not get stream URL")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Playback error: {str(e)}")

    def play_content(self):
        """Play selected content with VLC or resume if paused"""
        try:
            # Check if we have a paused player that can be resumed
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                state = self.vlc_media_player.get_state()
                if state == vlc.State.Paused:
                    # Resume from pause
                    self.vlc_media_player.set_pause(0)  # 0 = resume
                    self.play_btn.setEnabled(False)
                    self.pause_btn.setEnabled(True)
                    self.stop_btn.setEnabled(True)
                    self.status_label.setText("▶️ Resumed playback")
                    logger.info("Stalker VLC player resumed from pause")
                    return

            # No paused content, play selected content
            current_item = self.content_list.currentItem()
            if not current_item:
                QMessageBox.information(self, "Info", "Please select content to play")
                return

            content_data = current_item.data(Qt.ItemDataRole.UserRole)
            item_type = content_data.get('item_type', 'unknown')

            # Handle different item types
            if item_type == "Go Back":
                self.go_back()
            elif item_type == "series":
                self.navigate_to_seasons(content_data)
            elif item_type == "season":
                self.navigate_to_episodes(content_data)
            elif item_type in ["channel", "vod", "episode"]:
                self.play_content_item(content_data)
            else:
                QMessageBox.information(self, "Info", f"Cannot play item of type: {item_type}")

        except Exception as e:
            logger.error(f"Error in play_content: {e}")
            QMessageBox.critical(self, "Error", f"Failed to play content: {str(e)}")

    def get_stream_url(self, content_data):
        """Get stream URL for content using the same approach as STALKER PLAYER.py"""
        try:
            portal_url = self.url_input.text().strip()
            item_type = content_data.get("item_type", "channel").lower()

            # Determine if this is a Stalker Portal
            is_stalker = "/stalker_portal/" in portal_url and hasattr(self, 'portal') and self.portal

            if is_stalker:
                logger.info("Using Stalker portal method for stream URL")

                # Handle episodes specifically for Stalker portals
                if item_type == "episode":
                    movie_id = content_data.get("movie_id")
                    season_id = content_data.get("season_id")
                    episode_id = content_data.get("id")

                    if movie_id and season_id and episode_id:
                        try:
                            stream_url = self.portal.get_episode_stream_url(movie_id, season_id, episode_id)
                            if stream_url:
                                logger.info(f"Got episode stream URL from Stalker portal: {stream_url}")
                                return stream_url
                        except Exception as e:
                            logger.warning(f"Stalker episode stream URL failed: {e}")

                # Handle other Stalker content types (channels, movies)
                try:
                    stream_url = self.portal.get_stream_link(content_data)
                    if stream_url:
                        logger.info(f"Got stream URL from Stalker portal: {stream_url}")
                        return stream_url
                except Exception as e:
                    logger.warning(f"Stalker portal stream URL failed: {e}")

            # Fallback: Use regular portal method (same as STALKER PLAYER.py)
            logger.info("Using regular portal method for stream URL")
            cmd = content_data.get("cmd")
            if not cmd:
                logger.error(f"No command found for content: {content_data}")
                return None

            # Handle different item types for regular portals
            if item_type in ["channel", "vod"]:
                # For channels and movies, check if cmd contains direct URL
                if cmd.startswith("ffmpeg "):
                    # Strip 'ffmpeg ' prefix
                    stream_url = cmd[len("ffmpeg "):].strip()

                    # Fix localhost URLs for live channels
                    if item_type == "channel" and "localhost" in stream_url:
                        import re
                        ch_id_match = re.search(r'/ch/(\d+)_', stream_url)
                        if ch_id_match:
                            ch_id = ch_id_match.group(1)
                            base_url = portal_url.replace('/stalker_portal', '')
                            mac_address = self.mac_input.text().strip()
                            stream_url = f"{base_url}/play/live.php?mac={mac_address}&stream={ch_id}&extension=ts"
                            logger.info(f"Fixed localhost URL to: {stream_url}")

                    logger.info(f"Extracted stream URL from ffmpeg cmd: {stream_url}")
                    return stream_url
                elif cmd.startswith("http"):
                    # Direct URL - but still check for localhost issue
                    stream_url = cmd
                    if item_type == "channel" and "localhost" in stream_url:
                        import re
                        ch_id_match = re.search(r'/ch/(\d+)_', stream_url)
                        if ch_id_match:
                            ch_id = ch_id_match.group(1)
                            base_url = portal_url.replace('/stalker_portal', '')
                            mac_address = self.mac_input.text().strip()
                            stream_url = f"{base_url}/play/live.php?mac={mac_address}&stream={ch_id}&extension=ts"
                            logger.info(f"Fixed localhost URL to: {stream_url}")

                    logger.info(f"Using direct URL from cmd: {stream_url}")
                    return stream_url
                else:
                    # Need to create link via portal API
                    return self.create_regular_portal_link(cmd, item_type, content_data)

            elif item_type == "episode":
                # For episodes in regular portals
                return self.create_regular_portal_link(cmd, item_type, content_data)

            else:
                logger.error(f"Unknown item type: {item_type}")
                return None

        except Exception as e:
            logger.error(f"Error getting stream URL: {e}")
            return None

    def create_regular_portal_link(self, cmd, item_type, content_data):
        """Create stream link for regular portals with proper live channel URL handling"""
        try:
            import requests
            from urllib.parse import quote
            import re

            portal_url = self.url_input.text().strip()
            mac_address = self.mac_input.text().strip()

            # Remove /stalker_portal if present
            base_url = portal_url.replace('/stalker_portal', '')

            # Get token first for authentication
            token = self.get_token_for_portal(base_url, mac_address)
            if not token:
                logger.error("Failed to get token for portal")
                return None

            # Create session with cookies and headers
            session = requests.Session()
            cookies = {
                "mac": mac_address,
                "stb_lang": "en",
                "timezone": "Europe/London",
                "token": token,
            }
            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
                "Authorization": f"Bearer {token}",
                "Accept": "*/*",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
            }

            # Encode the command
            cmd_encoded = quote(cmd)

            # Create the appropriate URL based on item type
            if item_type == "channel":
                # For live channels, use itv type
                create_link_url = f"{base_url}/portal.php?type=itv&action=create_link&cmd={cmd_encoded}&JsHttpRequest=1-xml"
            elif item_type == "episode":
                episode_number = content_data.get("episode_number")
                if episode_number:
                    create_link_url = f"{base_url}/portal.php?type=vod&action=create_link&cmd={cmd_encoded}&series={episode_number}&JsHttpRequest=1-xml"
                else:
                    create_link_url = f"{base_url}/portal.php?type=vod&action=create_link&cmd={cmd_encoded}&JsHttpRequest=1-xml"
            else:
                create_link_url = f"{base_url}/portal.php?type=vod&action=create_link&cmd={cmd_encoded}&JsHttpRequest=1-xml"

            logger.debug(f"Create link URL: {create_link_url}")

            # Make the request
            response = session.get(
                create_link_url,
                cookies=cookies,
                headers=headers,
                timeout=10,
            )
            response.raise_for_status()
            json_response = response.json()
            logger.debug(f"Create link response: {json_response}")

            # Extract the stream URL
            cmd_value = json_response.get("js", {}).get("cmd")
            if cmd_value:
                # Remove any prefix (e.g., 'ffmpeg') and clean the URL
                if isinstance(cmd_value, str):
                    # Remove known prefixes
                    known_prefixes = ["ffmpeg ", "ffrt3 "]
                    for prefix in known_prefixes:
                        if cmd_value.lower().startswith(prefix.lower()):
                            cmd_value = cmd_value[len(prefix):].strip()
                            break

                    # Fix localhost URLs for live channels (same logic as checker.py)
                    if item_type == "channel" and "localhost" in cmd_value:
                        ch_id_match = re.search(r'/ch/(\d+)_', cmd_value)
                        if ch_id_match:
                            ch_id = ch_id_match.group(1)
                            # Create proper live stream URL
                            cmd_value = f"{base_url}/play/live.php?mac={mac_address}&stream={ch_id}&extension=ts"
                            logger.info(f"Fixed localhost URL to: {cmd_value}")

                    logger.info(f"Got stream URL from regular portal: {cmd_value}")
                    return cmd_value
                else:
                    logger.error(f"Unexpected cmd_value type: {type(cmd_value)}")
                    return None
            else:
                logger.error("Stream URL not found in the response")
                return None

        except Exception as e:
            logger.error(f"Error creating regular portal link: {e}")
            return None

    def get_token_for_portal(self, base_url, mac_address):
        """Get authentication token for portal"""
        try:
            import requests

            handshake_url = f"{base_url}/portal.php?type=stb&action=handshake&token=&JsHttpRequest=1-xml"

            cookies = {
                "mac": mac_address,
                "stb_lang": "en",
                "timezone": "Europe/London",
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
            }

            session = requests.Session()
            response = session.get(handshake_url, cookies=cookies, headers=headers, timeout=15)
            response.raise_for_status()

            token = response.json().get("js", {}).get("token")
            if token:
                logger.debug(f"Token retrieved for portal: {token}")
                return token
            else:
                logger.error("Token not found in handshake response")
                return None

        except Exception as e:
            logger.error(f"Error getting token for portal: {e}")
            return None

    def pause_content(self):
        """Pause VLC playback properly"""
        try:
            # Try integrated VLC player first
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                try:
                    # Check current state
                    state = self.vlc_media_player.get_state()

                    if state == vlc.State.Playing:
                        # Pause the video
                        self.vlc_media_player.set_pause(1)  # 1 = pause
                        self.status_label.setText("⏸️ Playback paused")
                        self.play_btn.setEnabled(True)
                        self.pause_btn.setEnabled(False)
                        logger.info("Integrated VLC player paused successfully")
                    else:
                        logger.warning(f"Cannot pause - current state: {state}")
                        self.status_label.setText("⚠️ Cannot pause - not playing")

                except Exception as vlc_error:
                    logger.warning(f"Error pausing integrated VLC: {vlc_error}")
                    self.status_label.setText("⚠️ Pause may not work with this stream")
            else:
                # Fallback message for external VLC
                QMessageBox.information(
                    self,
                    "Pause",
                    "⏸️ To pause/resume playback:\n\n"
                    "• Press SPACEBAR in VLC window\n"
                    "• Or use VLC's built-in controls\n"
                    "• Or click the pause button in VLC"
                )
        except Exception as e:
            logger.error(f"Pause error: {e}")
            self.status_label.setText("❌ Pause error occurred")

    def stop_content(self):
        """Stop VLC playback (completely non-blocking)"""
        try:
            # Update UI immediately to prevent freezing
            self.status_label.setText("⏹️ Stopping...")
            self.play_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

            # Stop VLC in a separate thread to prevent freezing
            from PyQt6.QtCore import QThread, pyqtSignal

            class StopThread(QThread):
                finished_signal = pyqtSignal()

                def __init__(self, vlc_media_player, vlc_player):
                    super().__init__()
                    self.vlc_media_player = vlc_media_player
                    self.vlc_player = vlc_player

                def run(self):
                    try:
                        # Stop integrated VLC player
                        if self.vlc_media_player:
                            self.vlc_media_player.stop()
                            logger.info("Integrated VLC player stopped successfully")
                    except Exception as e:
                        logger.warning(f"Error stopping integrated VLC: {e}")

                    try:
                        # Stop external VLC player
                        if self.vlc_player:
                            self.vlc_player.stop()
                            logger.info("External VLC player stopped successfully")
                    except Exception as e:
                        logger.warning(f"Error stopping external VLC: {e}")

                    finally:
                        self.finished_signal.emit()

            # Create and start stop thread
            vlc_media_player = getattr(self, 'vlc_media_player', None)
            vlc_player = getattr(self, 'vlc_player', None)

            if vlc_media_player or vlc_player:
                self.stop_thread = StopThread(vlc_media_player, vlc_player)
                self.stop_thread.finished_signal.connect(self.on_stop_finished)
                self.stop_thread.start()
            else:
                # No VLC players, just update UI
                self.on_stop_finished()

        except Exception as e:
            logger.error(f"Error stopping content: {e}")
            self.on_stop_finished()

    def on_stop_finished(self):
        """Called when stop operation is finished"""
        try:
            # Reset player widget appearance
            self.vlc_widget.setStyleSheet(f"""
                QWidget {{
                    background-color: black;
                    border: 2px solid {Colors.PRIMARY};
                    border-radius: 8px;
                    min-height: 300px;
                }}
            """)

            # Update UI to final state
            self.status_label.setText("⏹️ Playback stopped")
            self.play_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

        except Exception as e:
            logger.error(f"Error in stop finished: {e}")
            self.status_label.setText("❌ Stop error occurred")
            self.play_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

    def on_seasons_error(self, error_message):
        """Handle seasons loading error with fallback (same logic as STALKER PLAYER.py)"""
        logger.warning(f"Stalker seasons loading failed: {error_message}")

        # Try regular portal fallback for seasons (same as STALKER PLAYER.py)
        if self.current_series:
            series_name = self.current_series.get('name', self.current_series.get('title', 'Unknown Series'))
            logger.info(f"Attempting regular portal fallback for series: {series_name}")

            try:
                # Use regular portal method to fetch seasons (same as STALKER PLAYER.py)
                seasons = self.fetch_seasons_regular_portal(self.current_series)
                if seasons:
                    logger.info(f"Successfully fetched {len(seasons)} seasons using regular portal")
                    self.progress_bar.setVisible(False)
                    self.on_seasons_loaded(seasons)
                    return
                else:
                    logger.warning("No seasons found using regular portal method")
            except Exception as regular_error:
                logger.warning(f"Regular portal seasons fetch also failed: {regular_error}")

            # If seasons fetch fails, try to play series directly (final fallback)
            cmd = self.current_series.get('cmd', '')
            if cmd:
                logger.info(f"Final fallback: Playing series directly: {series_name}")
                self.progress_bar.setVisible(False)

                # Show message explaining the fallback
                QMessageBox.information(
                    self,
                    "Series Playback",
                    f"📺 {series_name}\n\n"
                    f"This portal doesn't support season navigation.\n"
                    f"Playing series directly..."
                )

                # Play the series directly
                self.play_content_item(self.current_series)
                return

        # If all fallbacks fail, show the error
        self.progress_bar.setVisible(False)
        QMessageBox.critical(
            self,
            "Error",
            f"Failed to load seasons:\n\n{error_message}\n\n"
            f"This portal may not support series navigation."
        )
        self.status_label.setText("Failed to load seasons")

    def fetch_seasons_regular_portal(self, series_data):
        """Fetch seasons using regular portal method (same logic as STALKER PLAYER.py)"""
        try:
            import requests

            portal_url = self.url_input.text().strip()
            mac_address = self.mac_input.text().strip()

            # Remove /stalker_portal if present
            base_url = portal_url.replace('/stalker_portal', '')

            # Get series ID
            series_id = series_data.get("id") or series_data.get("movie_id")
            if not series_id:
                logger.error(f"Series ID missing in series data: {series_data}")
                return []

            # Create session with cookies and headers (same as STALKER PLAYER.py)
            session = requests.Session()
            cookies = {"mac": mac_address}
            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
                "Accept": "*/*",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
            }

            # Fetch seasons using regular portal API (same as STALKER PLAYER.py)
            all_seasons = []
            page_number = 0

            while True:
                seasons_url = f"{base_url}/portal.php?type=series&action=get_ordered_list&movie_id={series_id}&season_id=0&episode_id=0&JsHttpRequest=1-xml&p={page_number}"
                logger.debug(f"Fetching seasons URL: {seasons_url}")

                response = session.get(
                    seasons_url,
                    cookies=cookies,
                    headers=headers,
                    timeout=10
                )
                response.raise_for_status()
                json_response = response.json()

                # Extract data from response
                js_data = json_response.get("js", {})
                data = js_data.get("data", [])

                if not data:
                    break

                # Process seasons (filter for is_season=True)
                for item in data:
                    if item.get("is_season") in [True, 1, "1", "true", "True"]:
                        # Add necessary fields for compatibility
                        item["item_type"] = "season"
                        item["season_id"] = item.get("season_id") or item.get("id")
                        item["movie_id"] = series_id
                        all_seasons.append(item)

                # Check if there are more pages
                total_items = js_data.get("total_items", 0)
                max_page_items = js_data.get("max_page_items", 14)
                if len(data) < max_page_items or (page_number + 1) * max_page_items >= total_items:
                    break

                page_number += 1

                # Safety limit to prevent infinite loops
                if page_number > 50:
                    logger.warning("Reached maximum page limit for seasons fetch")
                    break

            # Sort seasons by season_number (same as STALKER PLAYER.py)
            if all_seasons:
                all_seasons.sort(key=lambda x: x.get('season_number', 0))
                logger.info(f"Fetched {len(all_seasons)} seasons using regular portal")

            return all_seasons

        except Exception as e:
            logger.error(f"Error fetching seasons with regular portal: {e}")
            return []

    def fetch_episodes_regular_portal(self, series_data, season_data):
        """Fetch episodes using regular portal method (same logic as STALKER PLAYER.py)"""
        try:
            import requests

            portal_url = self.url_input.text().strip()
            mac_address = self.mac_input.text().strip()

            # Remove /stalker_portal if present
            base_url = portal_url.replace('/stalker_portal', '')

            # Get series and season IDs
            series_id = series_data.get("id") or series_data.get("movie_id")
            season_id = season_data.get("season_id") or season_data.get("id")

            if not series_id or not season_id:
                logger.error(f"Series ID or Season ID missing. Series: {series_data}, Season: {season_data}")
                return []

            # Create session with cookies and headers (same as STALKER PLAYER.py)
            session = requests.Session()
            cookies = {"mac": mac_address}
            headers = {
                "User-Agent": "Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3",
                "Accept": "*/*",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
            }

            # Fetch episodes using regular portal API (same as STALKER PLAYER.py)
            all_episodes = []
            page_number = 0

            while True:
                episodes_url = f"{base_url}/portal.php?type=series&action=get_ordered_list&movie_id={series_id}&season_id={season_id}&episode_id=0&JsHttpRequest=1-xml&p={page_number}"
                logger.debug(f"Fetching episodes URL: {episodes_url}")

                response = session.get(
                    episodes_url,
                    cookies=cookies,
                    headers=headers,
                    timeout=10
                )
                response.raise_for_status()
                json_response = response.json()

                # Extract data from response
                js_data = json_response.get("js", {})
                data = js_data.get("data", [])

                if not data:
                    break

                # Process episodes (filter for episodes, not seasons)
                for item in data:
                    if not item.get("is_season"):  # Episodes have is_season=False or missing
                        # Add necessary fields for compatibility
                        item["item_type"] = "episode"
                        item["season_id"] = season_id
                        item["movie_id"] = series_id
                        item["episode_id"] = item.get("id")
                        all_episodes.append(item)

                # Check if there are more pages
                total_items = js_data.get("total_items", 0)
                max_page_items = js_data.get("max_page_items", 14)
                if len(data) < max_page_items or (page_number + 1) * max_page_items >= total_items:
                    break

                page_number += 1

                # Safety limit to prevent infinite loops
                if page_number > 50:
                    logger.warning("Reached maximum page limit for episodes fetch")
                    break

            # Sort episodes by episode_number (same as STALKER PLAYER.py)
            if all_episodes:
                all_episodes.sort(key=lambda x: x.get('episode_number', 0))
                logger.info(f"Fetched {len(all_episodes)} episodes using regular portal")

            return all_episodes

        except Exception as e:
            logger.error(f"Error fetching episodes with regular portal: {e}")
            return []

    def on_episodes_error(self, error_message):
        """Handle episodes loading error with fallback (same logic as STALKER PLAYER.py)"""
        logger.warning(f"Stalker episodes loading failed: {error_message}")

        # Try regular portal fallback for episodes (same as STALKER PLAYER.py)
        if self.current_series and self.current_season:
            season_name = self.current_season.get('name', 'Unknown Season')
            logger.info(f"Attempting regular portal fallback for episodes in: {season_name}")

            try:
                # Use regular portal method to fetch episodes (same as STALKER PLAYER.py)
                episodes = self.fetch_episodes_regular_portal(self.current_series, self.current_season)
                if episodes:
                    logger.info(f"Successfully fetched {len(episodes)} episodes using regular portal")
                    self.progress_bar.setVisible(False)
                    self.on_episodes_loaded(episodes)
                    return
                else:
                    logger.warning("No episodes found using regular portal method")
            except Exception as regular_error:
                logger.warning(f"Regular portal episodes fetch also failed: {regular_error}")

        # If all fallbacks fail, show the error
        self.progress_bar.setVisible(False)
        QMessageBox.critical(
            self,
            "Error",
            f"Failed to load episodes:\n\n{error_message}\n\n"
            f"This season may not have any episodes available."
        )
        self.status_label.setText("Failed to load episodes")

    def on_error(self, error_message):
        """Handle errors"""
        QMessageBox.critical(self, "Error", error_message)
        self.status_label.setText("Connection failed")

    def on_connection_finished(self):
        """Handle connection finished"""
        self.connect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

    # Context menu methods removed - no longer needed

    def determine_stalker_content_type(self, item_data):
        """Determine the content type for Stalker items"""
        try:
            # Check if it's a series
            if item_data.get('is_series') == '1' or 'series' in item_data.get('category_name', '').lower():
                return 'series'

            # Check if it has a direct stream URL (movie or episode)
            if 'cmd' in item_data or 'url' in item_data:
                # If we're in a series context, it's an episode
                if hasattr(self, 'current_series_id') and self.current_series_id:
                    return 'episode'
                else:
                    return 'movie'

            return 'unknown'

        except Exception as e:
            logger.error(f"Error determining content type: {e}")
            return 'unknown'

    def download_stalker_item(self, item_data, content_type):
        """Download a single Stalker movie or episode"""
        try:
            if not hasattr(self, 'download_manager') or not self.download_manager:
                QMessageBox.warning(
                    self, "Download Manager",
                    "Download manager is not available. Please check your installation."
                )
                return

            # Prepare server info for Stalker - get from input fields
            portal_url = self.url_input.text().strip()
            mac_address = self.mac_input.text().strip()

            # Clean portal URL (remove /stalker_portal if present)
            if portal_url.endswith('/stalker_portal'):
                portal_url = portal_url.replace('/stalker_portal', '')

            server_info = {
                'portal_url': portal_url,
                'mac_address': mac_address,
                'device_id': getattr(self.current_portal, 'device_id', '') if self.current_portal else '',
                'device_id2': getattr(self.current_portal, 'device_id2', '') if self.current_portal else ''
            }

            # Debug logging
            logger.info(f"Stalker download server info: portal_url='{portal_url}', mac_address='{mac_address}'")

            # Handle download request
            self.handle_download_request(item_data, server_info, 'stalker')

        except Exception as e:
            logger.error(f"Error downloading Stalker item: {e}")
            QMessageBox.critical(
                self, "Download Error",
                f"Failed to start download: {str(e)}"
            )

    def download_stalker_series_batch(self, series_data):
        """Download all episodes of a Stalker series"""
        try:
            if not hasattr(self, 'download_manager') or not self.download_manager:
                QMessageBox.warning(
                    self, "Download Manager",
                    "Download manager is not available. Please check your installation."
                )
                return

            # Show confirmation dialog
            reply = QMessageBox.question(
                self, "Batch Download",
                f"Do you want to download all episodes of '{series_data.get('name', 'Unknown')}'?\n\n"
                "This will fetch all seasons and episodes and add them to the download queue.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                QMessageBox.information(
                    self, "Batch Download",
                    "Stalker series batch download is being implemented.\n"
                    "For now, please browse to individual episodes and download them one by one."
                )

        except Exception as e:
            logger.error(f"Error starting Stalker batch download: {e}")
            QMessageBox.critical(
                self, "Batch Download Error",
                f"Failed to start batch download: {str(e)}"
            )

    def set_download_manager(self, download_manager):
        """Set the download manager instance"""
        if DOWNLOAD_AVAILABLE:
            self.setup_download_manager(download_manager)

    def toggle_mute(self):
        """Toggle mute/unmute"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Get current mute state
                is_muted = self.vlc_media_player.audio_get_mute()

                # Toggle mute
                self.vlc_media_player.audio_set_mute(not is_muted)

                # Update button icon
                if not is_muted:  # Now muted
                    self.volume_btn.setText("🔇")
                    self.volume_btn.setToolTip("Unmute")
                    self.status_label.setText("🔇 Audio muted")
                else:  # Now unmuted
                    self.volume_btn.setText("🔊")
                    self.volume_btn.setToolTip("Mute")
                    self.status_label.setText("🔊 Audio unmuted")

                logger.info(f"Audio {'muted' if not is_muted else 'unmuted'}")
            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error toggling mute: {e}")
            self.status_label.setText("❌ Error controlling audio")

    def change_volume(self, value):
        """Change volume level"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Set volume (0-100)
                self.vlc_media_player.audio_set_volume(value)

                # Update volume button icon based on level
                if value == 0:
                    self.volume_btn.setText("🔇")
                elif value < 30:
                    self.volume_btn.setText("🔈")
                elif value < 70:
                    self.volume_btn.setText("🔉")
                else:
                    self.volume_btn.setText("🔊")

                # Update status
                self.status_label.setText(f"🔊 Volume: {value}%")
                logger.debug(f"Volume set to {value}%")
            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error changing volume: {e}")
            self.status_label.setText("❌ Error controlling volume")

    def toggle_subtitles(self):
        """Show subtitle track selection menu"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Get available subtitle tracks
                subtitle_count = self.vlc_media_player.video_get_spu_count()
                current_track = self.vlc_media_player.video_get_spu()

                # Create context menu for subtitle selection
                from PyQt6.QtWidgets import QMenu
                from PyQt6.QtGui import QAction

                menu = QMenu(self)
                menu.setStyleSheet(f"""
                    QMenu {{
                        background-color: {Colors.SURFACE};
                        color: {Colors.TEXT};
                        border: 2px solid {Colors.PRIMARY};
                        border-radius: 6px;
                        padding: 5px;
                    }}
                    QMenu::item {{
                        background-color: transparent;
                        padding: 8px 20px;
                        border-radius: 4px;
                    }}
                    QMenu::item:selected {{
                        background-color: {Colors.PRIMARY};
                        color: white;
                    }}
                    QMenu::item:checked {{
                        background-color: {Colors.SUCCESS};
                        color: white;
                        font-weight: bold;
                    }}
                """)

                # Add "Disable Subtitles" option
                disable_action = QAction("🚫 Disable Subtitles", self)
                disable_action.setCheckable(True)
                disable_action.setChecked(current_track == -1)
                disable_action.triggered.connect(lambda: self.set_subtitle_track(-1))
                menu.addAction(disable_action)

                if subtitle_count > 0:
                    menu.addSeparator()

                    # Add each available subtitle track
                    for i in range(subtitle_count):
                        # Try to get track description
                        track_description = self.get_subtitle_track_description(i)
                        if track_description:
                            action_text = f"📝 Track {i + 1}: {track_description}"
                        else:
                            action_text = f"📝 Subtitle Track {i + 1}"

                        track_action = QAction(action_text, self)
                        track_action.setCheckable(True)
                        track_action.setChecked(current_track == i)
                        track_action.triggered.connect(lambda checked, track=i: self.set_subtitle_track(track))
                        menu.addAction(track_action)

                    # Show track count in status
                    self.status_label.setText(f"📝 {subtitle_count} subtitle tracks available")
                else:
                    # No embedded tracks available
                    no_tracks_action = QAction("📝 No embedded tracks", self)
                    no_tracks_action.setEnabled(False)
                    menu.addAction(no_tracks_action)
                    self.status_label.setText("📝 No embedded subtitle tracks")

                # Add separator and external file option
                menu.addSeparator()
                load_external_action = QAction("📁 Load External Subtitle File...", self)
                load_external_action.triggered.connect(self.load_subtitle_file)
                menu.addAction(load_external_action)

                # Show menu at button position
                button_pos = self.subtitle_btn.mapToGlobal(self.subtitle_btn.rect().bottomLeft())
                menu.exec(button_pos)

            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error showing subtitle menu: {e}")
            self.status_label.setText("❌ Error accessing subtitles")

    def get_subtitle_track_description(self, track_index):
        """Get description for a subtitle track"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                # Try to get track description from VLC
                track_descriptions = self.vlc_media_player.video_get_spu_description()
                if track_descriptions and len(track_descriptions) > track_index + 1:  # +1 because first entry is "Disable"
                    # track_descriptions format: [(id, name), ...]
                    track_info = track_descriptions[track_index + 1]  # Skip "Disable" entry
                    if len(track_info) > 1 and track_info[1]:
                        return track_info[1].decode('utf-8') if isinstance(track_info[1], bytes) else str(track_info[1])

                # Fallback: try to detect language from track
                return self.detect_subtitle_language(track_index)

        except Exception as e:
            logger.debug(f"Could not get subtitle track description: {e}")

        return None

    def detect_subtitle_language(self, track_index):
        """Try to detect subtitle language"""
        try:
            # Common language codes and names
            common_languages = {
                'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
                'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ar': 'Arabic',
                'zh': 'Chinese', 'ja': 'Japanese', 'ko': 'Korean', 'hi': 'Hindi',
                'tr': 'Turkish', 'pl': 'Polish', 'nl': 'Dutch', 'sv': 'Swedish',
                'da': 'Danish', 'no': 'Norwegian', 'fi': 'Finnish', 'he': 'Hebrew'
            }

            # This is a basic implementation - in a real scenario, you might
            # analyze the subtitle content or use VLC's language detection
            if track_index == 0:
                return "Default"
            elif track_index == 1:
                return "Secondary"
            else:
                return f"Track {track_index + 1}"

        except Exception:
            return None

    def set_subtitle_track(self, track_index):
        """Set specific subtitle track"""
        try:
            if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                self.vlc_media_player.video_set_spu(track_index)

                if track_index == -1:
                    self.subtitle_btn.setText("📝")
                    self.status_label.setText("📝 Subtitles disabled")
                    logger.info("Subtitles disabled")
                else:
                    self.subtitle_btn.setText("📝")
                    track_desc = self.get_subtitle_track_description(track_index)
                    if track_desc:
                        self.status_label.setText(f"📝 Active: {track_desc}")
                        logger.info(f"Subtitle track set to: {track_desc}")
                    else:
                        self.status_label.setText(f"📝 Active: Track {track_index + 1}")
                        logger.info(f"Subtitle track set to: {track_index + 1}")
            else:
                self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error setting subtitle track: {e}")
            self.status_label.setText("❌ Error setting subtitle track")

    def load_subtitle_file(self):
        """Load external subtitle file"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            # Open file dialog for subtitle files
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Select Subtitle File",
                "",
                "Subtitle Files (*.srt *.vtt *.ass *.ssa *.sub *.idx);;All Files (*)"
            )

            if file_path:
                if hasattr(self, 'vlc_media_player') and self.vlc_media_player:
                    # Add subtitle file to VLC
                    result = self.vlc_media_player.video_set_subtitle_file(file_path)

                    if result == 0:  # Success
                        self.subtitle_btn.setText("📝")
                        self.status_label.setText(f"📝 Loaded: {os.path.basename(file_path)}")
                        logger.info(f"Loaded subtitle file: {file_path}")
                    else:
                        self.status_label.setText("❌ Failed to load subtitle file")
                        logger.error(f"Failed to load subtitle file: {file_path}")
                else:
                    self.status_label.setText("❌ Player not available")

        except Exception as e:
            logger.error(f"Error loading subtitle file: {e}")
            self.status_label.setText("❌ Error loading subtitle file")
