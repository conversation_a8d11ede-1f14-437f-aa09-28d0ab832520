"""
Download Integration Module
Integrates the download manager with Stalker and Xtream players
"""

import uuid
import re
import base64
import json
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin

from download_manager import DownloadItem
from download_manager_gui import DownloadManagerWidget

import logging
logger = logging.getLogger(__name__)

class DownloadIntegration:
    """Handles integration between players and download manager"""
    
    def __init__(self, download_manager: DownloadManagerWidget):
        self.download_manager = download_manager
    
    def create_download_from_xtream_content(self, content_data: Dict, server_info: Dict) -> Optional[DownloadItem]:
        """Create download item from Xtream-Codes content"""
        try:
            # Extract content information
            stream_id = content_data.get('stream_id') or content_data.get('series_id')
            title = content_data.get('name', 'Unknown')
            container_extension = content_data.get('container_extension', 'mp4')
            
            # Determine content type
            if 'series_id' in content_data or content_data.get('type') == 'series':
                content_type = 'episode'
                # For series, we need season and episode info
                season = content_data.get('season', '1')
                episode = content_data.get('episode_num', '1')
            else:
                content_type = 'movie'
                season = ""
                episode = ""
            
            # Build download URL - handle both direct URLs and stream IDs
            download_url = self.build_xtream_download_url(
                content_data, server_info, content_type, container_extension
            )

            # Debug logging
            logger.info(f"Created Xtream download item: {title}")
            logger.info(f"Original content URL: {content_data.get('url', 'N/A')}")
            logger.info(f"Built download URL: {download_url}")
            
            # Create download item
            download_item = DownloadItem(
                id=str(uuid.uuid4()),
                title=title,
                url=download_url,
                server_type='xtream',
                content_type=content_type,
                file_extension=container_extension,
                file_size=0,  # Will be determined during download
                duration=content_data.get('duration', ''),
                quality=self.extract_quality_from_title(title),
                season=season,
                episode=episode,
                year=str(content_data.get('year', '')),
                genre=content_data.get('category_name', ''),
                description=content_data.get('plot', ''),
                poster_url=content_data.get('stream_icon', ''),
                subtitle_url=""  # Xtream doesn't typically provide subtitle URLs
            )
            
            return download_item
            
        except Exception as e:
            logger.error(f"Error creating Xtream download item: {e}")
            return None

    def build_xtream_download_url(self, content_data: Dict, server_info: Dict, content_type: str, container_extension: str) -> str:
        """Build proper Xtream download URL"""
        try:
            server_url = server_info.get('server_url', '').rstrip('/')
            username = server_info.get('username', '')
            password = server_info.get('password', '')

            # Check if we have a direct URL in the content data
            if 'url' in content_data:
                url = content_data['url']
                # If it's already a complete URL, return it
                if url.startswith('http'):
                    return url
                # If it's a relative path, build the full URL
                elif url.startswith('/'):
                    return f"{server_url}{url}"
                # If it's encoded data, try to decode it
                else:
                    logger.info(f"Attempting to decode URL: {url}")
                    decoded_url = self.decode_xtream_url(url, server_info)
                    if decoded_url:
                        logger.info(f"Successfully decoded URL: {decoded_url}")
                        return decoded_url
                    else:
                        logger.warning(f"Failed to decode URL: {url}")

            # Get stream ID
            stream_id = content_data.get('stream_id') or content_data.get('series_id')
            if not stream_id:
                # Try to extract from other fields
                stream_id = content_data.get('id')

            if not stream_id:
                raise ValueError("No stream ID found in content data")

            # Build standard Xtream URL
            if content_type == 'movie':
                return f"{server_url}/movie/{username}/{password}/{stream_id}.{container_extension}"
            else:
                return f"{server_url}/series/{username}/{password}/{stream_id}.{container_extension}"

        except Exception as e:
            logger.error(f"Error building Xtream download URL: {e}")
            # Fallback: try to construct a basic URL
            server_url = server_info.get('server_url', '').rstrip('/')
            username = server_info.get('username', '')
            password = server_info.get('password', '')
            stream_id = content_data.get('stream_id') or content_data.get('id', 'unknown')

            if content_type == 'movie':
                return f"{server_url}/movie/{username}/{password}/{stream_id}.{container_extension}"
            else:
                return f"{server_url}/series/{username}/{password}/{stream_id}.{container_extension}"

    def decode_xtream_url(self, encoded_url: str, server_info: Dict) -> Optional[str]:
        """Decode encoded Xtream URL"""
        try:
            # Try base64 decoding
            try:
                decoded_bytes = base64.b64decode(encoded_url + '==')  # Add padding if needed
                decoded_str = decoded_bytes.decode('utf-8')

                # Try to parse as JSON
                try:
                    decoded_data = json.loads(decoded_str)
                    logger.info(f"Decoded Xtream data: {decoded_data}")

                    # Extract information from decoded data
                    stream_type = decoded_data.get('type', 'movie')
                    stream_id = decoded_data.get('stream_id')
                    container = decoded_data.get('target_container', 'mp4')

                    # Parse container - it might be a JSON string like '["mkv"]'
                    container_ext = 'mp4'  # default
                    if isinstance(container, str):
                        if container.startswith('[') and container.endswith(']'):
                            # It's a JSON array string, parse it
                            try:
                                container_list = json.loads(container)
                                if container_list and isinstance(container_list, list):
                                    container_ext = container_list[0].strip('"')
                            except json.JSONDecodeError:
                                # Fallback: extract manually
                                container_ext = container.strip('[]"').split(',')[0].strip('"')
                        else:
                            container_ext = container.strip('"')
                    elif isinstance(container, list) and container:
                        container_ext = container[0].strip('"')

                    # Build URL from decoded data
                    server_url = server_info.get('server_url', '').rstrip('/')
                    username = server_info.get('username', '')
                    password = server_info.get('password', '')

                    # Check if server_url is valid
                    if not server_url or server_url.strip() == '':
                        logger.warning("Empty server URL in Xtream URL decoding")
                        return None

                    if stream_type == 'movie':
                        return f"{server_url}/movie/{username}/{password}/{stream_id}.{container_ext}"
                    else:
                        return f"{server_url}/series/{username}/{password}/{stream_id}.{container_ext}"

                except json.JSONDecodeError:
                    # Not JSON, might be a direct URL
                    if decoded_str.startswith('http'):
                        return decoded_str

            except Exception:
                pass

            # If base64 decoding fails, try other methods
            # Check if it's a URL-encoded string
            try:
                from urllib.parse import unquote
                decoded_url = unquote(encoded_url)
                if decoded_url.startswith('http'):
                    return decoded_url
            except Exception:
                pass

            return None

        except Exception as e:
            logger.error(f"Error decoding Xtream URL: {e}")
            return None
    
    def create_download_from_stalker_content(self, content_data: Dict, server_info: Dict) -> Optional[DownloadItem]:
        """Create download item from Stalker portal content"""
        try:
            # Extract content information
            item_id = content_data.get('id') or content_data.get('movie_id')
            title = content_data.get('name', 'Unknown')
            
            # Determine file extension from cmd or use default
            cmd = content_data.get('cmd', '')
            file_extension = self.extract_extension_from_cmd(cmd) or 'mp4'
            
            # Determine content type
            is_series = content_data.get('is_series', '0') == '1'
            content_type = 'episode' if is_series else 'movie'
            
            # For series, extract season/episode info
            if is_series:
                season = content_data.get('season', '1')
                episode = content_data.get('episode', '1')
            else:
                season = ""
                episode = ""
            
            # Build download URL - handle both direct URLs and encoded URLs
            download_url = self.build_stalker_download_url(content_data, server_info)

            # Check if we got a valid URL
            if not download_url:
                logger.error(f"Could not build download URL for Stalker content: {title}")
                return None

            # Create download item
            download_item = DownloadItem(
                id=str(uuid.uuid4()),
                title=title,
                url=download_url,
                server_type='stalker',
                content_type=content_type,
                file_extension=file_extension,
                file_size=0,  # Will be determined during download
                duration=content_data.get('duration', ''),
                quality=self.extract_quality_from_title(title),
                season=season,
                episode=episode,
                year=str(content_data.get('year', '')),
                genre=content_data.get('genre_name', ''),
                description=content_data.get('description', ''),
                poster_url=content_data.get('screenshot_uri', ''),
                subtitle_url=""  # Will be populated if available
            )
            
            return download_item
            
        except Exception as e:
            logger.error(f"Error creating Stalker download item: {e}")
            return None

    def build_stalker_download_url(self, content_data: Dict, server_info: Dict) -> str:
        """Build proper Stalker download URL using the same method as the player"""
        try:
            # Get basic info
            portal_url = server_info.get('portal_url', '').rstrip('/')
            mac_address = server_info.get('mac_address', '')

            # First, check if we have a direct URL
            url = content_data.get('url', '')
            cmd = content_data.get('cmd', '')

            # Try URL first, then cmd
            potential_url = url or cmd

            # Check if it's already a proper HTTP URL
            if potential_url and potential_url.startswith('http'):
                return potential_url

            # Get content information
            movie_id = content_data.get('id') or content_data.get('movie_id', '')
            is_series = content_data.get('is_series', '0') == '1'

            # For series, we need season and episode info
            if is_series:
                season_id = content_data.get('season_id', '')
                episode_id = content_data.get('episode_id', '') or content_data.get('id', '')

                if movie_id and season_id and episode_id:
                    # Use the same method as the Stalker player for episodes
                    logger.info(f"Getting episode stream URL for movie_id={movie_id}, season_id={season_id}, episode_id={episode_id}")
                    return self.get_stalker_episode_stream_url(portal_url, mac_address, movie_id, season_id, episode_id)
                else:
                    logger.warning(f"Missing series info: movie_id={movie_id}, season_id={season_id}, episode_id={episode_id}")

            # For movies or if we have movie_id
            if movie_id:
                # Use the same method as the Stalker player for movies
                logger.info(f"Getting movie stream URL for movie_id={movie_id}")
                return self.get_stalker_movie_stream_url(portal_url, mac_address, movie_id)

            # Check if it's an encoded URL (could be Stalker or Xtream style)
            if potential_url and not potential_url.startswith('http') and len(potential_url) > 20:
                # First try Stalker-specific decoding
                stalker_url = self.decode_stalker_url(potential_url, server_info)
                if stalker_url:
                    logger.info(f"Successfully decoded Stalker URL: {stalker_url}")
                    return stalker_url

                # Fallback: try Xtream-style decoding but fix the server URL
                xtream_url = self.decode_xtream_url(potential_url, {
                    'server_url': portal_url,
                    'username': '',  # Stalker doesn't use username/password
                    'password': ''
                })

                if xtream_url:
                    # Fix the URL if it's missing the server part
                    if xtream_url.startswith('/movie/') or xtream_url.startswith('/series/'):
                        xtream_url = f"{portal_url}{xtream_url}"
                    logger.info(f"Successfully decoded as Xtream-style URL: {xtream_url}")
                    return xtream_url

            # If we have cmd, try to use it directly
            if cmd:
                # If it's a relative path, build full URL
                if cmd.startswith('/'):
                    return f"{portal_url}{cmd}"
                else:
                    # Strip ffmpeg prefix if present
                    if cmd.lower().startswith('ffmpeg '):
                        cmd = cmd[7:].strip()
                    return cmd

            # Last resort: return the original URL/cmd
            return potential_url or ''

        except Exception as e:
            logger.error(f"Error building Stalker download URL: {e}")
            # Fallback: return the original URL/cmd
            return content_data.get('url') or content_data.get('cmd', '')

    def decode_stalker_url(self, encoded_url: str, server_info: Dict) -> Optional[str]:
        """Decode Stalker-specific encoded URLs"""
        try:
            portal_url = server_info.get('portal_url', '').rstrip('/')
            mac_address = server_info.get('mac_address', '')

            # Try base64 decoding first
            try:
                decoded_bytes = base64.b64decode(encoded_url + '==')
                decoded_str = decoded_bytes.decode('utf-8')

                # Check if it's a Stalker stream URL or command
                if 'stalker_portal' in decoded_str or 'load.php' in decoded_str:
                    # It's already a Stalker URL
                    return decoded_str

                # Try to parse as JSON (might be Stalker metadata)
                try:
                    decoded_data = json.loads(decoded_str)

                    # Extract Stalker-specific info
                    stream_id = decoded_data.get('id') or decoded_data.get('stream_id')
                    cmd = decoded_data.get('cmd', '')

                    if stream_id or cmd:
                        # Build Stalker stream URL
                        return self.build_stalker_stream_url(
                            portal_url, mac_address, stream_id, 'movie', cmd
                        )

                except json.JSONDecodeError:
                    # Not JSON, might be a direct URL or command
                    if decoded_str.startswith('http'):
                        return decoded_str
                    elif decoded_str:
                        # Treat as cmd and build Stalker URL
                        return self.build_stalker_stream_url(
                            portal_url, mac_address, '', 'movie', decoded_str
                        )

            except Exception:
                pass

            # Try URL decoding
            try:
                from urllib.parse import unquote
                decoded_url = unquote(encoded_url)
                if decoded_url.startswith('http'):
                    return decoded_url
            except Exception:
                pass

            return None

        except Exception as e:
            logger.error(f"Error decoding Stalker URL: {e}")
            return None

    def build_stalker_stream_url(self, portal_url: str, mac_address: str, stream_id: str, content_type: str, cmd: str) -> Optional[str]:
        """Build Stalker portal stream URL using proper API"""
        try:
            if not portal_url or not mac_address:
                logger.warning("Missing portal URL or MAC address for Stalker stream")
                return None

            # Clean portal URL
            base_portal_url = portal_url.replace('/stalker_portal', '').rstrip('/')

            # For Stalker downloads, we need to get the actual stream URL
            # This requires making an API call to create_link
            return self.get_stalker_stream_url(base_portal_url, mac_address, stream_id, content_type, cmd)

        except Exception as e:
            logger.error(f"Error building Stalker stream URL: {e}")
            return None

    def get_stalker_stream_url(self, portal_url: str, mac_address: str, stream_id: str, content_type: str, cmd: str) -> Optional[str]:
        """Get actual Stalker stream URL by calling the portal API"""
        try:
            import requests
            from urllib.parse import quote

            # Detect portal type and structure
            portal_type = self.detect_stalker_portal_type(portal_url)
            logger.info(f"Detected portal type: {portal_type} for {portal_url}")

            # Step 1: Get token (simplified handshake)
            token = self.get_stalker_token(portal_url, mac_address, portal_type)
            if not token:
                logger.warning("Could not get Stalker token, trying without authentication")

            # Step 2: Create stream link using appropriate API endpoint
            api_endpoints = self.get_stalker_api_endpoints(portal_url, portal_type)

            # Build proper cmd for VOD
            if stream_id and not cmd:
                cmd = f"/media/file_{stream_id}.mpg"

            # Try different API endpoints
            for api_url in api_endpoints:
                try:
                    # Parameters for create_link
                    params = {
                        'action': 'create_link',
                        'type': 'vod' if content_type == 'movie' else 'vod',
                        'cmd': cmd,
                        'JsHttpRequest': '1-xml'
                    }

                    # Headers with MAC address
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3',
                        'Accept': '*/*',
                        'Referer': f"{portal_url}/c/index.html" if portal_type == 'mag' else f"{portal_url}/stalker_portal/c/index.html",
                        'Cookie': f'mac={quote(mac_address)}; stb_lang=en; timezone=Europe/London'
                    }

                    if token:
                        headers['Authorization'] = f'Bearer {token}'
                        headers['Cookie'] += f'; token={quote(token)}'

                    logger.info(f"Trying Stalker API: {api_url} with params: {params}")

                    # Make API call
                    response = requests.get(api_url, params=params, headers=headers, timeout=10)
                    response.raise_for_status()

                    # Parse response
                    json_response = response.json()
                    if 'js' in json_response:
                        js_data = json_response['js']

                        # Get stream URL from response
                        stream_url = js_data.get('url') or js_data.get('cmd')

                        if stream_url:
                            # If it's a relative path, make it absolute
                            if stream_url.startswith('/'):
                                stream_url = f"{portal_url.rstrip('/')}{stream_url}"

                            logger.info(f"Got Stalker stream URL: {stream_url}")
                            return stream_url
                        else:
                            logger.warning(f"No stream URL in Stalker response: {js_data}")

                except requests.exceptions.RequestException as e:
                    logger.warning(f"API endpoint {api_url} failed: {e}")
                    continue

            # All API endpoints failed, try fallback URLs
            fallback_urls = self.get_stalker_fallback_urls(portal_url, stream_id, portal_type)

            for fallback_url in fallback_urls:
                try:
                    # Test if the fallback URL exists
                    response = requests.head(fallback_url, timeout=5)
                    if response.status_code == 200:
                        logger.info(f"Using working fallback URL: {fallback_url}")
                        return fallback_url
                except:
                    continue

            # If no fallback works, return the first one anyway (might work during actual download)
            if fallback_urls:
                logger.info(f"Using untested fallback URL: {fallback_urls[0]}")
                return fallback_urls[0]

            return None

        except Exception as e:
            logger.error(f"Error getting Stalker stream URL: {e}")
            # Last resort fallback
            if stream_id:
                fallback_urls = self.get_stalker_fallback_urls(portal_url, stream_id, 'standard', mac_address, content_type)
                if fallback_urls:
                    logger.info(f"Using emergency fallback URL: {fallback_urls[0]}")
                    return fallback_urls[0]
            return None

    def detect_stalker_portal_type(self, portal_url: str) -> str:
        """Detect the type of Stalker portal based on URL structure"""
        portal_url_lower = portal_url.lower()

        # MAG device portals - detect by various indicators
        mag_indicators = [
            '/c/' in portal_url_lower,
            portal_url_lower.endswith('/c'),
            'mag.' in portal_url_lower,  # mag.domain.com
            '.mag' in portal_url_lower,  # domain.mag.com
            'ukhd.tv' in portal_url_lower,  # Your specific portal
            'iptvwings.com' in portal_url_lower,  # From your example
        ]

        if any(mag_indicators):
            return 'mag'

        # Standard Stalker portals
        if '/stalker_portal' in portal_url_lower:
            return 'standard'

        # Ministra/Stalker middleware
        if 'ministra' in portal_url_lower or 'middleware' in portal_url_lower:
            return 'ministra'

        # Default to MAG for unknown portals (since most modern portals are MAG-based)
        return 'mag'

    def get_stalker_api_endpoints(self, portal_url: str, portal_type: str) -> List[str]:
        """Get possible API endpoints based on portal type"""
        base_url = portal_url.rstrip('/')
        endpoints = []

        if portal_type == 'mag':
            # MAG device endpoints
            endpoints.extend([
                f"{base_url}/server/load.php",
                f"{base_url}/c/server/load.php",
                f"{base_url}/stalker_portal/server/load.php"
            ])
        elif portal_type == 'ministra':
            # Ministra endpoints
            endpoints.extend([
                f"{base_url}/stalker_portal/server/load.php",
                f"{base_url}/middleware/server/load.php",
                f"{base_url}/server/load.php"
            ])
        else:
            # Standard Stalker endpoints
            endpoints.extend([
                f"{base_url}/stalker_portal/server/load.php",
                f"{base_url}/server/load.php"
            ])

        return endpoints

    def get_stalker_fallback_urls(self, portal_url: str, stream_id: str, portal_type: str, mac_address: str = '', content_type: str = 'movie') -> List[str]:
        """Get possible fallback stream URLs based on portal type"""
        base_url = portal_url.rstrip('/')
        fallback_urls = []

        if not stream_id:
            return fallback_urls

        if portal_type == 'mag':
            # MAG device URLs - use the correct play/movie.php format
            if mac_address:
                # Generate a simple play token (in real implementation, this should come from the portal)
                play_token = self.generate_play_token(stream_id, mac_address)

                # Ensure we use the correct base URL with /c/ path for MAG portals
                # MAG portals typically need /c/ path for downloads
                if '/c/' in portal_url or portal_url.endswith('/c'):
                    # Use the full portal URL as base (includes /c/)
                    mag_base_url = base_url
                else:
                    # Add /c/ for MAG portals (most MAG portals need this)
                    mag_base_url = f"{base_url}/c"
                    logger.info(f"Added /c/ path to MAG portal URL: {mag_base_url}")

                # Generate URLs in the exact format you want: /play/movie.php
                # This is the format that works: http://mag.ukhd.tv:80/play/movie.php?mac=00:1A:79:3D:9C:70&stream=191092.mkv&play_token=2evLAUtMCk&type=movie

                # Format 1: Standard MAG play URL with token (primary format)
                mag_url = f"{mag_base_url}/play/movie.php?mac={mac_address}&stream={stream_id}.mp4&play_token={play_token}&type={content_type}"
                fallback_urls.append(mag_url)

                # Format 2: Try with different extensions
                for ext in ['mkv', 'avi', 'ts', 'mov']:
                    mag_url_ext = f"{mag_base_url}/play/movie.php?mac={mac_address}&stream={stream_id}.{ext}&play_token={play_token}&type={content_type}"
                    fallback_urls.append(mag_url_ext)

                # Format 3: Try without play_token (some portals don't need it)
                mag_url_no_token = f"{mag_base_url}/play/movie.php?mac={mac_address}&stream={stream_id}.mp4&type={content_type}"
                fallback_urls.append(mag_url_no_token)

                # Format 4: Try without /c/ path as backup
                if '/c' in mag_base_url:
                    backup_base = base_url.replace('/c', '')
                    backup_url = f"{backup_base}/play/movie.php?mac={mac_address}&stream={stream_id}.mp4&play_token={play_token}&type={content_type}"
                    fallback_urls.append(backup_url)



            # Traditional MAG fallback URLs as backup
            fallback_urls.extend([
                f"{base_url}/media/file_{stream_id}.mpg",
                f"{base_url}/c/media/file_{stream_id}.mpg",
                f"{base_url}/vod/file_{stream_id}.mpg",
                f"{base_url}/movies/file_{stream_id}.mpg",
                f"{base_url}/stream/file_{stream_id}.mpg"
            ])
        elif portal_type == 'ministra':
            # Ministra fallback URLs
            fallback_urls.extend([
                f"{base_url}/vod4/media/file_{stream_id}.mpg",
                f"{base_url}/media/file_{stream_id}.mpg",
                f"{base_url}/stalker_portal/misc/file_{stream_id}.mpg"
            ])
        else:
            # Standard Stalker fallback URLs
            fallback_urls.extend([
                f"{base_url}/vod4/media/file_{stream_id}.mpg",
                f"{base_url}/media/file_{stream_id}.mpg",
                f"{base_url}/stalker_portal/misc/file_{stream_id}.mpg"
            ])

        # Test URLs to find working ones
        working_urls = self.test_fallback_urls(fallback_urls)
        if working_urls:
            logger.info(f"Found {len(working_urls)} working URLs out of {len(fallback_urls)} tested")
            # Return only the first working URL to ensure consistency
            return [working_urls[0]]
        else:
            logger.info(f"No URLs passed GET testing, using prioritized fallback order")
            # Return URLs in priority order - direct stream URLs first
            return fallback_urls

    def test_fallback_urls(self, urls: List[str]) -> List[str]:
        """Test fallback URLs to find working ones using the same method as download"""
        working_urls = []

        if not urls:
            return working_urls

        try:
            import requests

            # Test first few URLs to avoid too many requests
            test_urls = urls[:5]  # Test first 5 URLs

            for url in test_urls:
                try:
                    # Use VLC User-Agent like M3U-Downloader (key improvement!)
                    headers = {
                        'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16',  # This is the key difference!
                        'Accept': '*/*',
                        'Connection': 'keep-alive'
                    }

                    # Add referer for MAG portals
                    if 'mag.' in url or '/c/' in url:
                        from urllib.parse import urlparse
                        parsed = urlparse(url)
                        base_url = f"{parsed.scheme}://{parsed.netloc}"
                        headers['Referer'] = f"{base_url}/c/index.html"

                    # Make a GET request with range header to test actual download capability
                    headers['Range'] = 'bytes=0-1023'  # Request first 1KB to test

                    response = requests.get(url, headers=headers, timeout=5, allow_redirects=True)

                    # Consider URL working if it doesn't return 4xx or 5xx errors
                    if response.status_code in [200, 206]:  # 206 = Partial Content (range request)
                        logger.info(f"Working URL found: {url} (status: {response.status_code})")
                        working_urls.append(url)
                    elif response.status_code == 404:
                        logger.debug(f"URL not found: {url}")
                    elif response.status_code == 551:
                        logger.debug(f"Authentication error for URL: {url}")
                    elif response.status_code >= 500:
                        logger.debug(f"Server error for URL: {url} (status: {response.status_code})")
                    else:
                        logger.debug(f"URL failed: {url} (status: {response.status_code})")

                except requests.exceptions.RequestException as e:
                    logger.debug(f"URL test failed: {url} - {e}")
                    continue

        except Exception as e:
            logger.debug(f"Error testing URLs: {e}")

        return working_urls

    def generate_play_token(self, stream_id: str, mac_address: str) -> str:
        """Generate or get a real play token for MAG portals"""
        # First try to get a real token from the portal
        real_token = self.get_real_play_token(stream_id, mac_address)
        if real_token:
            return real_token

        # Fallback: generate a simple token
        import hashlib
        import time

        # Create a simple token based on stream_id, mac, and current time
        # In a real implementation, this should match the portal's token generation
        timestamp = str(int(time.time()))
        token_data = f"{stream_id}_{mac_address}_{timestamp}"
        token_hash = hashlib.md5(token_data.encode()).hexdigest()

        # Return first 10 characters as token (similar to the examples)
        return token_hash[:10]

    def get_real_play_token(self, stream_id: str, mac_address: str) -> Optional[str]:
        """Try to get a real play token from the portal"""
        try:
            import requests
            from urllib.parse import quote
            import re

            # Try different token endpoints that MAG portals might use
            portal_base = "http://mag.ukhd.tv:80"

            token_endpoints = [
                f"{portal_base}/c/server/load.php",
                f"{portal_base}/c/stalker_portal/server/load.php",
                f"{portal_base}/server/load.php",
                f"{portal_base}/stalker_portal/server/load.php"
            ]

            for endpoint in token_endpoints:
                try:
                    # Try to get a play token using different methods
                    params = {
                        'action': 'get_play_token',
                        'stream_id': stream_id,
                        'type': 'vod',
                        'JsHttpRequest': '1-xml'
                    }

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3',
                        'Accept': '*/*',
                        'Referer': f"{portal_base}/c/index.html",
                        'Cookie': f'mac={quote(mac_address)}; stb_lang=en; timezone=Europe/London'
                    }

                    response = requests.get(endpoint, params=params, headers=headers, timeout=5)

                    if response.status_code == 200:
                        # Try to extract token from response
                        response_text = response.text

                        # Look for token patterns in the response
                        token_patterns = [
                            r'play_token["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{10})["\']?',
                            r'token["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]{10})["\']?',
                            r'"([a-zA-Z0-9]{10})"',
                        ]

                        for pattern in token_patterns:
                            match = re.search(pattern, response_text)
                            if match:
                                token = match.group(1)
                                logger.info(f"Found real play token: {token}")
                                return token

                except Exception as e:
                    logger.debug(f"Token endpoint {endpoint} failed: {e}")
                    continue

            logger.debug("Could not get real play token from portal")
            return None

        except Exception as e:
            logger.debug(f"Error getting real play token: {e}")
            return None

    def get_stalker_movie_stream_url(self, portal_url: str, mac_address: str, movie_id: str) -> Optional[str]:
        """Get real movie stream URL using the exact same method as Stalker player"""
        try:
            # Use the existing Stalker portal instance if available
            stalker_portal = self.get_stalker_portal_instance(portal_url, mac_address)

            if stalker_portal:
                # Use the exact same method as the Stalker player
                logger.info(f"Using Stalker portal instance to get movie stream URL for movie_id={movie_id}")
                stream_url = stalker_portal.get_vod_stream_url(movie_id)

                if stream_url:
                    logger.info(f"Got real movie stream URL from Stalker portal: {stream_url}")
                    return stream_url
                else:
                    logger.warning(f"Stalker portal returned no stream URL for movie_id={movie_id}")

            # Fallback: try direct API calls
            direct_url = self.get_stalker_stream_url_direct(portal_url, mac_address, movie_id, 'movie')
            if direct_url:
                return direct_url

            # Final fallback: use MAG URLs
            logger.info("All API methods failed, using MAG URL fallback")
            portal_type = self.detect_stalker_portal_type(portal_url)
            fallback_urls = self.get_stalker_fallback_urls(portal_url, movie_id, portal_type, mac_address, 'movie')
            if fallback_urls:
                logger.info(f"Using MAG fallback URL: {fallback_urls[0]}")
                return fallback_urls[0]

        except Exception as e:
            logger.error(f"Error getting movie stream URL: {e}")
            # Final fallback to MAG URLs
            portal_type = self.detect_stalker_portal_type(portal_url)
            fallback_urls = self.get_stalker_fallback_urls(portal_url, movie_id, portal_type, mac_address, 'movie')
            if fallback_urls:
                logger.info(f"Using fallback movie URL after error: {fallback_urls[0]}")
                return fallback_urls[0]
            return None

    def get_stalker_portal_instance(self, portal_url: str, mac_address: str):
        """Get or create a Stalker portal instance"""
        try:
            from stalker import StalkerPortal

            # Create a temporary Stalker portal instance
            stalker_portal = StalkerPortal(portal_url, mac_address)

            # Try to authenticate
            stalker_portal.authenticate()

            return stalker_portal

        except Exception as e:
            logger.warning(f"Could not create Stalker portal instance: {e}")
            return None

    def get_stalker_stream_url_direct(self, portal_url: str, mac_address: str, stream_id: str, content_type: str) -> Optional[str]:
        """Get stream URL using direct API calls (fallback method)"""
        try:
            import requests
            from urllib.parse import quote

            # Step 1: Get token
            portal_type = self.detect_stalker_portal_type(portal_url)
            token = self.get_stalker_token(portal_url, mac_address, portal_type)

            if not token:
                logger.warning("Could not get Stalker token for direct API call")
                # Try without token
                token = ""

            # Step 2: Try different API endpoints for your portal
            api_endpoints = [
                f"{portal_url}/stalker_portal/server/load.php",
                f"{portal_url}/c/stalker_portal/server/load.php",
                f"{portal_url}/server/load.php",
                f"{portal_url}/c/server/load.php"
            ]

            for api_url in api_endpoints:
                try:
                    # Use the exact same parameters as the Stalker player
                    params = {
                        'action': 'create_link',
                        'type': 'vod',
                        'cmd': f'/media/file_{stream_id}.mpg',
                        'JsHttpRequest': '1-xml'
                    }

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3',
                        'Accept': '*/*',
                        'Referer': f"{portal_url}/c/index.html",
                        'Cookie': f'mac={quote(mac_address)}; stb_lang=en; timezone=Europe/London'
                    }

                    if token:
                        headers['Cookie'] += f'; token={quote(token)}'
                        headers['Authorization'] = f'Bearer {token}'

                    logger.info(f"Trying direct API call: {api_url}")
                    response = requests.get(api_url, params=params, headers=headers, timeout=10)

                    if response.status_code == 200:
                        json_response = response.json()
                        if 'js' in json_response:
                            js_data = json_response['js']

                            # Get stream URL from response (same logic as Stalker player)
                            stream_url = js_data.get('url') or js_data.get('cmd')

                            if stream_url:
                                # Strip ffmpeg prefix if present
                                if isinstance(stream_url, str) and stream_url.lower().startswith('ffmpeg '):
                                    stream_url = stream_url[7:].strip()

                                # If it's a relative path, make it absolute
                                if stream_url.startswith('/'):
                                    stream_url = f"{portal_url}{stream_url}"

                                logger.info(f"Got stream URL from direct API: {stream_url}")
                                return stream_url
                    else:
                        logger.warning(f"API endpoint {api_url} returned {response.status_code}")

                except Exception as e:
                    logger.warning(f"API endpoint {api_url} failed: {e}")
                    continue

            # All API endpoints failed
            logger.warning("All direct API endpoints failed")
            return None

        except Exception as e:
            logger.error(f"Error in direct stream URL call: {e}")
            return None

    def get_stalker_episode_stream_url(self, portal_url: str, mac_address: str, movie_id: str, season_id: str, episode_id: str) -> Optional[str]:
        """Get real episode stream URL using the exact same method as Stalker player"""
        try:
            # Use the existing Stalker portal instance if available
            stalker_portal = self.get_stalker_portal_instance(portal_url, mac_address)

            if stalker_portal:
                # Use the exact same method as the Stalker player
                logger.info(f"Using Stalker portal instance to get episode stream URL for episode_id={episode_id}")
                stream_url = stalker_portal.get_episode_stream_url(movie_id, season_id, episode_id)

                if stream_url:
                    logger.info(f"Got real episode stream URL from Stalker portal: {stream_url}")
                    return stream_url
                else:
                    logger.warning(f"Stalker portal returned no stream URL for episode_id={episode_id}")

            # Fallback: try direct API calls
            direct_url = self.get_stalker_stream_url_direct(portal_url, mac_address, episode_id, 'series')
            if direct_url:
                return direct_url

            # Final fallback: use MAG URLs
            logger.info("All API methods failed for episode, using MAG URL fallback")
            portal_type = self.detect_stalker_portal_type(portal_url)
            fallback_urls = self.get_stalker_fallback_urls(portal_url, episode_id, portal_type, mac_address, 'series')
            if fallback_urls:
                logger.info(f"Using MAG fallback URL for episode: {fallback_urls[0]}")
                return fallback_urls[0]

        except Exception as e:
            logger.error(f"Error getting episode stream URL: {e}")
            # Final fallback to MAG URLs
            portal_type = self.detect_stalker_portal_type(portal_url)
            fallback_urls = self.get_stalker_fallback_urls(portal_url, episode_id, portal_type, mac_address, 'series')
            if fallback_urls:
                logger.info(f"Using fallback episode URL after error: {fallback_urls[0]}")
                return fallback_urls[0]
            return None

    def get_stalker_token(self, portal_url: str, mac_address: str, portal_type: str = 'standard') -> Optional[str]:
        """Get Stalker authentication token"""
        try:
            import requests
            from urllib.parse import quote

            # Handshake URL based on portal type
            if portal_type == 'mag':
                handshake_url = f"{portal_url}/server/load.php"
            else:
                handshake_url = f"{portal_url}/stalker_portal/server/load.php"
            params = {
                'type': 'stb',
                'action': 'handshake',
                'token': '',
                'JsHttpRequest': '1-xml'
            }

            headers = {
                'User-Agent': 'Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3',
                'Accept': '*/*',
                'Referer': f"{portal_url}/stalker_portal/c/index.html",
                'Cookie': f'mac={quote(mac_address)}; stb_lang=en; timezone=Europe/London'
            }

            response = requests.get(handshake_url, params=params, headers=headers, timeout=10)
            response.raise_for_status()

            json_response = response.json()
            if 'js' in json_response:
                token = json_response['js'].get('token')
                if token:
                    logger.info(f"Got Stalker token: {token[:20]}...")
                    return token

            return None

        except Exception as e:
            logger.warning(f"Could not get Stalker token: {e}")
            return None
    
    def add_download_from_player(self, content_data: Dict, server_info: Dict, server_type: str) -> bool:
        """Add download from player content"""
        try:
            if server_type.lower() == 'xtream':
                download_item = self.create_download_from_xtream_content(content_data, server_info)
            elif server_type.lower() == 'stalker':
                download_item = self.create_download_from_stalker_content(content_data, server_info)
            else:
                logger.error(f"Unsupported server type: {server_type}")
                return False
            
            if download_item:
                self.download_manager.add_download_item(download_item)
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error adding download from player: {e}")
            return False
    
    def add_batch_downloads_from_series(self, series_data: Dict, episodes: List[Dict], 
                                      server_info: Dict, server_type: str) -> int:
        """Add batch downloads for entire series"""
        added_count = 0
        
        try:
            for episode_data in episodes:
                # Merge series info with episode info
                combined_data = {**series_data, **episode_data}
                
                if self.add_download_from_player(combined_data, server_info, server_type):
                    added_count += 1
                    
        except Exception as e:
            logger.error(f"Error adding batch downloads: {e}")
        
        return added_count
    
    def extract_quality_from_title(self, title: str) -> str:
        """Extract video quality from title"""
        quality_patterns = [
            (r'4K|2160p', '4K'),
            (r'1080p|FHD', '1080p'),
            (r'720p|HD', '720p'),
            (r'480p|SD', '480p'),
            (r'360p', '360p'),
            (r'240p', '240p')
        ]
        
        for pattern, quality in quality_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                return quality
        
        return "Unknown"
    
    def extract_extension_from_cmd(self, cmd: str) -> Optional[str]:
        """Extract file extension from Stalker cmd"""
        if not cmd:
            return None
        
        # Look for common video extensions
        extensions = ['mp4', 'mkv', 'avi', 'ts', 'm3u8', 'flv', 'mov']
        
        for ext in extensions:
            if f'.{ext}' in cmd.lower():
                return ext
        
        # Default fallback
        return 'mp4'
    
    def get_download_suggestions(self, content_list: List[Dict], server_type: str) -> List[Dict]:
        """Get download suggestions based on content quality and popularity"""
        suggestions = []
        
        for content in content_list:
            suggestion = {
                'title': content.get('name', 'Unknown'),
                'quality': self.extract_quality_from_title(content.get('name', '')),
                'size_estimate': self.estimate_file_size(content),
                'download_time_estimate': self.estimate_download_time(content),
                'content_data': content
            }
            suggestions.append(suggestion)
        
        # Sort by quality (higher quality first)
        quality_order = {'4K': 4, '1080p': 3, '720p': 2, '480p': 1, 'Unknown': 0}
        suggestions.sort(key=lambda x: quality_order.get(x['quality'], 0), reverse=True)
        
        return suggestions
    
    def estimate_file_size(self, content_data: Dict) -> str:
        """Estimate file size based on duration and quality"""
        try:
            duration_str = content_data.get('duration', '0')
            
            # Parse duration (could be in various formats)
            duration_minutes = self.parse_duration_to_minutes(duration_str)
            
            if duration_minutes == 0:
                return "Unknown"
            
            # Estimate based on quality and duration
            title = content_data.get('name', '')
            quality = self.extract_quality_from_title(title)
            
            # Rough estimates (MB per minute)
            quality_bitrates = {
                '4K': 25,      # ~25 MB/min
                '1080p': 12,   # ~12 MB/min
                '720p': 6,     # ~6 MB/min
                '480p': 3,     # ~3 MB/min
                'Unknown': 8   # ~8 MB/min (assume decent quality)
            }
            
            mb_per_minute = quality_bitrates.get(quality, 8)
            estimated_mb = duration_minutes * mb_per_minute
            
            # Convert to appropriate unit
            if estimated_mb < 1024:
                return f"~{estimated_mb:.0f} MB"
            else:
                return f"~{estimated_mb/1024:.1f} GB"
                
        except Exception:
            return "Unknown"
    
    def estimate_download_time(self, content_data: Dict) -> str:
        """Estimate download time based on file size and average speed"""
        try:
            size_str = self.estimate_file_size(content_data)
            if size_str == "Unknown":
                return "Unknown"
            
            # Extract size in MB
            if "GB" in size_str:
                size_mb = float(size_str.replace("~", "").replace(" GB", "")) * 1024
            else:
                size_mb = float(size_str.replace("~", "").replace(" MB", ""))
            
            # Assume average download speed of 10 Mbps (1.25 MB/s)
            avg_speed_mbs = 1.25
            estimated_seconds = size_mb / avg_speed_mbs
            
            # Convert to human readable time
            if estimated_seconds < 60:
                return f"~{estimated_seconds:.0f}s"
            elif estimated_seconds < 3600:
                return f"~{estimated_seconds/60:.0f}m"
            else:
                return f"~{estimated_seconds/3600:.1f}h"
                
        except Exception:
            return "Unknown"
    
    def parse_duration_to_minutes(self, duration_str: str) -> int:
        """Parse duration string to minutes"""
        try:
            if not duration_str or duration_str == "0":
                return 0
            
            # Handle different duration formats
            if ":" in duration_str:
                # Format: HH:MM:SS or MM:SS
                parts = duration_str.split(":")
                if len(parts) == 3:  # HH:MM:SS
                    hours, minutes, seconds = map(int, parts)
                    return hours * 60 + minutes + (1 if seconds > 30 else 0)
                elif len(parts) == 2:  # MM:SS
                    minutes, seconds = map(int, parts)
                    return minutes + (1 if seconds > 30 else 0)
            else:
                # Assume it's in seconds
                seconds = int(duration_str)
                return seconds // 60 + (1 if seconds % 60 > 30 else 0)
                
        except Exception:
            return 0
        
        return 0
