#!/usr/bin/env python3
"""
EPG (Electronic Program Guide) Manager
Advanced EPG handling with external XML support and smart channel matching
"""

import xml.etree.ElementTree as ET
import gzip
import requests
import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse
import difflib
from dataclasses import dataclass
from threading import Lock

@dataclass
class EPGProgram:
    """Represents a single EPG program"""
    start: datetime
    stop: datetime
    title: str
    description: str = ""
    category: str = ""
    episode_num: str = ""
    rating: str = ""
    
    def to_dict(self) -> Dict:
        return {
            'start': self.start.isoformat(),
            'stop': self.stop.isoformat(),
            'title': self.title,
            'description': self.description,
            'category': self.category,
            'episode_num': self.episode_num,
            'rating': self.rating
        }

@dataclass
class EPGChannel:
    """Represents an EPG channel with programs"""
    id: str
    display_name: str
    icon: str = ""
    programs: List[EPGProgram] = None
    
    def __post_init__(self):
        if self.programs is None:
            self.programs = []
    
    def to_dict(self) -> Dict:
        return {
            'id': self.id,
            'display_name': self.display_name,
            'icon': self.icon,
            'programs': [prog.to_dict() for prog in self.programs]
        }

@dataclass
class ChannelMatch:
    """Represents a channel match with confidence score"""
    epg_channel: EPGChannel
    confidence: float
    match_reason: str

class EPGManager:
    """Main EPG management class"""
    
    def __init__(self, cache_dir: str = "epg_cache"):
        self.cache_dir = cache_dir
        self.epg_channels: Dict[str, EPGChannel] = {}
        self.channel_aliases: Dict[str, List[str]] = {}
        self.confidence_threshold = 70  # Default confidence threshold
        self.prefer_internal = True  # Prefer IPTV provider EPG first
        self.external_epg_url = ""
        self.last_update = None
        self.lock = Lock()
        
        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)
        
        # Load cached data
        self.load_cache()
    
    def set_external_epg_url(self, url: str) -> bool:
        """Set external EPG URL and validate it"""
        if not url:
            self.external_epg_url = ""
            return True
            
        # Validate URL format
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Check if URL ends with xml or xml.gz
            if not (url.lower().endswith('.xml') or url.lower().endswith('.xml.gz')):
                return False
                
            self.external_epg_url = url
            return True
            
        except Exception:
            return False
    
    def set_confidence_threshold(self, confidence: int) -> None:
        """Set confidence threshold (0-100)"""
        self.confidence_threshold = max(0, min(100, confidence))
    
    def set_prefer_internal(self, prefer: bool) -> None:
        """Set whether to prefer internal IPTV provider EPG first"""
        self.prefer_internal = prefer
    
    def download_external_epg(self, progress_callback=None) -> bool:
        """Download and parse external EPG"""
        if not self.external_epg_url:
            return False
            
        try:
            if progress_callback:
                progress_callback("Downloading EPG...", 10)
            
            # Download EPG file
            response = requests.get(self.external_epg_url, timeout=30, stream=True)
            response.raise_for_status()
            
            if progress_callback:
                progress_callback("Processing EPG data...", 30)
            
            # Handle gzipped content
            content = response.content
            if self.external_epg_url.lower().endswith('.gz'):
                content = gzip.decompress(content)
            
            if progress_callback:
                progress_callback("Parsing XML...", 50)
            
            # Parse XML
            root = ET.fromstring(content)
            
            if progress_callback:
                progress_callback("Building channel database...", 70)
            
            # Parse channels and programs
            self.parse_xmltv(root)
            
            if progress_callback:
                progress_callback("Saving cache...", 90)
            
            # Save to cache
            self.save_cache()
            self.last_update = datetime.now()
            
            if progress_callback:
                progress_callback("EPG update completed!", 100)
            
            return True
            
        except Exception as e:
            if progress_callback:
                progress_callback(f"EPG update failed: {str(e)}", 0)
            return False
    
    def parse_xmltv(self, root: ET.Element) -> None:
        """Parse XMLTV format EPG data"""
        with self.lock:
            self.epg_channels.clear()
            
            # Parse channels
            for channel_elem in root.findall('channel'):
                channel_id = channel_elem.get('id', '')
                if not channel_id:
                    continue
                
                # Get display name
                display_name_elem = channel_elem.find('display-name')
                display_name = display_name_elem.text if display_name_elem is not None else channel_id
                
                # Get icon
                icon_elem = channel_elem.find('icon')
                icon = icon_elem.get('src', '') if icon_elem is not None else ''
                
                # Create channel
                epg_channel = EPGChannel(
                    id=channel_id,
                    display_name=display_name,
                    icon=icon
                )
                
                self.epg_channels[channel_id] = epg_channel
                
                # Build aliases for better matching
                aliases = [display_name.lower().strip()]
                
                # Add all display-name variants
                for dn_elem in channel_elem.findall('display-name'):
                    if dn_elem.text:
                        aliases.append(dn_elem.text.lower().strip())
                
                self.channel_aliases[channel_id] = list(set(aliases))
            
            # Parse programs
            for program_elem in root.findall('programme'):
                channel_id = program_elem.get('channel', '')
                if channel_id not in self.epg_channels:
                    continue
                
                # Parse times
                start_str = program_elem.get('start', '')
                stop_str = program_elem.get('stop', '')
                
                try:
                    start_time = self.parse_xmltv_time(start_str)
                    stop_time = self.parse_xmltv_time(stop_str)
                except:
                    continue
                
                # Get program details
                title_elem = program_elem.find('title')
                title = title_elem.text if title_elem is not None else 'Unknown'
                
                desc_elem = program_elem.find('desc')
                description = desc_elem.text if desc_elem is not None else ''
                
                category_elem = program_elem.find('category')
                category = category_elem.text if category_elem is not None else ''
                
                episode_elem = program_elem.find('episode-num')
                episode_num = episode_elem.text if episode_elem is not None else ''
                
                rating_elem = program_elem.find('rating/value')
                rating = rating_elem.text if rating_elem is not None else ''
                
                # Create program
                program = EPGProgram(
                    start=start_time,
                    stop=stop_time,
                    title=title,
                    description=description,
                    category=category,
                    episode_num=episode_num,
                    rating=rating
                )
                
                self.epg_channels[channel_id].programs.append(program)
            
            # Sort programs by start time
            for channel in self.epg_channels.values():
                channel.programs.sort(key=lambda p: p.start)
    
    def parse_xmltv_time(self, time_str: str) -> datetime:
        """Parse XMLTV time format: YYYYMMDDHHMMSS +ZZZZ"""
        # Remove timezone info for now (basic implementation)
        time_part = time_str.split()[0] if ' ' in time_str else time_str
        
        # Parse basic format
        if len(time_part) >= 14:
            return datetime.strptime(time_part[:14], '%Y%m%d%H%M%S')
        elif len(time_part) >= 12:
            return datetime.strptime(time_part[:12], '%Y%m%d%H%M')
        else:
            return datetime.strptime(time_part[:8], '%Y%m%d')
    
    def find_channel_match(self, channel_name: str) -> Optional[ChannelMatch]:
        """Find best matching EPG channel for given channel name"""
        if not channel_name or not self.epg_channels:
            return None
        
        channel_name_clean = self.clean_channel_name(channel_name)
        best_match = None
        best_confidence = 0
        
        for epg_id, aliases in self.channel_aliases.items():
            for alias in aliases:
                confidence = self.calculate_match_confidence(channel_name_clean, alias)
                
                if confidence > best_confidence and confidence >= self.confidence_threshold:
                    best_confidence = confidence
                    best_match = ChannelMatch(
                        epg_channel=self.epg_channels[epg_id],
                        confidence=confidence,
                        match_reason=f"Matched '{channel_name}' with '{alias}' ({confidence}% confidence)"
                    )
        
        return best_match
    
    def clean_channel_name(self, name: str) -> str:
        """Clean channel name for better matching"""
        # Convert to lowercase
        clean = name.lower().strip()
        
        # Remove common prefixes/suffixes
        prefixes = ['hd', 'sd', 'fhd', '4k', 'uhd']
        suffixes = ['hd', 'sd', 'fhd', '4k', 'uhd', 'tv', 'channel']
        
        # Remove quality indicators
        clean = re.sub(r'\b(hd|sd|fhd|4k|uhd)\b', '', clean)
        
        # Remove special characters
        clean = re.sub(r'[^\w\s]', ' ', clean)
        
        # Remove extra spaces
        clean = ' '.join(clean.split())
        
        return clean
    
    def calculate_match_confidence(self, name1: str, name2: str) -> float:
        """Calculate confidence score between two channel names"""
        if not name1 or not name2:
            return 0.0
        
        # Exact match
        if name1 == name2:
            return 100.0
        
        # Sequence matcher for similarity
        similarity = difflib.SequenceMatcher(None, name1, name2).ratio() * 100
        
        # Bonus for word matches
        words1 = set(name1.split())
        words2 = set(name2.split())
        
        if words1 and words2:
            word_match_ratio = len(words1.intersection(words2)) / len(words1.union(words2))
            similarity += word_match_ratio * 20  # Bonus for word matches
        
        # Bonus for substring matches
        if name1 in name2 or name2 in name1:
            similarity += 10
        
        return min(100.0, similarity)
    
    def get_current_program(self, channel_name: str) -> Optional[EPGProgram]:
        """Get current program for a channel"""
        match = self.find_channel_match(channel_name)
        if not match:
            return None
        
        now = datetime.now()
        for program in match.epg_channel.programs:
            if program.start <= now <= program.stop:
                return program
        
        return None
    
    def get_programs_for_channel(self, channel_name: str, hours: int = 24) -> List[EPGProgram]:
        """Get programs for a channel within specified hours"""
        match = self.find_channel_match(channel_name)
        if not match:
            return []
        
        now = datetime.now()
        end_time = now + timedelta(hours=hours)
        
        programs = []
        for program in match.epg_channel.programs:
            if program.start <= end_time and program.stop >= now:
                programs.append(program)
        
        return programs
    
    def save_cache(self) -> None:
        """Save EPG data to cache"""
        try:
            cache_file = os.path.join(self.cache_dir, 'epg_cache.json')
            
            cache_data = {
                'channels': {cid: ch.to_dict() for cid, ch in self.epg_channels.items()},
                'aliases': self.channel_aliases,
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'external_epg_url': self.external_epg_url,
                'confidence_threshold': self.confidence_threshold,
                'prefer_internal': self.prefer_internal
            }
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Failed to save EPG cache: {e}")
    
    def load_cache(self) -> None:
        """Load EPG data from cache"""
        try:
            cache_file = os.path.join(self.cache_dir, 'epg_cache.json')
            
            if not os.path.exists(cache_file):
                return
            
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # Load channels
            for cid, ch_data in cache_data.get('channels', {}).items():
                programs = []
                for prog_data in ch_data.get('programs', []):
                    program = EPGProgram(
                        start=datetime.fromisoformat(prog_data['start']),
                        stop=datetime.fromisoformat(prog_data['stop']),
                        title=prog_data['title'],
                        description=prog_data.get('description', ''),
                        category=prog_data.get('category', ''),
                        episode_num=prog_data.get('episode_num', ''),
                        rating=prog_data.get('rating', '')
                    )
                    programs.append(program)
                
                channel = EPGChannel(
                    id=cid,
                    display_name=ch_data['display_name'],
                    icon=ch_data.get('icon', ''),
                    programs=programs
                )
                
                self.epg_channels[cid] = channel
            
            # Load other data
            self.channel_aliases = cache_data.get('aliases', {})
            self.external_epg_url = cache_data.get('external_epg_url', '')
            self.confidence_threshold = cache_data.get('confidence_threshold', 70)
            self.prefer_internal = cache_data.get('prefer_internal', True)
            
            if cache_data.get('last_update'):
                self.last_update = datetime.fromisoformat(cache_data['last_update'])
                
        except Exception as e:
            print(f"Failed to load EPG cache: {e}")
    
    def get_statistics(self) -> Dict:
        """Get EPG statistics"""
        total_channels = len(self.epg_channels)
        total_programs = sum(len(ch.programs) for ch in self.epg_channels.values())
        
        # Count programs by time
        now = datetime.now()
        current_programs = 0
        future_programs = 0
        
        for channel in self.epg_channels.values():
            for program in channel.programs:
                if program.start <= now <= program.stop:
                    current_programs += 1
                elif program.start > now:
                    future_programs += 1
        
        return {
            'total_channels': total_channels,
            'total_programs': total_programs,
            'current_programs': current_programs,
            'future_programs': future_programs,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'external_epg_url': self.external_epg_url,
            'confidence_threshold': self.confidence_threshold,
            'prefer_internal': self.prefer_internal
        }
