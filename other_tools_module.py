"""
Other Tools Module
Contains various utility tools: MAC generator, Portal converter, Proxy grabber
"""

import logging
import requests
import random
import os
from datetime import datetime
from urllib.parse import urlparse
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTextEdit, QComboBox, QSpinBox, QMessageBox,
    QProgressBar, QGroupBox, QScrollArea, QFileDialog, QTabWidget
)

from styles import Colors

# Setup logging
logger = logging.getLogger(__name__)

class ProxyGrabberThread(QThread):
    """Thread for grabbing proxies from various sources"""
    proxies_found = pyqtSignal(list)
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, proxy_type):
        super().__init__()
        self.proxy_type = proxy_type
        self.proxies = []

    def run(self):
        """Run proxy grabbing"""
        try:
            self.status_updated.emit(f"🔍 Searching for {self.proxy_type} proxies...")
            self.progress_updated.emit(10)

            # Proxy sources (free proxy APIs and websites)
            sources = self.get_proxy_sources()
            total_sources = len(sources)

            for i, source in enumerate(sources):
                try:
                    self.status_updated.emit(f"📡 Checking source {i+1}/{total_sources}...")
                    proxies = self.fetch_proxies_from_source(source)
                    self.proxies.extend(proxies)
                    progress = int(((i + 1) / total_sources) * 90)
                    self.progress_updated.emit(progress)
                except Exception as e:
                    logger.warning(f"Failed to fetch from source {source}: {e}")
                    continue

            # Remove duplicates
            unique_proxies = list(set(self.proxies))
            self.proxies = unique_proxies

            self.progress_updated.emit(100)
            self.status_updated.emit(f"✅ Found {len(self.proxies)} {self.proxy_type} proxies")
            self.proxies_found.emit(self.proxies)

        except Exception as e:
            logger.error(f"Error in proxy grabber: {e}")
            self.error_occurred.emit(str(e))
        finally:
            self.finished.emit()

    def get_proxy_sources(self):
        """Get proxy sources based on type"""
        if self.proxy_type in ["HTTP", "HTTPS"]:
            return [
                "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all",
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
                "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
                "https://raw.githubusercontent.com/sunny9577/proxy-scraper/master/proxies.txt"
            ]
        else:  # SOCKS4/SOCKS5
            return [
                "https://api.proxyscrape.com/v2/?request=get&protocol=socks4&timeout=10000&country=all",
                "https://api.proxyscrape.com/v2/?request=get&protocol=socks5&timeout=10000&country=all",
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks4.txt",
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/socks5.txt"
            ]

    def fetch_proxies_from_source(self, url):
        """Fetch proxies from a single source"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # Parse proxy list
            proxies = []
            lines = response.text.strip().split('\n')

            for line in lines:
                line = line.strip()
                if ':' in line and self.is_valid_proxy(line):
                    proxies.append(line)

            return proxies
        except Exception as e:
            logger.warning(f"Failed to fetch from {url}: {e}")
            return []

    def is_valid_proxy(self, proxy):
        """Validate proxy format"""
        try:
            if ':' not in proxy:
                return False

            parts = proxy.split(':')
            if len(parts) != 2:
                return False

            ip, port = parts

            # Validate IP
            ip_parts = ip.split('.')
            if len(ip_parts) != 4:
                return False

            for part in ip_parts:
                if not part.isdigit() or not 0 <= int(part) <= 255:
                    return False

            # Validate port
            if not port.isdigit() or not 1 <= int(port) <= 65535:
                return False

            return True
        except:
            return False

class PortalConverterThread(QThread):
    """Thread for converting portal URL + MAC to username/password"""
    conversion_completed = pyqtSignal(dict)
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, portal_url, mac_address):
        super().__init__()
        self.portal_url = portal_url
        self.mac_address = mac_address

    def run(self):
        """Run portal conversion"""
        try:
            self.status_updated.emit("🔍 Analyzing portal...")
            self.progress_updated.emit(20)

            # Try to connect to portal and analyze VOD URLs
            result = self.analyze_portal()

            self.progress_updated.emit(100)
            self.conversion_completed.emit(result)

        except Exception as e:
            logger.error(f"Error in portal converter: {e}")
            self.error_occurred.emit(str(e))
        finally:
            self.finished.emit()

    def analyze_portal(self):
        """Analyze portal to extract credentials from VOD URLs"""
        try:
            # Method 1: Try to extract from Stalker portal VOD URLs
            self.status_updated.emit("🔗 Connecting to portal...")
            self.progress_updated.emit(20)

            stalker_result = self.extract_from_stalker_portal()
            if stalker_result['success']:
                return stalker_result

            # Method 2: Try alternative extraction methods
            self.status_updated.emit("🔍 Trying alternative methods...")
            self.progress_updated.emit(60)

            alternative_result = self.try_alternative_methods()
            if alternative_result['success']:
                return alternative_result

            # Method 3: Try URL pattern analysis
            self.status_updated.emit("📋 Analyzing URL patterns...")
            self.progress_updated.emit(80)

            pattern_result = self.analyze_url_patterns()
            return pattern_result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to analyze portal: {e}'
            }

    def extract_from_stalker_portal(self):
        """Extract credentials from Stalker portal VOD URLs"""
        try:
            from stalker import StalkerPortal
            import re

            # Clean portal URL
            portal_url = self.portal_url.replace('/stalker_portal', '')

            self.status_updated.emit("🎬 Fetching VOD content...")

            # Connect to Stalker portal
            with StalkerPortal(portal_url=portal_url, mac=self.mac_address, timeout=15) as portal:
                # Get VOD categories
                vod_categories = portal.get_vod_categories()
                if not vod_categories:
                    return {'success': False, 'message': 'No VOD categories found'}

                self.status_updated.emit("🔍 Analyzing VOD URLs...")

                # Try to get some VOD items to analyze their URLs
                for category in vod_categories[:3]:  # Check first 3 categories
                    try:
                        category_id = category.get('id')
                        if not category_id:
                            continue

                        # Get VOD items from this category
                        vod_items = portal.get_vod_list(category_id, page=1)

                        for item in vod_items[:5]:  # Check first 5 items
                            try:
                                item_id = item.get('id')
                                if not item_id:
                                    continue

                                # Get stream link for this item
                                stream_url = portal.create_stream_link(item_id)
                                if stream_url:
                                    # Analyze the stream URL for credentials
                                    credentials = self.extract_credentials_from_url(stream_url)
                                    if credentials['username'] and credentials['password']:
                                        parsed_url = urlparse(self.portal_url)
                                        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

                                        # Create Xtream-Codes format URL
                                        converted_url = f"{base_url}/get.php?username={credentials['username']}&password={credentials['password']}&type=m3u_plus&output=ts"

                                        return {
                                            'success': True,
                                            'original_url': self.portal_url,
                                            'original_mac': self.mac_address,
                                            'base_url': base_url,
                                            'username': credentials['username'],
                                            'password': credentials['password'],
                                            'converted_url': converted_url,
                                            'extraction_method': 'Stalker VOD URL Analysis',
                                            'sample_stream_url': stream_url,
                                            'message': 'Successfully extracted credentials from VOD URLs'
                                        }

                            except Exception as e:
                                continue

                    except Exception as e:
                        continue

            return {'success': False, 'message': 'No credentials found in VOD URLs'}

        except Exception as e:
            return {'success': False, 'message': f'Stalker portal analysis failed: {str(e)}'}

    def extract_credentials_from_url(self, url):
        """Extract username and password from various URL patterns"""
        import re

        credentials = {'username': None, 'password': None}

        # Pattern 1: http://server:port/username/password/id.mp4
        pattern1 = re.search(r'https?://[^/]+/([^/]+)/([^/]+)/\d+\.(mp4|ts|m3u8)', url)
        if pattern1:
            credentials['username'] = pattern1.group(1)
            credentials['password'] = pattern1.group(2)
            return credentials

        # Pattern 2: http://server:port/live/username/password/id.ts
        pattern2 = re.search(r'https?://[^/]+/live/([^/]+)/([^/]+)/\d+\.(ts|m3u8)', url)
        if pattern2:
            credentials['username'] = pattern2.group(1)
            credentials['password'] = pattern2.group(2)
            return credentials

        # Pattern 3: http://server:port/movie/username/password/id.mp4
        pattern3 = re.search(r'https?://[^/]+/movie/([^/]+)/([^/]+)/\d+\.(mp4|mkv|avi)', url)
        if pattern3:
            credentials['username'] = pattern3.group(1)
            credentials['password'] = pattern3.group(2)
            return credentials

        # Pattern 4: http://server:port/series/username/password/id.mp4
        pattern4 = re.search(r'https?://[^/]+/series/([^/]+)/([^/]+)/\d+\.(mp4|mkv|avi)', url)
        if pattern4:
            credentials['username'] = pattern4.group(1)
            credentials['password'] = pattern4.group(2)
            return credentials

        # Pattern 5: URL parameters ?username=X&password=Y
        pattern5 = re.search(r'[?&]username=([^&]+).*[?&]password=([^&]+)', url)
        if pattern5:
            credentials['username'] = pattern5.group(1)
            credentials['password'] = pattern5.group(2)
            return credentials

        # Pattern 6: URL parameters ?user=X&pass=Y
        pattern6 = re.search(r'[?&]user=([^&]+).*[?&]pass=([^&]+)', url)
        if pattern6:
            credentials['username'] = pattern6.group(1)
            credentials['password'] = pattern6.group(2)
            return credentials

        # Pattern 7: http://server:port/vod/username/password/id.ext
        pattern7 = re.search(r'https?://[^/]+/vod/([^/]+)/([^/]+)/\d+\.(mp4|mkv|avi|ts)', url)
        if pattern7:
            credentials['username'] = pattern7.group(1)
            credentials['password'] = pattern7.group(2)
            return credentials

        # Pattern 8: http://server:port/play/username/password/id.ext
        pattern8 = re.search(r'https?://[^/]+/play/([^/]+)/([^/]+)/\d+\.(mp4|mkv|avi|ts)', url)
        if pattern8:
            credentials['username'] = pattern8.group(1)
            credentials['password'] = pattern8.group(2)
            return credentials

        # Pattern 9: http://server:port/stream/username/password/id.ext
        pattern9 = re.search(r'https?://[^/]+/stream/([^/]+)/([^/]+)/\d+\.(mp4|mkv|avi|ts)', url)
        if pattern9:
            credentials['username'] = pattern9.group(1)
            credentials['password'] = pattern9.group(2)
            return credentials

        # Pattern 10: *******************************:port/path/id.ext
        pattern10 = re.search(r'https?://([^:]+):([^@]+)@[^/]+/', url)
        if pattern10:
            credentials['username'] = pattern10.group(1)
            credentials['password'] = pattern10.group(2)
            return credentials

        # Pattern 11: Look for any two consecutive path segments that look like credentials
        # This catches custom patterns like /custom/username/password/
        path_segments = re.findall(r'/([^/]+)', url)
        if len(path_segments) >= 2:
            for i in range(len(path_segments) - 1):
                segment1 = path_segments[i]
                segment2 = path_segments[i + 1]

                # Skip numeric segments and common words
                if (not segment1.isdigit() and not segment2.isdigit() and
                    segment1.lower() not in ['live', 'movie', 'series', 'vod', 'play', 'stream', 'media', 'file'] and
                    segment2.lower() not in ['live', 'movie', 'series', 'vod', 'play', 'stream', 'media', 'file'] and
                    len(segment1) > 2 and len(segment2) > 2):

                    credentials['username'] = segment1
                    credentials['password'] = segment2
                    return credentials

        return credentials

    def try_alternative_methods(self):
        """Try alternative methods to extract credentials"""
        try:
            import requests
            import re

            # Method 1: Try to access portal info page
            parsed_url = urlparse(self.portal_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            # Common info endpoints that might reveal credentials
            info_endpoints = [
                f"{base_url}/portal.php?type=account_info&action=get_main_info",
                f"{base_url}/stalker_portal/server/load.php?type=account_info&action=get_main_info",
                f"{base_url}/player_api.php?username=&password=&action=get_live_categories",
                f"{base_url}/get.php?username=&password=&type=m3u_plus"
            ]

            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (QtEmbedded; U; Linux; C) AppleWebKit/533.3 (KHTML, like Gecko) MAG200 stbapp ver: 2 rev: 250 Safari/533.3',
                'X-User-Agent': 'Model: MAG250; Link: WiFi',
                'Cookie': f'mac={self.mac_address}; stb_lang=en; timezone=Europe/London'
            })

            for endpoint in info_endpoints:
                try:
                    response = session.get(endpoint, timeout=10)
                    if response.status_code == 200:
                        # Look for credential patterns in response
                        content = response.text

                        # Look for JSON with credentials
                        username_match = re.search(r'"username":\s*"([^"]+)"', content)
                        password_match = re.search(r'"password":\s*"([^"]+)"', content)

                        if username_match and password_match:
                            converted_url = f"{base_url}/get.php?username={username_match.group(1)}&password={password_match.group(1)}&type=m3u_plus&output=ts"

                            return {
                                'success': True,
                                'original_url': self.portal_url,
                                'original_mac': self.mac_address,
                                'base_url': base_url,
                                'username': username_match.group(1),
                                'password': password_match.group(1),
                                'converted_url': converted_url,
                                'extraction_method': 'Portal Info API',
                                'message': 'Successfully extracted credentials from portal info'
                            }

                except Exception:
                    continue

            return {'success': False, 'message': 'Alternative methods failed'}

        except Exception as e:
            return {'success': False, 'message': f'Alternative methods error: {str(e)}'}

    def analyze_url_patterns(self):
        """Final analysis when automatic extraction fails"""
        try:
            return {
                'success': False,
                'original_url': self.portal_url,
                'original_mac': self.mac_address,
                'extraction_method': 'All Methods Failed',
                'message': 'Could not extract credentials from this portal. This portal may not embed credentials in VOD URLs or uses a different authentication method.'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Analysis failed: {e}'
            }

class OtherToolsWidget(QWidget):
    """Widget containing various utility tools"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create tab widget for different tool categories
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {Colors.PRIMARY};
                background-color: {Colors.BACKGROUND};
            }}
            QTabBar::tab {{
                background-color: {Colors.SURFACE};
                color: {Colors.TEXT};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background-color: {Colors.PRIMARY};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {Colors.PRIMARY}CC;
            }}
        """)

        # Create Basic Tools tab
        self.setup_basic_tools_tab()

        # Create EPG Manager tab
        self.setup_epg_manager_tab()

        # Add Language Settings tab
        self.setup_language_settings_tab()

        # Add M3U Converter tab
        self.setup_m3u_converter_tab()

        # Add M3U Editor tab
        self.setup_m3u_editor_tab()

        # Add Multi-Device Export tab
        self.setup_multi_device_export_tab()

        main_layout.addWidget(self.tabs)

    def setup_basic_tools_tab(self):
        """Setup the basic tools tab"""
        # Create scroll area for tools
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: {Colors.BACKGROUND};
            }}
        """)

        # Main content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)

        # MAC Address Generator
        self.setup_mac_generator(content_layout)

        # Portal Converter
        self.setup_portal_converter(content_layout)

        # Proxy Grabber
        self.setup_proxy_grabber(content_layout)

        # Set content widget to scroll area
        scroll_area.setWidget(content_widget)

        # Add tab
        self.tabs.addTab(scroll_area, "🛠️ Basic")

    def setup_epg_manager_tab(self):
        """Setup the EPG Manager tab"""
        try:
            from epg_manager_gui import EPGManagerWidget
            self.epg_manager_widget = EPGManagerWidget()
            self.tabs.addTab(self.epg_manager_widget, "📺 EPG")
        except ImportError as e:
            print(f"⚠️ EPG Manager not available: {e}")
            # Create placeholder tab
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_label = QLabel("📺 EPG Manager\n\nEPG Manager is not available.\nPlease ensure epg_manager_gui.py is in the same directory.")
            placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 14px; padding: 20px;")
            placeholder_layout.addWidget(placeholder_label)
            self.tabs.addTab(placeholder, "📺 EPG")

    def setup_language_settings_tab(self):
        """Setup the Language Settings tab"""
        try:
            from language_selector import LanguageSelectorWidget
            self.language_selector_widget = LanguageSelectorWidget()

            # Connect language change signal
            self.language_selector_widget.language_changed.connect(self.on_language_changed)

            self.tabs.addTab(self.language_selector_widget, "🌍 Language")
        except ImportError as e:
            print(f"⚠️ Language Selector not available: {e}")
            # Create placeholder tab
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_label = QLabel("🌍 Language Settings\n\nLanguage Settings are not available.\nPlease ensure language_selector.py is in the same directory.")
            placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 14px; padding: 20px;")
            placeholder_layout.addWidget(placeholder_label)
            self.tabs.addTab(placeholder, "🌍 Language")

    def setup_m3u_converter_tab(self):
        """Setup the M3U Converter tab"""
        try:
            from m3u_converter_widget import M3UConverterWidget
            self.m3u_converter_widget = M3UConverterWidget()
            self.tabs.addTab(self.m3u_converter_widget, "🔄 M3U")
        except ImportError as e:
            print(f"⚠️ M3U Converter not available: {e}")
            # Create placeholder tab
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_label = QLabel("🔄 M3U Converter\n\nM3U Converter is not available.\nPlease ensure m3u_converter_widget.py is in the same directory.")
            placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 14px; padding: 20px;")
            placeholder_layout.addWidget(placeholder_label)
            self.tabs.addTab(placeholder, "🔄 M3U")

    def setup_m3u_editor_tab(self):
        """Setup the M3U Editor tab"""
        try:
            from m3u_editor_widget import M3UEditorWidget
            self.m3u_editor_widget = M3UEditorWidget()
            self.tabs.addTab(self.m3u_editor_widget, "✏️ M3U Editor")
        except ImportError as e:
            print(f"⚠️ M3U Editor not available: {e}")
            # Create placeholder tab
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_label = QLabel("✏️ M3U Editor\n\nM3U Editor is not available.\nPlease ensure m3u_editor_widget.py is in the same directory.")
            placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 14px; padding: 20px;")
            placeholder_layout.addWidget(placeholder_label)
            self.tabs.addTab(placeholder, "✏️ M3U Editor")

    def setup_multi_device_export_tab(self):
        """Setup the Multi-Device Export tab"""
        try:
            from multi_device_export_widget import MultiDeviceExportWidget
            self.multi_device_export_widget = MultiDeviceExportWidget()
            self.tabs.addTab(self.multi_device_export_widget, "📱 Multi-Device Export")
        except ImportError as e:
            print(f"⚠️ Multi-Device Export not available: {e}")
            # Create placeholder tab
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_label = QLabel("📱 Multi-Device Export\n\nMulti-Device Export is not available.\nPlease ensure multi_device_export_widget.py is in the same directory.\n\nNote: This feature requires the 'qrcode' package.\nInstall with: pip install qrcode[pil]")
            placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 14px; padding: 20px;")
            placeholder_layout.addWidget(placeholder_label)
            self.tabs.addTab(placeholder, "📱 Multi-Device Export")

    def on_language_changed(self, language_code):
        """Handle language change from language selector"""
        print(f"🌍 Language changed to: {language_code}")

        # Update all translatable elements in this widget
        self.update_translations()

        # Emit signal to parent to update entire application
        # This allows the main window to update all tabs and UI elements
        if hasattr(self.parent(), 'update_all_translations'):
            self.parent().update_all_translations()

    def update_translations(self):
        """Update all translatable text in this widget"""
        try:
            from translations.languages import _

            # Update tab titles
            if hasattr(self, 'tabs'):
                # Find and update tab titles
                for i in range(self.tabs.count()):
                    current_text = self.tabs.tabText(i)
                    if "Basic" in current_text or "🛠️" in current_text:
                        self.tabs.setTabText(i, _("basic_tools", "🛠️ Basic"))
                    elif "EPG" in current_text or "📺" in current_text:
                        self.tabs.setTabText(i, _("epg_manager", "📺 EPG"))
                    elif "Language" in current_text or "🌍" in current_text:
                        self.tabs.setTabText(i, _("language_settings", "🌍 Language"))
                    elif "M3U" in current_text or "🔄" in current_text:
                        self.tabs.setTabText(i, _("m3u_converter", "🔄 M3U"))

            # Update M3U converter widget if available
            if hasattr(self, 'm3u_converter_widget') and hasattr(self.m3u_converter_widget, 'update_translations'):
                self.m3u_converter_widget.update_translations()

        except Exception as e:
            print(f"Error updating translations: {e}")

    def setup_mac_generator(self, layout):
        """Setup MAC address generator section"""
        # MAC Generator Group
        mac_group = QGroupBox("🔧 MAC Address Generator")
        mac_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        mac_layout = QVBoxLayout(mac_group)
        mac_layout.setSpacing(10)

        # Description
        desc_label = QLabel("Generate unlimited random MAC addresses with format: 00:1A:79:XX:XX:XX (Auto-saved to macs/ folder)")
        desc_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 12px; padding: 5px;")
        mac_layout.addWidget(desc_label)

        # Input row
        input_row = QHBoxLayout()

        # Number of MACs
        count_label = QLabel("Number of MACs:")
        count_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.mac_count_spin = QSpinBox()
        self.mac_count_spin.setRange(1, 999999999)  # Practically unlimited
        self.mac_count_spin.setValue(10)
        self.mac_count_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
                min-width: 80px;
            }}
        """)

        # Generate button
        self.generate_mac_btn = QPushButton("🎲 Generate MACs")
        self.generate_mac_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SUCCESS};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.SUCCESS}DD;
            }}
        """)
        self.generate_mac_btn.clicked.connect(self.generate_macs)

        # Save button
        self.save_mac_btn = QPushButton("💾 Save MACs")
        self.save_mac_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
        """)
        self.save_mac_btn.clicked.connect(self.save_macs)
        self.save_mac_btn.setEnabled(False)  # Initially disabled

        input_row.addWidget(count_label)
        input_row.addWidget(self.mac_count_spin)
        input_row.addStretch()
        input_row.addWidget(self.generate_mac_btn)
        input_row.addWidget(self.save_mac_btn)
        mac_layout.addLayout(input_row)

        # Output area
        self.mac_output = QTextEdit()
        self.mac_output.setMaximumHeight(150)
        self.mac_output.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
                font-size: 11px;
                color: {Colors.TEXT};
            }}
        """)
        self.mac_output.setPlaceholderText("Generated MAC addresses will appear here...")
        mac_layout.addWidget(self.mac_output)

        layout.addWidget(mac_group)

    def generate_macs(self):
        """Generate random MAC addresses"""
        try:
            count = self.mac_count_spin.value()
            prefix = "00:1A:79:"

            # Warning for very large numbers
            if count > 100000:
                reply = QMessageBox.question(
                    self,
                    "Large Number Warning",
                    f"You are about to generate {count:,} MAC addresses.\n"
                    f"This may take a while and use significant memory.\n\n"
                    f"Do you want to continue?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return

            macs = []
            for _ in range(count):
                # Generate random last 3 octets
                octet1 = random.randint(0, 255)
                octet2 = random.randint(0, 255)
                octet3 = random.randint(0, 255)
                mac = f"{prefix}{octet1:02X}:{octet2:02X}:{octet3:02X}"
                macs.append(mac)

            # Store generated MACs for saving
            self.generated_macs = macs

            # Display generated MACs
            self.mac_output.clear()
            self.mac_output.setPlainText('\n'.join(macs))

            # Enable save button
            self.save_mac_btn.setEnabled(True)

            # Auto-save MACs
            self.auto_save_macs(macs, count)

        except Exception as e:
            logger.error(f"Error generating MACs: {e}")
            QMessageBox.warning(self, "Error", f"Failed to generate MACs: {str(e)}")

    def auto_save_macs(self, macs, count):
        """Automatically save generated MACs to a file"""
        try:
            # Create macs directory if it doesn't exist
            if not os.path.exists("macs"):
                os.makedirs("macs")

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"macs/generated_macs_{count}_{timestamp}.txt"

            # Save MACs to file
            with open(filename, 'w') as f:
                f.write('\n'.join(macs))

            logger.info(f"Auto-saved {count} MACs to {filename}")

            # Update placeholder text to show save location
            self.mac_output.setPlaceholderText(f"Generated MAC addresses auto-saved to: {filename}")

        except Exception as e:
            logger.error(f"Error auto-saving MACs: {e}")

    def save_macs(self):
        """Manually save generated MACs to a custom location"""
        try:
            if not hasattr(self, 'generated_macs') or not self.generated_macs:
                QMessageBox.warning(self, "No MACs", "No MAC addresses to save. Generate some first!")
                return

            # Get save location from user
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"generated_macs_{len(self.generated_macs)}_{timestamp}.txt"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save MAC Addresses",
                default_filename,
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                # Save MACs to selected file
                with open(file_path, 'w') as f:
                    f.write('\n'.join(self.generated_macs))

                QMessageBox.information(
                    self,
                    "Saved Successfully",
                    f"Saved {len(self.generated_macs)} MAC addresses to:\n{file_path}"
                )
                logger.info(f"Manually saved {len(self.generated_macs)} MACs to {file_path}")

        except Exception as e:
            logger.error(f"Error saving MACs: {e}")
            QMessageBox.warning(self, "Error", f"Failed to save MACs: {str(e)}")

    def setup_portal_converter(self, layout):
        """Setup portal converter section"""
        # Portal Converter Group
        portal_group = QGroupBox("🔄 Portal Converter")
        portal_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        portal_layout = QVBoxLayout(portal_group)
        portal_layout.setSpacing(10)

        # Description
        desc_label = QLabel("Convert Portal URL + MAC to Username/Password format")
        desc_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 12px; padding: 5px;")
        portal_layout.addWidget(desc_label)

        # Portal URL input
        url_layout = QHBoxLayout()
        url_label = QLabel("Portal URL:")
        url_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.portal_url_input = QLineEdit()
        self.portal_url_input.setPlaceholderText("http://example.com:8080/stalker_portal/c/")
        self.portal_url_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }}
        """)
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.portal_url_input)
        portal_layout.addLayout(url_layout)

        # MAC address input
        mac_layout = QHBoxLayout()
        mac_label = QLabel("MAC Address:")
        mac_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.portal_mac_input = QLineEdit()
        self.portal_mac_input.setPlaceholderText("00:1A:79:XX:XX:XX")
        self.portal_mac_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }}
        """)
        mac_layout.addWidget(mac_label)
        mac_layout.addWidget(self.portal_mac_input)
        portal_layout.addLayout(mac_layout)

        # Convert button and progress
        button_layout = QHBoxLayout()
        self.convert_portal_btn = QPushButton("🔄 Convert Portal")
        self.convert_portal_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
        """)
        self.convert_portal_btn.clicked.connect(self.convert_portal)

        self.portal_progress = QProgressBar()
        self.portal_progress.setVisible(False)
        self.portal_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {Colors.PRIMARY};
                border-radius: 5px;
                text-align: center;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.SUCCESS};
                border-radius: 3px;
            }}
        """)

        button_layout.addWidget(self.convert_portal_btn)
        button_layout.addWidget(self.portal_progress)
        button_layout.addStretch()
        portal_layout.addLayout(button_layout)

        # Output area
        self.portal_output = QTextEdit()
        self.portal_output.setMaximumHeight(150)
        self.portal_output.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
                font-size: 11px;
                color: {Colors.TEXT};
            }}
        """)
        self.portal_output.setPlaceholderText("Conversion results will appear here...")
        portal_layout.addWidget(self.portal_output)

        layout.addWidget(portal_group)

    def convert_portal(self):
        """Convert portal URL and MAC to username/password"""
        try:
            portal_url = self.portal_url_input.text().strip()
            mac_address = self.portal_mac_input.text().strip()

            if not portal_url or not mac_address:
                QMessageBox.warning(self, "Input Error", "Please enter both Portal URL and MAC address")
                return

            # Show progress
            self.portal_progress.setVisible(True)
            self.portal_progress.setValue(0)
            self.convert_portal_btn.setEnabled(False)

            # Create and start converter thread
            self.portal_converter_thread = PortalConverterThread(portal_url, mac_address)
            self.portal_converter_thread.progress_updated.connect(self.portal_progress.setValue)
            self.portal_converter_thread.conversion_completed.connect(self.on_portal_conversion_completed)
            self.portal_converter_thread.error_occurred.connect(self.on_portal_conversion_error)
            self.portal_converter_thread.finished.connect(self.on_portal_conversion_finished)
            self.portal_converter_thread.start()

        except Exception as e:
            logger.error(f"Error starting portal conversion: {e}")
            QMessageBox.warning(self, "Error", f"Failed to start conversion: {str(e)}")
            self.on_portal_conversion_finished()

    def on_portal_conversion_completed(self, result):
        """Handle portal conversion completion"""
        try:
            output_text = f"Portal Conversion Results:\n"
            output_text += f"{'='*60}\n"
            output_text += f"Original Portal URL: {result.get('original_url', 'N/A')}\n"
            output_text += f"Original MAC Address: {result.get('original_mac', 'N/A')}\n"
            output_text += f"Base Server URL: {result.get('base_url', 'N/A')}\n"
            output_text += f"Extraction Method: {result.get('extraction_method', 'Unknown')}\n"
            output_text += f"\n"

            if result.get('success') and result.get('username') and result.get('password'):
                output_text += f"✅ CREDENTIALS SUCCESSFULLY EXTRACTED:\n"
                output_text += f"{'='*40}\n"
                output_text += f"Username: {result.get('username')}\n"
                output_text += f"Password: {result.get('password')}\n"
                output_text += f"\n"
                output_text += f"🔗 CONVERTED XTREAM-CODES URL:\n"
                output_text += f"{'='*40}\n"
                output_text += f"{result.get('converted_url', 'Not available')}\n"
                output_text += f"\n"

                if result.get('sample_stream_url'):
                    output_text += f"📺 Sample VOD URL analyzed:\n"
                    output_text += f"{result.get('sample_stream_url')}\n"
                    output_text += f"\n"

                output_text += f"📋 HOW TO USE:\n"
                output_text += f"{'='*20}\n"
                output_text += f"1. Copy the converted URL above\n"
                output_text += f"2. Use it in any IPTV player that supports Xtream-Codes\n"
                output_text += f"3. Or use the username/password in apps like TiviMate, IPTV Smarters, etc.\n"

            else:
                output_text += f"❌ EXTRACTION FAILED:\n"
                output_text += f"{'='*30}\n"
                output_text += f"Reason: {result.get('message', 'Unknown error')}\n"
                output_text += f"\n"
                output_text += f"🔧 POSSIBLE REASONS:\n"
                output_text += f"{'='*25}\n"
                output_text += f"1. This portal does not embed credentials in VOD URLs\n"
                output_text += f"2. Portal uses a different authentication method\n"
                output_text += f"3. Portal has no VOD content available\n"
                output_text += f"4. Portal requires different access method\n"
                output_text += f"5. Network connectivity issues\n"
                output_text += f"\n"
                output_text += f"💡 ALTERNATIVE SOLUTIONS:\n"
                output_text += f"{'='*30}\n"
                output_text += f"• Contact your IPTV provider for Xtream-Codes credentials\n"
                output_text += f"• Ask for username/password format instead of MAC\n"
                output_text += f"• Check if provider offers M3U playlist downloads\n"
                output_text += f"• Verify the portal URL format is correct\n"

            output_text += f"\n"
            output_text += f"Status: {result.get('message', 'No message')}\n"

            self.portal_output.clear()
            self.portal_output.setPlainText(output_text)

        except Exception as e:
            logger.error(f"Error displaying portal conversion results: {e}")

    def on_portal_conversion_error(self, error_msg):
        """Handle portal conversion error"""
        self.portal_output.clear()
        self.portal_output.setPlainText(f"Error: {error_msg}")

    def on_portal_conversion_finished(self):
        """Handle portal conversion thread finished"""
        self.portal_progress.setVisible(False)
        self.convert_portal_btn.setEnabled(True)

    def setup_proxy_grabber(self, layout):
        """Setup proxy grabber section"""
        # Proxy Grabber Group
        proxy_group = QGroupBox("🌐 Proxy Grabber")
        proxy_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                color: {Colors.TEXT};
                border: 2px solid {Colors.PRIMARY};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        proxy_layout = QVBoxLayout(proxy_group)
        proxy_layout.setSpacing(10)

        # Description
        desc_label = QLabel("Grab free proxies from various sources (Auto-saved to proxies/ folder)")
        desc_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 12px; padding: 5px;")
        proxy_layout.addWidget(desc_label)

        # Proxy type selection
        type_layout = QHBoxLayout()
        type_label = QLabel("Proxy Type:")
        type_label.setStyleSheet(f"color: {Colors.TEXT}; font-weight: bold;")
        self.proxy_type_combo = QComboBox()
        self.proxy_type_combo.addItems(["HTTP", "HTTPS", "SOCKS4", "SOCKS5"])
        self.proxy_type_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
                min-width: 100px;
            }}
        """)
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.proxy_type_combo)
        type_layout.addStretch()
        proxy_layout.addLayout(type_layout)

        # Grab button and progress
        button_layout = QHBoxLayout()
        self.grab_proxies_btn = QPushButton("🔍 Grab Proxies")
        self.grab_proxies_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.WARNING};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.WARNING}DD;
            }}
        """)
        self.grab_proxies_btn.clicked.connect(self.grab_proxies)

        # Save proxies button
        self.save_proxies_btn = QPushButton("💾 Save Proxies")
        self.save_proxies_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
        """)
        self.save_proxies_btn.clicked.connect(self.save_proxies)
        self.save_proxies_btn.setEnabled(False)  # Initially disabled

        self.proxy_progress = QProgressBar()
        self.proxy_progress.setVisible(False)
        self.proxy_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {Colors.PRIMARY};
                border-radius: 5px;
                text-align: center;
            }}
            QProgressBar::chunk {{
                background-color: {Colors.SUCCESS};
                border-radius: 3px;
            }}
        """)

        # Status label
        self.proxy_status_label = QLabel("")
        self.proxy_status_label.setStyleSheet(f"color: {Colors.TEXT}; font-size: 11px;")

        button_layout.addWidget(self.grab_proxies_btn)
        button_layout.addWidget(self.save_proxies_btn)
        button_layout.addWidget(self.proxy_progress)
        button_layout.addStretch()
        proxy_layout.addLayout(button_layout)
        proxy_layout.addWidget(self.proxy_status_label)

        # Output area
        self.proxy_output = QTextEdit()
        self.proxy_output.setMaximumHeight(150)
        self.proxy_output.setStyleSheet(f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-family: monospace;
                font-size: 11px;
                color: {Colors.TEXT};
            }}
        """)
        self.proxy_output.setPlaceholderText("Grabbed proxies will appear here...")
        proxy_layout.addWidget(self.proxy_output)

        layout.addWidget(proxy_group)

    def grab_proxies(self):
        """Grab proxies from various sources"""
        try:
            proxy_type = self.proxy_type_combo.currentText()

            # Show progress
            self.proxy_progress.setVisible(True)
            self.proxy_progress.setValue(0)
            self.grab_proxies_btn.setEnabled(False)
            self.proxy_status_label.setText("Starting proxy grabber...")

            # Create and start proxy grabber thread
            self.proxy_grabber_thread = ProxyGrabberThread(proxy_type)
            self.proxy_grabber_thread.progress_updated.connect(self.proxy_progress.setValue)
            self.proxy_grabber_thread.status_updated.connect(self.proxy_status_label.setText)
            self.proxy_grabber_thread.proxies_found.connect(self.on_proxies_found)
            self.proxy_grabber_thread.error_occurred.connect(self.on_proxy_grabber_error)
            self.proxy_grabber_thread.finished.connect(self.on_proxy_grabber_finished)
            self.proxy_grabber_thread.start()

        except Exception as e:
            logger.error(f"Error starting proxy grabber: {e}")
            QMessageBox.warning(self, "Error", f"Failed to start proxy grabber: {str(e)}")
            self.on_proxy_grabber_finished()

    def on_proxies_found(self, proxies):
        """Handle proxies found"""
        try:
            if proxies:
                # Store proxies for saving
                self.grabbed_proxies = proxies
                proxy_type = self.proxy_type_combo.currentText()

                # Display proxies
                self.proxy_output.clear()
                self.proxy_output.setPlainText('\n'.join(proxies))
                self.proxy_status_label.setText(f"✅ Found {len(proxies)} proxies")

                # Enable save button
                self.save_proxies_btn.setEnabled(True)

                # Auto-save proxies
                self.auto_save_proxies(proxies, proxy_type)
            else:
                self.proxy_output.clear()
                self.proxy_output.setPlainText("No proxies found")
                self.proxy_status_label.setText("❌ No proxies found")
                self.save_proxies_btn.setEnabled(False)

        except Exception as e:
            logger.error(f"Error displaying proxies: {e}")

    def on_proxy_grabber_error(self, error_msg):
        """Handle proxy grabber error"""
        self.proxy_output.clear()
        self.proxy_output.setPlainText(f"Error: {error_msg}")
        self.proxy_status_label.setText(f"❌ Error: {error_msg}")

    def on_proxy_grabber_finished(self):
        """Handle proxy grabber thread finished"""
        self.proxy_progress.setVisible(False)
        self.grab_proxies_btn.setEnabled(True)

    def auto_save_proxies(self, proxies, proxy_type):
        """Automatically save grabbed proxies to a file"""
        try:
            # Create proxies directory if it doesn't exist
            if not os.path.exists("proxies"):
                os.makedirs("proxies")

            # Generate filename with timestamp and proxy type
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"proxies/{proxy_type.lower()}_proxies_{len(proxies)}_{timestamp}.txt"

            # Save proxies to file
            with open(filename, 'w') as f:
                f.write('\n'.join(proxies))

            logger.info(f"Auto-saved {len(proxies)} {proxy_type} proxies to {filename}")

            # Update placeholder text to show save location
            self.proxy_output.setPlaceholderText(f"Grabbed {proxy_type} proxies auto-saved to: {filename}")

        except Exception as e:
            logger.error(f"Error auto-saving proxies: {e}")

    def save_proxies(self):
        """Manually save grabbed proxies to a custom location"""
        try:
            if not hasattr(self, 'grabbed_proxies') or not self.grabbed_proxies:
                QMessageBox.warning(self, "No Proxies", "No proxies to save. Grab some first!")
                return

            # Get proxy type and timestamp
            proxy_type = self.proxy_type_combo.currentText()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"{proxy_type.lower()}_proxies_{len(self.grabbed_proxies)}_{timestamp}.txt"

            # Get save location from user
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Proxies",
                default_filename,
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                # Save proxies to selected file
                with open(file_path, 'w') as f:
                    f.write('\n'.join(self.grabbed_proxies))

                QMessageBox.information(
                    self,
                    "Saved Successfully",
                    f"Saved {len(self.grabbed_proxies)} {proxy_type} proxies to:\n{file_path}"
                )
                logger.info(f"Manually saved {len(self.grabbed_proxies)} {proxy_type} proxies to {file_path}")

        except Exception as e:
            logger.error(f"Error saving proxies: {e}")
            QMessageBox.warning(self, "Error", f"Failed to save proxies: {str(e)}")
