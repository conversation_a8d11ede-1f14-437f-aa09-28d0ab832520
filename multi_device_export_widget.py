"""
Multi-Device Export Widget
Professional IPTV playlist export for different apps and devices
"""

import os
import json
import qrcode
import io
import base64
from datetime import datetime
from urllib.parse import quote, urlencode
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTextEdit, QProgressBar,
                            QGroupBox, QGridLayout, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QFileDialog, QComboBox,
                            QCheckBox, QSpinBox, QTabWidget, QSplitter,
                            QFrame, QScrollArea, QDialog, QDialogButtonBox,
                            QListWidget, QListWidgetItem, QTextBrowser, QApplication)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPixmap, QIcon

class DeviceProfile:
    """Device/App profile with specific requirements"""
    
    def __init__(self, name, app_type, requirements=None):
        self.name = name
        self.app_type = app_type
        self.requirements = requirements or {}
        self.icon = self.get_icon()
        self.setup_instructions = self.get_setup_instructions()
    
    def get_icon(self):
        """Get icon for device/app type"""
        icons = {
            "tivimate": "📱",
            "iptv_smarters": "📺", 
            "perfect_player": "🎮",
            "kodi": "🖥️",
            "vlc": "📻",
            "smart_tv": "📟",
            "android_tv": "🤖",
            "ios": "🍎",
            "web": "🌐",
            "generic": "📋"
        }
        return icons.get(self.app_type, "📱")
    
    def get_setup_instructions(self):
        """Get setup instructions for this device/app"""
        instructions = {
            "tivimate": [
                "1. Open TiviMate app on your device",
                "2. Go to Settings → Playlists",
                "3. Click 'Add Playlist'",
                "4. Select 'M3U URL' option",
                "5. Scan the QR code or paste the URL",
                "6. EPG will be configured automatically"
            ],
            "iptv_smarters": [
                "1. Open IPTV Smarters Pro app",
                "2. Click 'Add User'",
                "3. Select 'Load Your Playlist or File/URL'",
                "4. Choose 'M3U URL' option",
                "5. Paste the playlist URL",
                "6. Enter the EPG URL if provided",
                "7. Click 'Add User' to save"
            ],
            "perfect_player": [
                "1. Open Perfect Player app",
                "2. Go to Settings (gear icon)",
                "3. Select 'General' → 'Playlist'",
                "4. Click 'Add playlist'",
                "5. Enter playlist name and URL",
                "6. Save and restart the app"
            ],
            "kodi": [
                "1. Open Kodi media center",
                "2. Go to Add-ons → PVR clients",
                "3. Install 'PVR IPTV Simple Client'",
                "4. Configure with M3U URL",
                "5. Set EPG source if available",
                "6. Enable the PVR client"
            ],
            "vlc": [
                "1. Open VLC Media Player",
                "2. Go to Media → Open Network Stream",
                "3. Paste the playlist URL",
                "4. Click 'Play' to start streaming",
                "5. Bookmark for easy access"
            ],
            "smart_tv": [
                "1. Open your Smart TV's browser",
                "2. Navigate to the playlist URL",
                "3. Download the M3U file",
                "4. Open with your TV's media player",
                "5. Or use your TV's IPTV app with the URL"
            ],
            "android_tv": [
                "1. Install an IPTV app from Play Store",
                "2. Open the app on your Android TV",
                "3. Go to playlist settings",
                "4. Add M3U URL or scan QR code",
                "5. Configure EPG if supported"
            ],
            "ios": [
                "1. Install an IPTV app from App Store",
                "2. Open the app on your iOS device",
                "3. Go to playlist/channel settings",
                "4. Add M3U URL or scan QR code",
                "5. Save and refresh channels"
            ]
        }
        return instructions.get(self.app_type, ["Generic setup instructions not available"])

class ExportProfile:
    """Export configuration profile"""
    
    def __init__(self, device_profile, playlist_url="", epg_url="", options=None):
        self.device_profile = device_profile
        self.playlist_url = playlist_url
        self.epg_url = epg_url
        self.options = options or {}
        self.export_time = datetime.now()
        self.qr_code_data = None
        self.optimized_url = None
        self.setup_url = None

class QRCodeGenerator:
    """QR Code generation utility"""
    
    @staticmethod
    def generate_qr_code(data, size=(200, 200)):
        """Generate QR code for given data"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to QPixmap
            buffer = io.BytesIO()
            qr_img.save(buffer, format='PNG')
            buffer.seek(0)
            
            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())
            
            # Scale to desired size
            scaled_pixmap = pixmap.scaled(size[0], size[1], Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            
            return scaled_pixmap
            
        except Exception as e:
            print(f"Error generating QR code: {e}")
            return None

class PlaylistOptimizer:
    """Optimize playlists for different devices"""
    
    @staticmethod
    def optimize_for_device(playlist_content, device_type, options=None):
        """Optimize playlist content for specific device"""
        options = options or {}
        
        if device_type == "tivimate":
            return PlaylistOptimizer._optimize_for_tivimate(playlist_content, options)
        elif device_type == "iptv_smarters":
            return PlaylistOptimizer._optimize_for_iptv_smarters(playlist_content, options)
        elif device_type == "smart_tv":
            return PlaylistOptimizer._optimize_for_smart_tv(playlist_content, options)
        elif device_type == "android_tv":
            return PlaylistOptimizer._optimize_for_android_tv(playlist_content, options)
        elif device_type == "ios":
            return PlaylistOptimizer._optimize_for_ios(playlist_content, options)
        else:
            return playlist_content  # Generic format
    
    @staticmethod
    def _optimize_for_tivimate(content, options):
        """Optimize for TiviMate app"""
        lines = content.split('\n')
        optimized_lines = []
        
        for line in lines:
            if line.startswith('#EXTINF:'):
                # TiviMate prefers shorter channel names for mobile
                if options.get('mobile_friendly', True):
                    # Truncate very long names
                    if len(line) > 150:
                        # Find the title part (after last comma)
                        parts = line.split(',')
                        if len(parts) > 1:
                            title = parts[-1]
                            if len(title) > 50:
                                title = title[:47] + "..."
                                parts[-1] = title
                                line = ','.join(parts)
                
                # Add TiviMate-specific attributes if needed
                if 'tvg-rec=' not in line and options.get('add_recording_support', False):
                    line = line.replace('#EXTINF:', '#EXTINF:').replace(',', ' tvg-rec="1",', 1)
                
                optimized_lines.append(line)
            else:
                optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    @staticmethod
    def _optimize_for_iptv_smarters(content, options):
        """Optimize for IPTV Smarters Pro"""
        lines = content.split('\n')
        optimized_lines = []
        
        for line in lines:
            if line.startswith('#EXTINF:'):
                # IPTV Smarters works well with full metadata
                # Ensure group-title is present for proper categorization
                if 'group-title=' not in line:
                    line = line.replace(',', ' group-title="General",', 1)
                
                optimized_lines.append(line)
            else:
                optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    @staticmethod
    def _optimize_for_smart_tv(content, options):
        """Optimize for Smart TV browsers"""
        lines = content.split('\n')
        optimized_lines = []
        
        for line in lines:
            if line.startswith('#EXTINF:'):
                # Smart TVs prefer simpler formats
                # Remove complex attributes that might cause issues
                if options.get('simplify_format', True):
                    # Keep only essential attributes
                    essential_attrs = ['tvg-id', 'tvg-name', 'tvg-logo', 'group-title']
                    # This is a simplified approach - in practice, you'd parse and rebuild
                    pass
                
                optimized_lines.append(line)
            else:
                optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    @staticmethod
    def _optimize_for_android_tv(content, options):
        """Optimize for Android TV"""
        # Android TV can handle full metadata but benefits from larger logos
        return content  # For now, return as-is
    
    @staticmethod
    def _optimize_for_ios(content, options):
        """Optimize for iOS devices"""
        # iOS devices prefer standard M3U format
        return content  # For now, return as-is

class ExportDialog(QDialog):
    """Dialog for configuring export settings"""
    
    def __init__(self, device_profile, parent=None):
        super().__init__(parent)
        self.device_profile = device_profile
        self.setWindowTitle(f"Export for {device_profile.name}")
        self.setModal(True)
        self.resize(500, 400)
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Device info
        device_group = QGroupBox(f"{self.device_profile.icon} {self.device_profile.name}")
        device_layout = QVBoxLayout()
        
        device_info = QLabel(f"Optimizing playlist for {self.device_profile.name}")
        device_info.setStyleSheet("font-weight: bold; color: #2196F3;")
        device_layout.addWidget(device_info)
        
        device_group.setLayout(device_layout)
        layout.addWidget(device_group)
        
        # URLs
        urls_group = QGroupBox("📡 Playlist Configuration")
        urls_layout = QGridLayout()
        
        urls_layout.addWidget(QLabel("Playlist URL:"), 0, 0)
        self.playlist_url_input = QLineEdit()
        self.playlist_url_input.setPlaceholderText("http://example.com/playlist.m3u")
        urls_layout.addWidget(self.playlist_url_input, 0, 1)
        
        urls_layout.addWidget(QLabel("EPG URL:"), 1, 0)
        self.epg_url_input = QLineEdit()
        self.epg_url_input.setPlaceholderText("http://example.com/epg.xml (optional)")
        urls_layout.addWidget(self.epg_url_input, 1, 1)
        
        urls_group.setLayout(urls_layout)
        layout.addWidget(urls_group)
        
        # Optimization options
        options_group = QGroupBox("⚙️ Optimization Options")
        options_layout = QVBoxLayout()
        
        self.mobile_friendly_cb = QCheckBox("Mobile-friendly optimization")
        self.mobile_friendly_cb.setChecked(True)
        options_layout.addWidget(self.mobile_friendly_cb)
        
        self.add_recording_cb = QCheckBox("Add recording support (if supported)")
        options_layout.addWidget(self.add_recording_cb)
        
        self.simplify_format_cb = QCheckBox("Simplify format for compatibility")
        options_layout.addWidget(self.simplify_format_cb)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def get_export_config(self):
        """Get export configuration from dialog"""
        return {
            'playlist_url': self.playlist_url_input.text().strip(),
            'epg_url': self.epg_url_input.text().strip(),
            'mobile_friendly': self.mobile_friendly_cb.isChecked(),
            'add_recording_support': self.add_recording_cb.isChecked(),
            'simplify_format': self.simplify_format_cb.isChecked()
        }

class MultiDeviceExportWidget(QWidget):
    """Main Multi-Device Export Widget"""

    def __init__(self):
        super().__init__()
        self.device_profiles = self.create_device_profiles()
        self.export_history = []
        self.last_export_results = []
        self.init_ui()

    def create_device_profiles(self):
        """Create predefined device profiles"""
        profiles = [
            DeviceProfile("TiviMate", "tivimate"),
            DeviceProfile("IPTV Smarters Pro", "iptv_smarters"),
            DeviceProfile("Perfect Player", "perfect_player"),
            DeviceProfile("Kodi PVR", "kodi"),
            DeviceProfile("VLC Media Player", "vlc"),
            DeviceProfile("Smart TV Browser", "smart_tv"),
            DeviceProfile("Android TV", "android_tv"),
            DeviceProfile("iOS IPTV Apps", "ios"),
            DeviceProfile("Web Player", "web"),
            DeviceProfile("Generic M3U", "generic")
        ]
        return profiles

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()

        # Header
        header_layout = QHBoxLayout()

        title_label = QLabel("📱 Multi-Device Export")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196F3;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Quick export button
        self.quick_export_button = QPushButton("⚡ Quick Export All")
        self.quick_export_button.clicked.connect(self.quick_export_all)
        header_layout.addWidget(self.quick_export_button)

        layout.addLayout(header_layout)

        # Description
        desc_label = QLabel("Export your IPTV playlist optimized for different apps and devices. Each export includes QR codes, setup instructions, and device-specific optimizations.")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(desc_label)

        # Main content splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel - Device selection
        left_panel = QWidget()
        left_layout = QVBoxLayout()

        # Source configuration
        source_group = QGroupBox("📡 Source Configuration")
        source_layout = QGridLayout()

        source_layout.addWidget(QLabel("Playlist URL:"), 0, 0)
        self.source_playlist_input = QLineEdit()
        self.source_playlist_input.setPlaceholderText("http://example.com/playlist.m3u")
        source_layout.addWidget(self.source_playlist_input, 0, 1)

        self.load_playlist_button = QPushButton("📂 Load")
        self.load_playlist_button.clicked.connect(self.load_playlist_file)
        source_layout.addWidget(self.load_playlist_button, 0, 2)

        source_layout.addWidget(QLabel("EPG URL:"), 1, 0)
        self.source_epg_input = QLineEdit()
        self.source_epg_input.setPlaceholderText("http://example.com/epg.xml (optional)")
        source_layout.addWidget(self.source_epg_input, 1, 1, 1, 2)

        source_group.setLayout(source_layout)
        left_layout.addWidget(source_group)

        # Device selection
        devices_group = QGroupBox("📱 Select Target Devices")
        devices_layout = QVBoxLayout()

        # Device list
        self.device_list = QListWidget()
        self.populate_device_list()
        devices_layout.addWidget(self.device_list)

        # Device controls
        device_controls = QHBoxLayout()

        self.export_selected_button = QPushButton("📤 Export Selected")
        self.export_selected_button.clicked.connect(self.export_selected_devices)
        device_controls.addWidget(self.export_selected_button)

        self.select_all_button = QPushButton("✅ Select All")
        self.select_all_button.clicked.connect(self.select_all_devices)
        device_controls.addWidget(self.select_all_button)

        self.clear_selection_button = QPushButton("❌ Clear All")
        self.clear_selection_button.clicked.connect(self.clear_device_selection)
        device_controls.addWidget(self.clear_selection_button)

        devices_layout.addLayout(device_controls)
        devices_group.setLayout(devices_layout)
        left_layout.addWidget(devices_group)

        left_panel.setLayout(left_layout)
        main_splitter.addWidget(left_panel)

        # Right panel - Export results
        right_panel = QWidget()
        right_layout = QVBoxLayout()

        # Export results
        results_group = QGroupBox("📋 Export Results")
        results_layout = QVBoxLayout()

        # Results tabs
        self.results_tabs = QTabWidget()

        # QR Codes tab
        qr_tab = QWidget()
        qr_layout = QVBoxLayout()

        self.qr_scroll = QScrollArea()
        self.qr_content = QWidget()
        self.qr_content_layout = QVBoxLayout()
        self.qr_content.setLayout(self.qr_content_layout)
        self.qr_scroll.setWidget(self.qr_content)
        self.qr_scroll.setWidgetResizable(True)
        qr_layout.addWidget(self.qr_scroll)

        qr_tab.setLayout(qr_layout)
        self.results_tabs.addTab(qr_tab, "📱 QR Codes")

        # URLs tab
        urls_tab = QWidget()
        urls_layout = QVBoxLayout()

        self.urls_text = QTextEdit()
        self.urls_text.setPlaceholderText("Export results will appear here...")
        urls_layout.addWidget(self.urls_text)

        # URL controls
        url_controls = QHBoxLayout()

        self.copy_urls_button = QPushButton("📋 Copy All URLs")
        self.copy_urls_button.clicked.connect(self.copy_all_urls)
        url_controls.addWidget(self.copy_urls_button)

        self.save_urls_button = QPushButton("💾 Save URLs")
        self.save_urls_button.clicked.connect(self.save_urls_to_file)
        url_controls.addWidget(self.save_urls_button)

        self.export_playlists_button = QPushButton("📁 Export Playlist Files")
        self.export_playlists_button.clicked.connect(self.export_playlist_files)
        url_controls.addWidget(self.export_playlists_button)

        url_controls.addStretch()
        urls_layout.addLayout(url_controls)

        urls_tab.setLayout(urls_layout)
        self.results_tabs.addTab(urls_tab, "🔗 URLs")

        # Instructions tab
        instructions_tab = QWidget()
        instructions_layout = QVBoxLayout()

        self.instructions_browser = QTextBrowser()
        self.instructions_browser.setPlaceholderText("Setup instructions will appear here...")
        instructions_layout.addWidget(self.instructions_browser)

        instructions_tab.setLayout(instructions_layout)
        self.results_tabs.addTab(instructions_tab, "📖 Instructions")

        results_layout.addWidget(self.results_tabs)
        results_group.setLayout(results_layout)
        right_layout.addWidget(results_group)

        # Export history
        history_group = QGroupBox("📊 Export History")
        history_layout = QVBoxLayout()

        self.history_list = QListWidget()
        self.history_list.setMaximumHeight(100)
        history_layout.addWidget(self.history_list)

        history_controls = QHBoxLayout()

        self.clear_history_button = QPushButton("🗑️ Clear History")
        self.clear_history_button.clicked.connect(self.clear_export_history)
        history_controls.addWidget(self.clear_history_button)

        history_controls.addStretch()
        history_layout.addLayout(history_controls)

        history_group.setLayout(history_layout)
        right_layout.addWidget(history_group)

        right_panel.setLayout(right_layout)
        main_splitter.addWidget(right_panel)

        # Set splitter proportions
        main_splitter.setSizes([400, 600])
        layout.addWidget(main_splitter)

        # Status bar
        self.status_label = QLabel("Ready to export")
        self.status_label.setStyleSheet("color: #666; padding: 5px;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def populate_device_list(self):
        """Populate the device selection list"""
        for profile in self.device_profiles:
            item = QListWidgetItem(f"{profile.icon} {profile.name}")
            item.setData(Qt.ItemDataRole.UserRole, profile)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            item.setCheckState(Qt.CheckState.Unchecked)
            self.device_list.addItem(item)

    def load_playlist_file(self):
        """Load playlist file and set URL"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load Playlist File",
            "",
            "Playlist Files (*.m3u *.m3u8);;All Files (*)"
        )

        if file_path:
            # For local files, we'd need to serve them or copy to a web-accessible location
            # For now, just show the file path
            self.source_playlist_input.setText(f"file://{file_path}")
            self.status_label.setText(f"Loaded playlist: {os.path.basename(file_path)}")

    def select_all_devices(self):
        """Select all devices in the list"""
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            item.setCheckState(Qt.CheckState.Checked)

    def clear_device_selection(self):
        """Clear all device selections"""
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            item.setCheckState(Qt.CheckState.Unchecked)

    def get_selected_devices(self):
        """Get list of selected device profiles"""
        selected = []
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                profile = item.data(Qt.ItemDataRole.UserRole)
                selected.append(profile)
        return selected

    def export_selected_devices(self):
        """Export for selected devices"""
        selected_devices = self.get_selected_devices()

        if not selected_devices:
            QMessageBox.warning(self, "Warning", "Please select at least one device to export to.")
            return

        playlist_url = self.source_playlist_input.text().strip()
        if not playlist_url:
            QMessageBox.warning(self, "Warning", "Please enter a playlist URL.")
            return

        epg_url = self.source_epg_input.text().strip()

        # Clear previous results
        self.clear_results()

        # Export for each selected device
        export_results = []
        for device_profile in selected_devices:
            try:
                result = self.export_for_device(device_profile, playlist_url, epg_url)
                export_results.append(result)
                self.add_to_history(device_profile.name, playlist_url)
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export for {device_profile.name}:\n{str(e)}")

        # Store results for file export
        self.last_export_results = export_results

        # Display results
        self.display_export_results(export_results)
        self.status_label.setText(f"✅ Exported for {len(export_results)} devices")

    def export_for_device(self, device_profile, playlist_url, epg_url=""):
        """Export playlist for specific device with real optimization"""
        # Create export profile
        export_profile = ExportProfile(device_profile, playlist_url, epg_url)

        # Generate device-specific optimized URL and content
        optimized_data = self.generate_device_specific_export(device_profile, playlist_url, epg_url)
        export_profile.optimized_url = optimized_data['url']
        export_profile.format_info = optimized_data['format']
        export_profile.setup_url = optimized_data.get('setup_url', optimized_data['url'])

        # Create QR code data with device-specific information
        qr_data = {
            "url": export_profile.optimized_url,
            "format": optimized_data['format'],
            "device": device_profile.name,
            "app_type": device_profile.app_type
        }

        # Create comprehensive QR text
        qr_text_lines = [f"📱 {device_profile.name}"]
        qr_text_lines.append(f"Format: {optimized_data['format']}")
        qr_text_lines.append(f"URL: {export_profile.optimized_url}")
        if epg_url:
            qr_text_lines.append(f"EPG: {epg_url}")

        export_profile.qr_code_data = "\n".join(qr_text_lines)

        return export_profile

    def generate_device_specific_export(self, device_profile, playlist_url, epg_url=""):
        """Generate device-specific URL and format"""
        app_type = device_profile.app_type

        # Device-specific URL modifications and formats
        if app_type == "tivimate":
            return {
                'url': self.optimize_url_for_tivimate(playlist_url),
                'format': 'M3U8 (TiviMate Optimized)',
                'setup_url': f"tivimate://playlist?url={quote(playlist_url)}" if playlist_url.startswith('http') else playlist_url
            }
        elif app_type == "iptv_smarters":
            return {
                'url': self.optimize_url_for_iptv_smarters(playlist_url),
                'format': 'M3U (IPTV Smarters Format)',
                'setup_url': playlist_url
            }
        elif app_type == "vlc":
            return {
                'url': self.optimize_url_for_vlc(playlist_url),
                'format': 'M3U8 (VLC Compatible)',
                'setup_url': f"vlc://{playlist_url}" if playlist_url.startswith('http') else playlist_url
            }
        elif app_type == "kodi":
            return {
                'url': self.optimize_url_for_kodi(playlist_url),
                'format': 'M3U (Kodi PVR Format)',
                'setup_url': playlist_url
            }
        elif app_type == "perfect_player":
            return {
                'url': self.optimize_url_for_perfect_player(playlist_url),
                'format': 'M3U8 (Perfect Player)',
                'setup_url': playlist_url
            }
        elif app_type == "smart_tv":
            return {
                'url': self.optimize_url_for_smart_tv(playlist_url),
                'format': 'M3U (Smart TV Compatible)',
                'setup_url': playlist_url
            }
        elif app_type == "android_tv":
            return {
                'url': self.optimize_url_for_android_tv(playlist_url),
                'format': 'M3U8 (Android TV)',
                'setup_url': playlist_url
            }
        elif app_type == "ios":
            return {
                'url': self.optimize_url_for_ios(playlist_url),
                'format': 'M3U8 (iOS Compatible)',
                'setup_url': playlist_url
            }
        else:  # generic
            return {
                'url': playlist_url,
                'format': 'M3U (Generic)',
                'setup_url': playlist_url
            }

    def optimize_url_for_tivimate(self, url):
        """Optimize URL for TiviMate - prefers .m3u8 extension"""
        if url.endswith('.m3u'):
            return url.replace('.m3u', '.m3u8')
        elif '?' in url and not url.endswith('.m3u8'):
            return url + '&format=m3u8'
        return url

    def optimize_url_for_iptv_smarters(self, url):
        """Optimize URL for IPTV Smarters - works well with .m3u"""
        if url.endswith('.m3u8'):
            return url.replace('.m3u8', '.m3u')
        return url

    def optimize_url_for_vlc(self, url):
        """Optimize URL for VLC - prefers .m3u8 and specific parameters"""
        if url.endswith('.m3u'):
            url = url.replace('.m3u', '.m3u8')
        # Add VLC-specific parameters for better compatibility
        separator = '&' if '?' in url else '?'
        return f"{url}{separator}vlc=1"

    def optimize_url_for_kodi(self, url):
        """Optimize URL for Kodi PVR - standard .m3u format"""
        if url.endswith('.m3u8'):
            return url.replace('.m3u8', '.m3u')
        return url

    def optimize_url_for_perfect_player(self, url):
        """Optimize URL for Perfect Player - prefers .m3u8"""
        if url.endswith('.m3u'):
            return url.replace('.m3u', '.m3u8')
        return url

    def optimize_url_for_smart_tv(self, url):
        """Optimize URL for Smart TV - simple .m3u format"""
        if url.endswith('.m3u8'):
            return url.replace('.m3u8', '.m3u')
        # Remove complex parameters that might cause issues
        if '?' in url:
            base_url = url.split('?')[0]
            return base_url if base_url.endswith('.m3u') else f"{base_url}.m3u"
        return url

    def optimize_url_for_android_tv(self, url):
        """Optimize URL for Android TV - .m3u8 format"""
        if url.endswith('.m3u'):
            return url.replace('.m3u', '.m3u8')
        return url

    def optimize_url_for_ios(self, url):
        """Optimize URL for iOS - .m3u8 format (HLS standard)"""
        if url.endswith('.m3u'):
            return url.replace('.m3u', '.m3u8')
        return url

    def quick_export_all(self):
        """Quick export for all popular devices"""
        # Select popular devices automatically
        popular_devices = ["TiviMate", "IPTV Smarters Pro", "VLC Media Player", "Generic M3U"]

        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            profile = item.data(Qt.ItemDataRole.UserRole)
            if profile.name in popular_devices:
                item.setCheckState(Qt.CheckState.Checked)
            else:
                item.setCheckState(Qt.CheckState.Unchecked)

        # Export selected devices
        self.export_selected_devices()

    def clear_results(self):
        """Clear all export results"""
        # Clear QR codes
        for i in reversed(range(self.qr_content_layout.count())):
            child = self.qr_content_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # Clear URLs
        self.urls_text.clear()

        # Clear instructions
        self.instructions_browser.clear()

    def display_export_results(self, export_results):
        """Display export results in the UI"""
        if not export_results:
            return

        # Generate QR codes
        self.generate_qr_codes(export_results)

        # Generate URLs text
        self.generate_urls_text(export_results)

        # Generate instructions
        self.generate_instructions(export_results)

    def generate_qr_codes(self, export_results):
        """Generate and display QR codes"""
        for export_profile in export_results:
            # Create QR code widget
            qr_widget = QWidget()
            qr_layout = QVBoxLayout()

            # Device header
            header_label = QLabel(f"{export_profile.device_profile.icon} {export_profile.device_profile.name}")
            header_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2196F3;")
            qr_layout.addWidget(header_label)

            # Generate QR code
            qr_pixmap = QRCodeGenerator.generate_qr_code(export_profile.qr_code_data, (150, 150))

            if qr_pixmap:
                qr_label = QLabel()
                qr_label.setPixmap(qr_pixmap)
                qr_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                qr_layout.addWidget(qr_label)
            else:
                error_label = QLabel("❌ QR Code generation failed")
                error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                qr_layout.addWidget(error_label)

            # Format and URL info
            format_label = QLabel(f"Format: {getattr(export_profile, 'format_info', 'M3U')}")
            format_label.setStyleSheet("font-size: 10px; color: #2196F3; font-weight: bold;")
            qr_layout.addWidget(format_label)

            url_label = QLabel(f"URL: {export_profile.optimized_url[:45]}...")
            url_label.setStyleSheet("font-size: 9px; color: #666;")
            url_label.setWordWrap(True)
            qr_layout.addWidget(url_label)

            # Save QR button
            save_qr_button = QPushButton("💾 Save QR Code")
            save_qr_button.clicked.connect(lambda checked, ep=export_profile: self.save_qr_code(ep))
            qr_layout.addWidget(save_qr_button)

            qr_widget.setLayout(qr_layout)
            qr_widget.setMaximumWidth(200)
            qr_widget.setStyleSheet("border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin: 5px;")

            self.qr_content_layout.addWidget(qr_widget)

    def generate_urls_text(self, export_results):
        """Generate URLs text display"""
        urls_text = "📋 Device-Specific Export URLs\n" + "=" * 60 + "\n\n"

        for export_profile in export_results:
            urls_text += f"{export_profile.device_profile.icon} {export_profile.device_profile.name}\n"
            urls_text += f"Format: {getattr(export_profile, 'format_info', 'M3U')}\n"
            urls_text += f"Optimized URL: {export_profile.optimized_url}\n"

            # Show setup URL if different from optimized URL
            if hasattr(export_profile, 'setup_url') and export_profile.setup_url != export_profile.optimized_url:
                urls_text += f"Setup URL: {export_profile.setup_url}\n"

            if export_profile.epg_url:
                urls_text += f"EPG URL: {export_profile.epg_url}\n"

            urls_text += f"Export Time: {export_profile.export_time.strftime('%Y-%m-%d %H:%M:%S')}\n"

            # Show URL differences
            if export_profile.optimized_url != export_profile.playlist_url:
                urls_text += f"📝 Note: URL optimized for {export_profile.device_profile.name}\n"
                urls_text += f"Original: {export_profile.playlist_url}\n"

            urls_text += "-" * 60 + "\n\n"

        self.urls_text.setPlainText(urls_text)

    def generate_instructions(self, export_results):
        """Generate setup instructions"""
        instructions_html = "<h2>📖 Device-Specific Setup Instructions</h2>"

        for export_profile in export_results:
            instructions_html += f"<h3>{export_profile.device_profile.icon} {export_profile.device_profile.name}</h3>"

            # Format information
            format_info = getattr(export_profile, 'format_info', 'M3U')
            instructions_html += f"<p><strong>📋 Format:</strong> <span style='color: #2196F3;'>{format_info}</span></p>"

            # URL information
            instructions_html += f"<p><strong>🔗 Optimized URL:</strong> <code>{export_profile.optimized_url}</code></p>"

            # Setup URL if different
            if hasattr(export_profile, 'setup_url') and export_profile.setup_url != export_profile.optimized_url:
                instructions_html += f"<p><strong>⚡ Quick Setup URL:</strong> <code>{export_profile.setup_url}</code></p>"

            if export_profile.epg_url:
                instructions_html += f"<p><strong>📺 EPG URL:</strong> <code>{export_profile.epg_url}</code></p>"

            # Show optimization note
            if export_profile.optimized_url != export_profile.playlist_url:
                instructions_html += f"<p><em>📝 Note: URL has been optimized for {export_profile.device_profile.name}</em></p>"

            instructions_html += "<h4>Setup Steps:</h4><ol>"

            for instruction in export_profile.device_profile.setup_instructions:
                instructions_html += f"<li>{instruction}</li>"

            instructions_html += "</ol>"
            instructions_html += "<hr>"

        self.instructions_browser.setHtml(instructions_html)

    def save_qr_code(self, export_profile):
        """Save QR code to file"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                f"Save QR Code for {export_profile.device_profile.name}",
                f"qr_code_{export_profile.device_profile.app_type}.png",
                "PNG Files (*.png);;All Files (*)"
            )

            if file_path:
                qr_pixmap = QRCodeGenerator.generate_qr_code(export_profile.qr_code_data, (300, 300))
                if qr_pixmap:
                    qr_pixmap.save(file_path)
                    QMessageBox.information(self, "Success", f"QR code saved to {file_path}")
                else:
                    QMessageBox.critical(self, "Error", "Failed to generate QR code")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save QR code:\n{str(e)}")

    def copy_all_urls(self):
        """Copy all URLs to clipboard"""
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(self.urls_text.toPlainText())
            self.status_label.setText("✅ URLs copied to clipboard")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to copy URLs:\n{str(e)}")

    def save_urls_to_file(self):
        """Save URLs to text file"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Export URLs",
                f"export_urls_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.urls_text.toPlainText())
                QMessageBox.information(self, "Success", f"URLs saved to {file_path}")
                self.status_label.setText(f"✅ URLs saved to {os.path.basename(file_path)}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save URLs:\n{str(e)}")

    def export_playlist_files(self):
        """Export actual optimized playlist files for each device"""
        if not hasattr(self, 'last_export_results') or not self.last_export_results:
            QMessageBox.warning(self, "Warning", "No export results available. Please export for devices first.")
            return

        # Choose export directory
        export_dir = QFileDialog.getExistingDirectory(
            self,
            "Choose Export Directory",
            os.path.expanduser("~/Desktop"),
            QFileDialog.Option.ShowDirsOnly
        )

        if not export_dir:
            return

        try:
            # Get original playlist content
            original_url = self.source_playlist_input.text().strip()
            if not original_url:
                QMessageBox.warning(self, "Warning", "No source playlist URL available.")
                return

            # Download original playlist content
            self.status_label.setText("📥 Downloading original playlist...")
            QApplication.processEvents()  # Update UI

            playlist_content = self.download_playlist_content(original_url)
            if not playlist_content:
                QMessageBox.critical(self, "Error", "Failed to download original playlist content.")
                return

            exported_files = []

            # Create optimized files for each device
            for export_profile in self.last_export_results:
                device_name = export_profile.device_profile.name
                app_type = export_profile.device_profile.app_type

                self.status_label.setText(f"🔧 Optimizing for {device_name}...")
                QApplication.processEvents()

                # Optimize content for this device
                optimized_content = PlaylistOptimizer.optimize_for_device(
                    playlist_content,
                    app_type,
                    {'mobile_friendly': True, 'add_recording_support': False}
                )

                # Determine file extension
                file_extension = self.get_file_extension_for_device(app_type)

                # Create filename
                safe_device_name = "".join(c for c in device_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_device_name = safe_device_name.replace(' ', '_')
                filename = f"playlist_{safe_device_name.lower()}{file_extension}"
                file_path = os.path.join(export_dir, filename)

                # Write optimized content to file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(optimized_content)

                exported_files.append({
                    'device': device_name,
                    'file': filename,
                    'path': file_path,
                    'format': getattr(export_profile, 'format_info', 'M3U')
                })

            # Create summary file
            self.create_export_summary(export_dir, exported_files)

            # Show success message
            file_list = "\n".join([f"• {f['device']}: {f['file']}" for f in exported_files])
            QMessageBox.information(
                self,
                "Export Complete",
                f"Successfully exported {len(exported_files)} playlist files:\n\n{file_list}\n\nLocation: {export_dir}"
            )

            self.status_label.setText(f"✅ Exported {len(exported_files)} playlist files to {os.path.basename(export_dir)}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export playlist files:\n{str(e)}")
            self.status_label.setText("❌ Export failed")

    def download_playlist_content(self, url):
        """Download playlist content from URL"""
        try:
            import requests
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"Error downloading playlist: {e}")
            return None

    def get_file_extension_for_device(self, app_type):
        """Get appropriate file extension for device type"""
        extensions = {
            "tivimate": ".m3u8",
            "iptv_smarters": ".m3u",
            "vlc": ".m3u8",
            "kodi": ".m3u",
            "perfect_player": ".m3u8",
            "smart_tv": ".m3u",
            "android_tv": ".m3u8",
            "ios": ".m3u8",
            "web": ".m3u8",
            "generic": ".m3u"
        }
        return extensions.get(app_type, ".m3u")

    def create_export_summary(self, export_dir, exported_files):
        """Create a summary file with export information"""
        summary_path = os.path.join(export_dir, "EXPORT_SUMMARY.txt")

        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("📱 Multi-Device IPTV Playlist Export Summary\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Files: {len(exported_files)}\n\n")

            for file_info in exported_files:
                f.write(f"📱 {file_info['device']}\n")
                f.write(f"   File: {file_info['file']}\n")
                f.write(f"   Format: {file_info['format']}\n")
                f.write(f"   Path: {file_info['path']}\n\n")

            f.write("📖 Usage Instructions:\n")
            f.write("• Use the appropriate file for each device/app\n")
            f.write("• Files are optimized for maximum compatibility\n")
            f.write("• Import these files into your IPTV apps\n")

    def add_to_history(self, device_name, playlist_url):
        """Add export to history"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        history_item = f"{timestamp} - {device_name}: {playlist_url[:30]}..."

        self.export_history.append({
            'timestamp': timestamp,
            'device': device_name,
            'url': playlist_url
        })

        # Add to history list
        self.history_list.addItem(history_item)

        # Keep only last 10 items
        if self.history_list.count() > 10:
            self.history_list.takeItem(0)

    def clear_export_history(self):
        """Clear export history"""
        self.export_history.clear()
        self.history_list.clear()
        self.status_label.setText("🗑️ Export history cleared")
