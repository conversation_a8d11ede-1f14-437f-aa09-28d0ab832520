#!/usr/bin/env python3
"""
Language Selector Widget
Clean, simple language selection with automatic GUI updates
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QComboBox, QGroupBox, QCheckBox)
from PyQt6.QtCore import Qt, pyqtSignal, QSettings
from PyQt6.QtGui import QFont
from translations.languages import language_manager, _, set_language, get_available_languages
from styles import Colors

class LanguageSelectorWidget(QWidget):
    """Simplified widget for language selection"""

    language_changed = pyqtSignal(str)  # Emitted when language changes

    def __init__(self):
        super().__init__()
        self.settings = QSettings("MEGA_IPTV_TOOLS", "LanguageSettings")
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()

        # Title
        title = QLabel("🌍 Language Settings")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Language Selection
        self.setup_language_selection(layout)

        self.setLayout(layout)

    def setup_language_selection(self, layout):
        """Setup the language selection"""
        # Current Language section
        current_group = QGroupBox("🎯 Select Language")
        current_layout = QVBoxLayout()

        # Language selector
        selector_layout = QHBoxLayout()

        selector_layout.addWidget(QLabel("Language:"))

        self.language_combo = QComboBox()
        self.language_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: white;
                border: 2px solid {Colors.PRIMARY};
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                min-width: 250px;
                min-height: 30px;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: 2px solid {Colors.PRIMARY};
                width: 8px;
                height: 8px;
            }}
        """)

        # Populate language combo
        self.populate_language_combo()
        self.language_combo.currentTextChanged.connect(self.on_language_changed)

        selector_layout.addWidget(self.language_combo)
        selector_layout.addStretch()

        current_layout.addLayout(selector_layout)

        # Current language info
        self.current_lang_info = QLabel()
        self.current_lang_info.setStyleSheet(f"""
            QLabel {{
                color: {Colors.TEXT};
                font-size: 12px;
                padding: 10px;
                background-color: {Colors.SURFACE};
                border-radius: 4px;
                margin: 5px 0px;
            }}
        """)
        current_layout.addWidget(self.current_lang_info)

        current_group.setLayout(current_layout)
        layout.addWidget(current_group)

        # Language Options section
        options_group = QGroupBox("⚙️ Options")
        options_layout = QVBoxLayout()

        # Auto-apply (always enabled for simplicity)
        self.auto_apply_label = QLabel("✅ Language changes are applied automatically")
        self.auto_apply_label.setStyleSheet(f"color: {Colors.SUCCESS}; font-weight: bold; padding: 5px;")
        options_layout.addWidget(self.auto_apply_label)

        # Remember language choice
        self.remember_choice_check = QCheckBox("Remember language choice")
        self.remember_choice_check.setChecked(True)
        options_layout.addWidget(self.remember_choice_check)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Status
        self.status_label = QLabel("Ready")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {Colors.TEXT};
                font-size: 12px;
                padding: 8px;
                background-color: {Colors.SURFACE};
                border-radius: 4px;
                margin: 5px 0px;
                font-weight: bold;
            }}
        """)
        layout.addWidget(self.status_label)

    def populate_language_combo(self):
        """Populate the language selection combo box"""
        self.language_combo.clear()

        # Only show loaded languages and a few popular ones
        available_languages = get_available_languages()
        loaded_languages = language_manager.get_loaded_languages()

        # Always include these popular languages
        priority_languages = ["english", "spanish", "french", "german", "italian", "portuguese", "russian", "chinese"]

        # Combine loaded languages with priority languages
        languages_to_show = set(loaded_languages + priority_languages)

        for code in languages_to_show:
            if code in available_languages:
                name = available_languages[code]
                # Mark if language is loaded
                if code in loaded_languages:
                    self.language_combo.addItem(f"{name} ✓", code)
                else:
                    self.language_combo.addItem(f"{name}", code)

        # Set current language
        current_lang = language_manager.get_current_language()
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == current_lang:
                self.language_combo.setCurrentIndex(i)
                break

    def on_language_changed(self):
        """Handle language selection change - automatically apply"""
        selected_code = self.language_combo.currentData()
        if selected_code:
            lang_name = language_manager.get_language_name(selected_code)
            loaded = language_manager.is_language_loaded(selected_code)
            current_lang = language_manager.get_current_language()

            # Update info display
            if loaded:
                info_text = f"✅ {lang_name}\nLanguage is loaded and ready"
            else:
                info_text = f"🔄 {lang_name}\nLoading language..."

            self.current_lang_info.setText(info_text)

            # Automatically apply if different from current language
            if selected_code != current_lang:
                self.apply_language_automatically(selected_code)

    def apply_language_automatically(self, selected_code):
        """Apply language change automatically"""
        try:
            # Temporarily disconnect signal to prevent recursion
            self.language_combo.currentTextChanged.disconnect()

            # Load language if not loaded
            if not language_manager.is_language_loaded(selected_code):
                self.status_label.setText(f"Loading {language_manager.get_language_name(selected_code)}...")
                success = language_manager.load_language(selected_code)

                if not success:
                    # Try to create a basic translation file for popular languages
                    if selected_code in ["german", "italian", "portuguese", "russian", "chinese"]:
                        self.create_basic_translation(selected_code)
                        success = language_manager.load_language(selected_code)

                    if not success:
                        self.status_label.setText(f"Failed to load {selected_code}, using English")
                        selected_code = "english"

            # Set language
            success = set_language(selected_code)
            if success:
                lang_name = language_manager.get_language_name(selected_code)
                self.status_label.setText(f"✅ Language changed to {lang_name}")
                self.status_label.setStyleSheet(f"""
                    QLabel {{
                        color: {Colors.SUCCESS};
                        font-size: 12px;
                        padding: 8px;
                        background-color: {Colors.SURFACE};
                        border-radius: 4px;
                        margin: 5px 0px;
                        font-weight: bold;
                    }}
                """)

                # Update info display
                self.current_lang_info.setText(f"✅ {lang_name}\nLanguage is active")

                # Save settings
                if self.remember_choice_check.isChecked():
                    self.save_settings()

                # Emit signal for main application to update
                self.language_changed.emit(selected_code)

            else:
                self.status_label.setText(f"❌ Failed to set language: {selected_code}")
                self.status_label.setStyleSheet(f"""
                    QLabel {{
                        color: {Colors.ERROR};
                        font-size: 12px;
                        padding: 8px;
                        background-color: {Colors.SURFACE};
                        border-radius: 4px;
                        margin: 5px 0px;
                        font-weight: bold;
                    }}
                """)

        except Exception as e:
            self.status_label.setText(f"❌ Error: {str(e)}")
            print(f"Language change error: {e}")

        finally:
            # Reconnect signal
            self.language_combo.currentTextChanged.connect(self.on_language_changed)

    def create_basic_translation(self, language_code):
        """Create a basic translation file for popular languages"""
        try:
            # Get English translations as base
            english_translations = language_manager.translations.get("english", {})

            # For now, just copy English (user can edit later)
            language_manager.translations[language_code] = english_translations.copy()
            language_manager.save_language(language_code)

        except Exception as e:
            print(f"Failed to create basic translation for {language_code}: {e}")

    def save_settings(self):
        """Save language settings"""
        self.settings.setValue("current_language", language_manager.get_current_language())
        self.settings.setValue("remember_choice", self.remember_choice_check.isChecked())

    def load_settings(self):
        """Load language settings"""
        # Load saved language
        saved_language = self.settings.value("current_language", "english")
        if saved_language and language_manager.is_language_loaded(saved_language):
            set_language(saved_language)

            # Update combo box
            for i in range(self.language_combo.count()):
                if self.language_combo.itemData(i) == saved_language:
                    self.language_combo.setCurrentIndex(i)
                    break

        # Load other settings
        self.remember_choice_check.setChecked(self.settings.value("remember_choice", True, type=bool))

        # Update UI
        self.on_language_changed()
