"""
Download Integration GUI Components
Adds download functionality to existing player interfaces
"""

from PyQt6.QtWidgets import (QPushButton, QHBoxLayout, QVBoxLayout, QLabel, 
                            QMessageBox, QDialog, QDialogButtonBox, QComboBox,
                            QCheckBox, QSpinBox, QGroupBox, QGridLayout)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from download_integration import DownloadIntegration
from download_manager_gui import DownloadManagerWidget
from styles import Colors

import logging
logger = logging.getLogger(__name__)

class DownloadDialog(QDialog):
    """Dialog for configuring download options"""
    
    def __init__(self, content_data, server_info, server_type, parent=None):
        super().__init__(parent)
        self.content_data = content_data
        self.server_info = server_info
        self.server_type = server_type
        self.setWindowTitle("📥 Download Configuration")
        self.setModal(True)
        self.resize(400, 300)
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize the dialog UI"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("Configure Download Settings")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Content info
        info_group = QGroupBox("📋 Content Information")
        info_layout = QGridLayout()
        
        content_title = self.content_data.get('name', 'Unknown')
        info_layout.addWidget(QLabel("Title:"), 0, 0)
        info_layout.addWidget(QLabel(content_title), 0, 1)
        
        content_type = "Movie" if self.content_data.get('is_series', '0') == '0' else "Series"
        info_layout.addWidget(QLabel("Type:"), 1, 0)
        info_layout.addWidget(QLabel(content_type), 1, 1)
        
        if 'category_name' in self.content_data:
            info_layout.addWidget(QLabel("Category:"), 2, 0)
            info_layout.addWidget(QLabel(self.content_data['category_name']), 2, 1)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # Download options
        options_group = QGroupBox("⚙️ Download Options")
        options_layout = QGridLayout()
        
        # Quality selection
        options_layout.addWidget(QLabel("Quality:"), 0, 0)
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(['Original', 'High (1080p)', 'Medium (720p)', 'Low (480p)'])
        options_layout.addWidget(self.quality_combo, 0, 1)
        
        # Format selection
        options_layout.addWidget(QLabel("Format:"), 1, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems(['mp4', 'mkv', 'avi', 'original'])
        options_layout.addWidget(self.format_combo, 1, 1)
        
        # Auto-start download
        self.auto_start_check = QCheckBox("Start download immediately")
        self.auto_start_check.setChecked(True)
        options_layout.addWidget(self.auto_start_check, 2, 0, 1, 2)
        
        # Download subtitles (if available)
        self.subtitles_check = QCheckBox("Download subtitles (if available)")
        self.subtitles_check.setChecked(True)
        options_layout.addWidget(self.subtitles_check, 3, 0, 1, 2)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # Series-specific options
        if content_type == "Series":
            series_group = QGroupBox("📺 Series Options")
            series_layout = QGridLayout()
            
            self.download_season_check = QCheckBox("Download entire season")
            series_layout.addWidget(self.download_season_check, 0, 0, 1, 2)
            
            self.download_all_check = QCheckBox("Download all available episodes")
            series_layout.addWidget(self.download_all_check, 1, 0, 1, 2)
            
            series_layout.addWidget(QLabel("Max concurrent downloads:"), 2, 0)
            self.concurrent_spin = QSpinBox()
            self.concurrent_spin.setRange(1, 5)
            self.concurrent_spin.setValue(2)
            series_layout.addWidget(self.concurrent_spin, 2, 1)
            
            series_group.setLayout(series_layout)
            layout.addWidget(series_group)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def get_download_settings(self):
        """Get the configured download settings"""
        return {
            'quality': self.quality_combo.currentText(),
            'format': self.format_combo.currentText(),
            'auto_start': self.auto_start_check.isChecked(),
            'download_subtitles': self.subtitles_check.isChecked(),
            'download_season': getattr(self, 'download_season_check', None) and self.download_season_check.isChecked(),
            'download_all': getattr(self, 'download_all_check', None) and self.download_all_check.isChecked(),
            'max_concurrent': getattr(self, 'concurrent_spin', None) and self.concurrent_spin.value() or 2
        }

class DownloadButtonWidget:
    """Mixin class to add download functionality to player widgets"""
    
    def __init__(self):
        self.download_manager = None
        self.download_integration = None
    
    def setup_download_manager(self, download_manager: DownloadManagerWidget):
        """Setup the download manager"""
        self.download_manager = download_manager
        self.download_integration = DownloadIntegration(download_manager)
    
    def create_download_button(self, parent_layout=None) -> QPushButton:
        """Create a download button with proper styling"""
        download_btn = QPushButton("📥 Download")
        download_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}DD;
            }}
            QPushButton:pressed {{
                background-color: {Colors.PRIMARY}BB;
            }}
            QPushButton:disabled {{
                background-color: #CCCCCC;
                color: #666666;
            }}
        """)
        
        if parent_layout:
            parent_layout.addWidget(download_btn)
        
        return download_btn
    
    def create_batch_download_button(self, parent_layout=None) -> QPushButton:
        """Create a batch download button for series"""
        batch_btn = QPushButton("📦 Batch Download")
        batch_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SUCCESS};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {Colors.SUCCESS}DD;
            }}
            QPushButton:pressed {{
                background-color: {Colors.SUCCESS}BB;
            }}
            QPushButton:disabled {{
                background-color: #CCCCCC;
                color: #666666;
            }}
        """)
        
        if parent_layout:
            parent_layout.addWidget(batch_btn)
        
        return batch_btn
    
    def handle_download_request(self, content_data, server_info, server_type):
        """Handle download request from player"""
        if not self.download_manager or not self.download_integration:
            QMessageBox.warning(
                self, "Download Manager", 
                "Download manager is not available. Please enable it in settings."
            )
            return
        
        try:
            # Show download configuration dialog
            dialog = DownloadDialog(content_data, server_info, server_type, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                settings = dialog.get_download_settings()
                
                # Add download to manager
                success = self.download_integration.add_download_from_player(
                    content_data, server_info, server_type
                )
                
                if success:
                    QMessageBox.information(
                        self, "Download Added", 
                        f"'{content_data.get('name', 'Unknown')}' has been added to the download queue."
                    )
                else:
                    QMessageBox.warning(
                        self, "Download Failed", 
                        "Failed to add download. Please check the content and try again."
                    )
        
        except Exception as e:
            logger.error(f"Error handling download request: {e}")
            QMessageBox.critical(
                self, "Download Error", 
                f"An error occurred while adding the download: {str(e)}"
            )
    
    def handle_batch_download_request(self, series_data, episodes_list, server_info, server_type):
        """Handle batch download request for series"""
        if not self.download_manager or not self.download_integration:
            QMessageBox.warning(
                self, "Download Manager", 
                "Download manager is not available. Please enable it in settings."
            )
            return
        
        try:
            # Show confirmation dialog
            reply = QMessageBox.question(
                self, "Batch Download", 
                f"Do you want to download all {len(episodes_list)} episodes of "
                f"'{series_data.get('name', 'Unknown')}'?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # Add batch downloads
                added_count = self.download_integration.add_batch_downloads_from_series(
                    series_data, episodes_list, server_info, server_type
                )
                
                if added_count > 0:
                    QMessageBox.information(
                        self, "Batch Download Added", 
                        f"{added_count} episodes have been added to the download queue."
                    )
                else:
                    QMessageBox.warning(
                        self, "Batch Download Failed", 
                        "Failed to add episodes to download queue."
                    )
        
        except Exception as e:
            logger.error(f"Error handling batch download request: {e}")
            QMessageBox.critical(
                self, "Batch Download Error", 
                f"An error occurred while adding batch downloads: {str(e)}"
            )
    
    def add_download_buttons_to_layout(self, layout, content_data, server_info, server_type, is_series=False):
        """Add download buttons to a layout"""
        download_layout = QHBoxLayout()
        
        # Single download button
        download_btn = self.create_download_button()
        download_btn.clicked.connect(
            lambda: self.handle_download_request(content_data, server_info, server_type)
        )
        download_layout.addWidget(download_btn)
        
        # Batch download button for series
        if is_series:
            batch_btn = self.create_batch_download_button()
            batch_btn.clicked.connect(
                lambda: self.handle_batch_download_request(
                    content_data, [], server_info, server_type  # episodes_list would be populated
                )
            )
            download_layout.addWidget(batch_btn)
        
        download_layout.addStretch()
        layout.addLayout(download_layout)
        
        return download_btn, batch_btn if is_series else None
