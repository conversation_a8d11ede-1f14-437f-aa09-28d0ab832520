#!/usr/bin/env python3
"""
Intelligent Proxy Manager for Download System
Automatically tests, validates, and manages proxies for optimal download performance
"""

import asyncio
import aiohttp
import requests
import time
import json
import threading
import os
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from urllib.parse import urlparse
import logging

logger = logging.getLogger(__name__)

@dataclass
class ProxyInfo:
    """Information about a proxy server"""
    proxy: str  # Format: "ip:port" or "protocol://ip:port"
    protocol: str  # http, https, socks4, socks5
    response_time: float = 0.0  # milliseconds
    success_rate: float = 0.0  # 0-100%
    last_tested: Optional[datetime] = None
    total_tests: int = 0
    successful_tests: int = 0
    country: str = ""
    anonymity: str = ""  # transparent, anonymous, elite
    is_working: bool = False
    avg_speed: float = 0.0  # KB/s
    reliability_score: float = 0.0  # 0-100%
    
    def update_test_result(self, success: bool, response_time: float = 0.0, speed: float = 0.0):
        """Update proxy test results"""
        self.total_tests += 1
        if success:
            self.successful_tests += 1
            self.response_time = response_time
            if speed > 0:
                # Update average speed (weighted average)
                if self.avg_speed == 0:
                    self.avg_speed = speed
                else:
                    self.avg_speed = (self.avg_speed * 0.7) + (speed * 0.3)
        
        self.success_rate = (self.successful_tests / self.total_tests) * 100
        self.is_working = success and response_time < 10000  # Less than 10 seconds
        self.last_tested = datetime.now()
        
        # Calculate reliability score (combines success rate, speed, and response time)
        speed_score = min(self.avg_speed / 100, 100)  # Normalize speed (100 KB/s = 100 points)
        time_score = max(0, 100 - (self.response_time / 100))  # Lower response time = higher score
        self.reliability_score = (self.success_rate * 0.5) + (speed_score * 0.3) + (time_score * 0.2)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        if self.last_tested:
            data['last_tested'] = self.last_tested.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ProxyInfo':
        """Create from dictionary (JSON deserialization)"""
        if data.get('last_tested'):
            data['last_tested'] = datetime.fromisoformat(data['last_tested'])
        return cls(**data)

class ProxyTester:
    """Tests proxy servers for functionality and performance"""
    
    def __init__(self):
        self.test_urls = [
            "http://httpbin.org/ip",
            "https://httpbin.org/ip", 
            "http://icanhazip.com",
            "https://api.ipify.org?format=json"
        ]
        self.timeout = 15  # seconds
    
    async def test_proxy_async(self, proxy_info: ProxyInfo) -> bool:
        """Test a proxy asynchronously"""
        try:
            proxy_url = self.format_proxy_url(proxy_info.proxy, proxy_info.protocol)
            
            start_time = time.time()
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout),
                connector=aiohttp.TCPConnector(ssl=False)
            ) as session:
                
                # Test with a simple HTTP request
                test_url = self.test_urls[0]  # Use httpbin for testing
                
                async with session.get(
                    test_url,
                    proxy=proxy_url,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as response:
                    
                    if response.status == 200:
                        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                        
                        # Test download speed with a small file
                        speed = await self.test_download_speed(session, proxy_url)
                        
                        proxy_info.update_test_result(True, response_time, speed)
                        logger.info(f"✅ Proxy {proxy_info.proxy} - OK ({response_time:.0f}ms, {speed:.1f} KB/s)")
                        return True
                    else:
                        proxy_info.update_test_result(False)
                        logger.warning(f"❌ Proxy {proxy_info.proxy} - HTTP {response.status}")
                        return False
                        
        except Exception as e:
            proxy_info.update_test_result(False)
            logger.warning(f"❌ Proxy {proxy_info.proxy} - Error: {str(e)[:50]}")
            return False
    
    async def test_download_speed(self, session: aiohttp.ClientSession, proxy_url: str) -> float:
        """Test download speed through proxy"""
        try:
            # Use a small test file (100KB)
            test_file_url = "http://speedtest.ftp.otenet.gr/files/test100k.db"
            
            start_time = time.time()
            async with session.get(test_file_url, proxy=proxy_url) as response:
                if response.status == 200:
                    data = await response.read()
                    elapsed = time.time() - start_time
                    if elapsed > 0:
                        speed_kbps = (len(data) / 1024) / elapsed  # KB/s
                        return speed_kbps
            return 0.0
        except:
            return 0.0
    
    def test_proxy_sync(self, proxy_info: ProxyInfo) -> bool:
        """Test a proxy synchronously (for compatibility)"""
        try:
            proxy_dict = self.get_proxy_dict(proxy_info.proxy, proxy_info.protocol)
            
            start_time = time.time()
            
            response = requests.get(
                self.test_urls[0],
                proxies=proxy_dict,
                timeout=self.timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            if response.status_code == 200:
                response_time = (time.time() - start_time) * 1000
                proxy_info.update_test_result(True, response_time)
                return True
            else:
                proxy_info.update_test_result(False)
                return False
                
        except Exception as e:
            proxy_info.update_test_result(False)
            return False
    
    def format_proxy_url(self, proxy: str, protocol: str) -> str:
        """Format proxy for aiohttp"""
        if "://" in proxy:
            return proxy
        return f"{protocol}://{proxy}"
    
    def get_proxy_dict(self, proxy: str, protocol: str) -> Dict[str, str]:
        """Get proxy dictionary for requests library"""
        proxy_url = self.format_proxy_url(proxy, protocol)
        return {
            'http': proxy_url,
            'https': proxy_url
        }

class ProxyManager:
    """Manages a pool of proxies with automatic testing and filtering"""
    
    def __init__(self, cache_file: str = "proxy_cache.json"):
        self.cache_file = cache_file
        self.proxies: List[ProxyInfo] = []
        self.tester = ProxyTester()
        self.min_reliability_score = 30.0  # Minimum score to consider proxy "good"
        self.test_interval = 300  # Test proxies every 5 minutes
        self.last_test_time = 0
        self.load_cache()
    
    def add_proxy(self, proxy: str, protocol: str = "http") -> ProxyInfo:
        """Add a new proxy to the pool"""
        # Check if proxy already exists
        for existing in self.proxies:
            if existing.proxy == proxy and existing.protocol == protocol:
                return existing
        
        proxy_info = ProxyInfo(proxy=proxy, protocol=protocol)
        self.proxies.append(proxy_info)
        return proxy_info
    
    def add_proxy_list(self, proxy_list: List[str], protocol: str = "http"):
        """Add multiple proxies from a list"""
        for proxy in proxy_list:
            if proxy.strip():
                # Auto-detect protocol if included
                if "://" in proxy:
                    parsed = urlparse(proxy)
                    self.add_proxy(f"{parsed.hostname}:{parsed.port}", parsed.scheme)
                else:
                    self.add_proxy(proxy.strip(), protocol)
    
    async def test_all_proxies(self) -> Dict[str, int]:
        """Test all proxies asynchronously"""
        logger.info(f"🧪 Testing {len(self.proxies)} proxies...")
        
        # Create tasks for all proxy tests
        tasks = []
        for proxy_info in self.proxies:
            task = asyncio.create_task(self.tester.test_proxy_async(proxy_info))
            tasks.append(task)
        
        # Run all tests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count results
        working = sum(1 for r in results if r is True)
        failed = len(results) - working
        
        # Sort proxies by reliability score
        self.proxies.sort(key=lambda p: p.reliability_score, reverse=True)
        
        # Save results
        self.save_cache()
        self.last_test_time = time.time()
        
        logger.info(f"✅ Proxy testing complete: {working} working, {failed} failed")
        
        return {
            'total': len(self.proxies),
            'working': working,
            'failed': failed,
            'good': len(self.get_good_proxies())
        }
    
    def test_all_proxies_sync(self) -> Dict[str, int]:
        """Test all proxies synchronously (fallback)"""
        logger.info(f"🧪 Testing {len(self.proxies)} proxies (sync mode)...")
        
        working = 0
        for proxy_info in self.proxies:
            if self.tester.test_proxy_sync(proxy_info):
                working += 1
        
        failed = len(self.proxies) - working
        self.proxies.sort(key=lambda p: p.reliability_score, reverse=True)
        self.save_cache()
        self.last_test_time = time.time()
        
        return {
            'total': len(self.proxies),
            'working': working,
            'failed': failed,
            'good': len(self.get_good_proxies())
        }
    
    def get_good_proxies(self) -> List[ProxyInfo]:
        """Get list of reliable proxies"""
        return [p for p in self.proxies if p.is_working and p.reliability_score >= self.min_reliability_score]
    
    def get_best_proxy(self) -> Optional[ProxyInfo]:
        """Get the best performing proxy"""
        good_proxies = self.get_good_proxies()
        return good_proxies[0] if good_proxies else None
    
    def get_proxy_for_download(self) -> Optional[Dict[str, str]]:
        """Get a proxy formatted for requests library"""
        best_proxy = self.get_best_proxy()
        if best_proxy:
            return self.tester.get_proxy_dict(best_proxy.proxy, best_proxy.protocol)
        return None
    
    def remove_bad_proxies(self) -> int:
        """Remove proxies with poor performance"""
        initial_count = len(self.proxies)
        
        # Remove proxies that have been tested but are not working
        self.proxies = [
            p for p in self.proxies 
            if p.total_tests == 0 or (p.is_working and p.reliability_score >= 10.0)
        ]
        
        removed = initial_count - len(self.proxies)
        if removed > 0:
            logger.info(f"🗑️ Removed {removed} bad proxies")
            self.save_cache()
        
        return removed
    
    def should_test_proxies(self) -> bool:
        """Check if proxies should be tested again"""
        return (time.time() - self.last_test_time) > self.test_interval
    
    def save_cache(self):
        """Save proxy information to cache file"""
        try:
            cache_data = {
                'proxies': [p.to_dict() for p in self.proxies],
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving proxy cache: {e}")
    
    def load_cache(self):
        """Load proxy information from cache file"""
        try:
            if not os.path.exists(self.cache_file):
                return
            
            with open(self.cache_file, 'r') as f:
                cache_data = json.load(f)
            
            self.proxies = [ProxyInfo.from_dict(p) for p in cache_data.get('proxies', [])]
            logger.info(f"📂 Loaded {len(self.proxies)} proxies from cache")
            
        except Exception as e:
            logger.error(f"Error loading proxy cache: {e}")
            self.proxies = []
    
    def get_statistics(self) -> Dict:
        """Get proxy pool statistics"""
        if not self.proxies:
            return {'total': 0, 'working': 0, 'good': 0, 'avg_score': 0}
        
        working = [p for p in self.proxies if p.is_working]
        good = self.get_good_proxies()
        avg_score = sum(p.reliability_score for p in self.proxies) / len(self.proxies)
        
        return {
            'total': len(self.proxies),
            'working': len(working),
            'good': len(good),
            'avg_score': avg_score,
            'best_score': max(p.reliability_score for p in self.proxies) if self.proxies else 0,
            'last_tested': self.last_test_time
        }

class ProxyFetcher:
    """Fetches free proxies from various online sources"""

    def __init__(self):
        self.sources = [
            {
                'name': 'Free Proxy List',
                'url': 'https://www.proxy-list.download/api/v1/get?type=http',
                'parser': self.parse_simple_list
            },
            {
                'name': 'ProxyScrape',
                'url': 'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all',
                'parser': self.parse_simple_list
            },
            {
                'name': 'GitHub Proxy List',
                'url': 'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
                'parser': self.parse_simple_list
            }
        ]

    def fetch_proxies(self) -> List[str]:
        """Fetch proxies from all sources"""
        all_proxies = []

        for source in self.sources:
            try:
                logger.info(f"🌐 Fetching proxies from {source['name']}...")
                proxies = self.fetch_from_source(source)
                all_proxies.extend(proxies)
                logger.info(f"✅ Got {len(proxies)} proxies from {source['name']}")

            except Exception as e:
                logger.warning(f"❌ Failed to fetch from {source['name']}: {e}")

        # Remove duplicates
        unique_proxies = list(set(all_proxies))
        logger.info(f"📊 Total unique proxies fetched: {len(unique_proxies)}")

        return unique_proxies

    def fetch_from_source(self, source: Dict) -> List[str]:
        """Fetch proxies from a single source"""
        try:
            response = requests.get(
                source['url'],
                timeout=30,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )

            if response.status_code == 200:
                return source['parser'](response.text)
            else:
                logger.warning(f"HTTP {response.status_code} from {source['name']}")
                return []

        except Exception as e:
            logger.error(f"Error fetching from {source['name']}: {e}")
            return []

    def parse_simple_list(self, content: str) -> List[str]:
        """Parse simple proxy list (one proxy per line)"""
        proxies = []
        for line in content.strip().split('\n'):
            line = line.strip()
            if line and ':' in line and not line.startswith('#'):
                # Basic validation
                parts = line.split(':')
                if len(parts) == 2:
                    try:
                        ip, port = parts[0].strip(), parts[1].strip()
                        # Basic IP validation
                        ip_parts = ip.split('.')
                        if len(ip_parts) == 4 and all(0 <= int(part) <= 255 for part in ip_parts):
                            if 1 <= int(port) <= 65535:
                                proxies.append(f"{ip}:{port}")
                    except ValueError:
                        continue
        return proxies

class AutoProxyManager:
    """Automatically manages proxy pool with fetching, testing, and optimization"""

    def __init__(self, cache_file: str = "auto_proxy_cache.json"):
        self.proxy_manager = ProxyManager(cache_file)
        self.proxy_fetcher = ProxyFetcher()
        self.auto_refresh_enabled = True
        self.refresh_interval = 3600  # Refresh every hour
        self.last_refresh = 0
        self.min_good_proxies = 5  # Minimum number of good proxies to maintain

    async def initialize(self) -> Dict[str, int]:
        """Initialize proxy pool with fresh proxies"""
        logger.info("🚀 Initializing Auto Proxy Manager...")

        # Load existing proxies
        existing_count = len(self.proxy_manager.proxies)
        logger.info(f"📂 Loaded {existing_count} cached proxies")

        # Fetch new proxies if needed
        good_proxies = len(self.proxy_manager.get_good_proxies())
        if good_proxies < self.min_good_proxies:
            logger.info(f"🔄 Need more proxies (have {good_proxies}, need {self.min_good_proxies})")
            await self.refresh_proxy_pool()

        # Test all proxies
        results = await self.proxy_manager.test_all_proxies()

        # Clean up bad proxies
        removed = self.proxy_manager.remove_bad_proxies()

        logger.info("✅ Auto Proxy Manager initialized")
        return results

    async def refresh_proxy_pool(self):
        """Refresh proxy pool with new proxies"""
        logger.info("🔄 Refreshing proxy pool...")

        # Fetch new proxies
        new_proxies = self.proxy_fetcher.fetch_proxies()

        # Add to manager
        self.proxy_manager.add_proxy_list(new_proxies, "http")

        self.last_refresh = time.time()
        logger.info(f"✅ Added {len(new_proxies)} new proxies to pool")

    async def get_best_proxy_for_download(self) -> Optional[Dict[str, str]]:
        """Get the best proxy for downloading, with automatic refresh if needed"""

        # Check if we need to refresh
        if self.should_refresh():
            await self.refresh_proxy_pool()
            await self.proxy_manager.test_all_proxies()

        # Get best proxy
        return self.proxy_manager.get_proxy_for_download()

    def should_refresh(self) -> bool:
        """Check if proxy pool should be refreshed"""
        if not self.auto_refresh_enabled:
            return False

        # Time-based refresh
        if (time.time() - self.last_refresh) > self.refresh_interval:
            return True

        # Quality-based refresh
        good_proxies = len(self.proxy_manager.get_good_proxies())
        if good_proxies < self.min_good_proxies:
            return True

        return False

    def get_proxy_statistics(self) -> Dict:
        """Get comprehensive proxy statistics"""
        stats = self.proxy_manager.get_statistics()
        stats.update({
            'auto_refresh_enabled': self.auto_refresh_enabled,
            'last_refresh': self.last_refresh,
            'next_refresh': self.last_refresh + self.refresh_interval if self.auto_refresh_enabled else None,
            'min_good_proxies': self.min_good_proxies
        })
        return stats
