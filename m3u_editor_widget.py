"""
M3U Editor Widget
Professional M3U file editor for channels, VOD, and series
"""

import os
import re
import json
from datetime import datetime
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTextEdit, QProgressBar,
                            QGroupBox, QGridLayout, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QFileDialog, QComboBox,
                            QCheckBox, QSpinBox, QTabWidget, QSplitter,
                            QFrame, QScrollArea, QDialog, QDialogButtonBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QPixmap
import requests

class M3UEntry:
    """Represents a single M3U entry (channel/VOD/series)"""
    
    def __init__(self, extinf_line="", url=""):
        self.extinf_line = extinf_line
        self.url = url
        self.parse_extinf()
    
    def parse_extinf(self):
        """Parse EXTINF line to extract metadata"""
        self.tvg_id = ""
        self.tvg_name = ""
        self.tvg_logo = ""
        self.group_title = ""
        self.title = ""
        self.duration = "-1"
        self.tvg_type = ""
        self.tvg_serie = ""
        self.tvg_season = ""
        self.tvg_episode = ""
        
        if not self.extinf_line.startswith("#EXTINF:"):
            return
        
        # Extract duration
        duration_match = re.search(r'#EXTINF:([^,]*),', self.extinf_line)
        if duration_match:
            self.duration = duration_match.group(1).strip()
        
        # Extract title (after last comma)
        title_match = re.search(r',([^,]*)$', self.extinf_line)
        if title_match:
            self.title = title_match.group(1).strip()
        
        # Extract attributes
        self.tvg_id = self._extract_attribute('tvg-id')
        self.tvg_name = self._extract_attribute('tvg-name')
        self.tvg_logo = self._extract_attribute('tvg-logo')
        self.group_title = self._extract_attribute('group-title')
        self.tvg_type = self._extract_attribute('tvg-type')
        self.tvg_serie = self._extract_attribute('tvg-serie')
        self.tvg_season = self._extract_attribute('tvg-season')
        self.tvg_episode = self._extract_attribute('tvg-episode')
    
    def _extract_attribute(self, attr_name):
        """Extract attribute value from EXTINF line"""
        pattern = rf'{attr_name}="([^"]*)"'
        match = re.search(pattern, self.extinf_line)
        return match.group(1) if match else ""
    
    def build_extinf(self):
        """Build EXTINF line from current metadata"""
        attributes = []
        
        if self.tvg_id:
            attributes.append(f'tvg-id="{self.tvg_id}"')
        if self.tvg_name:
            attributes.append(f'tvg-name="{self.tvg_name}"')
        if self.tvg_logo:
            attributes.append(f'tvg-logo="{self.tvg_logo}"')
        if self.group_title:
            attributes.append(f'group-title="{self.group_title}"')
        if self.tvg_type:
            attributes.append(f'tvg-type="{self.tvg_type}"')
        if self.tvg_serie:
            attributes.append(f'tvg-serie="{self.tvg_serie}"')
        if self.tvg_season:
            attributes.append(f'tvg-season="{self.tvg_season}"')
        if self.tvg_episode:
            attributes.append(f'tvg-episode="{self.tvg_episode}"')
        
        attr_string = " ".join(attributes)
        if attr_string:
            attr_string = " " + attr_string
        
        return f"#EXTINF:{self.duration}{attr_string},{self.title}"
    
    def get_type(self):
        """Determine entry type based on metadata"""
        if self.tvg_type == "serie" or self.tvg_serie:
            return "Series"
        elif "VOD" in self.group_title or "Movie" in self.group_title:
            return "VOD"
        elif "Live" in self.group_title or not self.group_title:
            return "Channel"
        else:
            return "Other"

class M3ULoader(QThread):
    """Thread for loading M3U files"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    entries_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path
    
    def run(self):
        try:
            self.status_updated.emit("📂 Loading M3U file...")
            self.progress_updated.emit(10)
            
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.status_updated.emit("📋 Parsing entries...")
            self.progress_updated.emit(30)
            
            entries = []
            current_extinf = ""
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith("#EXTINF:"):
                    current_extinf = line
                elif line and not line.startswith("#") and current_extinf:
                    # This is a URL line
                    entry = M3UEntry(current_extinf, line)
                    entries.append(entry)
                    current_extinf = ""
                
                # Update progress
                progress = 30 + (i / len(lines)) * 60
                self.progress_updated.emit(int(progress))
            
            self.status_updated.emit(f"✅ Loaded {len(entries)} entries")
            self.progress_updated.emit(100)
            self.entries_loaded.emit(entries)
            
        except Exception as e:
            self.error_occurred.emit(f"Failed to load M3U file: {str(e)}")

class EntryEditDialog(QDialog):
    """Dialog for editing individual M3U entries"""
    
    def __init__(self, entry, parent=None):
        super().__init__(parent)
        self.entry = entry
        self.setWindowTitle("Edit M3U Entry")
        self.setModal(True)
        self.resize(600, 500)
        self.init_ui()
        self.load_entry_data()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Basic info
        basic_group = QGroupBox("📝 Basic Information")
        basic_layout = QGridLayout()
        
        basic_layout.addWidget(QLabel("Title:"), 0, 0)
        self.title_input = QLineEdit()
        basic_layout.addWidget(self.title_input, 0, 1, 1, 2)
        
        basic_layout.addWidget(QLabel("URL:"), 1, 0)
        self.url_input = QLineEdit()
        basic_layout.addWidget(self.url_input, 1, 1, 1, 2)
        
        basic_layout.addWidget(QLabel("Group:"), 2, 0)
        self.group_input = QLineEdit()
        basic_layout.addWidget(self.group_input, 2, 1, 1, 2)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # TVG attributes
        tvg_group = QGroupBox("📺 TVG Attributes")
        tvg_layout = QGridLayout()
        
        tvg_layout.addWidget(QLabel("TVG ID:"), 0, 0)
        self.tvg_id_input = QLineEdit()
        tvg_layout.addWidget(self.tvg_id_input, 0, 1)
        
        tvg_layout.addWidget(QLabel("TVG Name:"), 0, 2)
        self.tvg_name_input = QLineEdit()
        tvg_layout.addWidget(self.tvg_name_input, 0, 3)
        
        tvg_layout.addWidget(QLabel("Logo URL:"), 1, 0)
        self.logo_input = QLineEdit()
        tvg_layout.addWidget(self.logo_input, 1, 1, 1, 2)
        
        self.test_logo_button = QPushButton("🖼️ Test Logo")
        self.test_logo_button.clicked.connect(self.test_logo)
        tvg_layout.addWidget(self.test_logo_button, 1, 3)
        
        tvg_group.setLayout(tvg_layout)
        layout.addWidget(tvg_group)
        
        # Series attributes (if applicable)
        series_group = QGroupBox("📺 Series Attributes")
        series_layout = QGridLayout()
        
        series_layout.addWidget(QLabel("Type:"), 0, 0)
        self.type_combo = QComboBox()
        self.type_combo.addItems(["", "serie"])
        series_layout.addWidget(self.type_combo, 0, 1)
        
        series_layout.addWidget(QLabel("Serie ID:"), 0, 2)
        self.serie_input = QLineEdit()
        series_layout.addWidget(self.serie_input, 0, 3)
        
        series_layout.addWidget(QLabel("Season:"), 1, 0)
        self.season_input = QLineEdit()
        series_layout.addWidget(self.season_input, 1, 1)
        
        series_layout.addWidget(QLabel("Episode:"), 1, 2)
        self.episode_input = QLineEdit()
        series_layout.addWidget(self.episode_input, 1, 3)
        
        series_group.setLayout(series_layout)
        layout.addWidget(series_group)
        
        # Logo preview
        self.logo_label = QLabel("Logo preview will appear here")
        self.logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.logo_label.setMinimumHeight(100)
        self.logo_label.setStyleSheet("border: 1px solid #ccc; background-color: #f9f9f9;")
        layout.addWidget(self.logo_label)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def load_entry_data(self):
        """Load entry data into form fields"""
        self.title_input.setText(self.entry.title)
        self.url_input.setText(self.entry.url)
        self.group_input.setText(self.entry.group_title)
        self.tvg_id_input.setText(self.entry.tvg_id)
        self.tvg_name_input.setText(self.entry.tvg_name)
        self.logo_input.setText(self.entry.tvg_logo)
        self.type_combo.setCurrentText(self.entry.tvg_type)
        self.serie_input.setText(self.entry.tvg_serie)
        self.season_input.setText(self.entry.tvg_season)
        self.episode_input.setText(self.entry.tvg_episode)
        
        # Load logo if available
        if self.entry.tvg_logo:
            self.load_logo_preview(self.entry.tvg_logo)
    
    def test_logo(self):
        """Test logo URL and show preview"""
        logo_url = self.logo_input.text().strip()
        if logo_url:
            self.load_logo_preview(logo_url)
        else:
            QMessageBox.warning(self, "Warning", "Please enter a logo URL first")
    
    def load_logo_preview(self, url):
        """Load logo preview from URL"""
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                pixmap = QPixmap()
                pixmap.loadFromData(response.content)
                if not pixmap.isNull():
                    # Scale to fit preview area
                    scaled_pixmap = pixmap.scaled(150, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    self.logo_label.setPixmap(scaled_pixmap)
                    self.logo_label.setText("")
                else:
                    self.logo_label.setText("❌ Invalid image format")
            else:
                self.logo_label.setText(f"❌ HTTP {response.status_code}")
        except Exception as e:
            self.logo_label.setText(f"❌ Error: {str(e)}")
    
    def get_updated_entry(self):
        """Get updated entry with form data"""
        self.entry.title = self.title_input.text().strip()
        self.entry.url = self.url_input.text().strip()
        self.entry.group_title = self.group_input.text().strip()
        self.entry.tvg_id = self.tvg_id_input.text().strip()
        self.entry.tvg_name = self.tvg_name_input.text().strip()
        self.entry.tvg_logo = self.logo_input.text().strip()
        self.entry.tvg_type = self.type_combo.currentText()
        self.entry.tvg_serie = self.serie_input.text().strip()
        self.entry.tvg_season = self.season_input.text().strip()
        self.entry.tvg_episode = self.episode_input.text().strip()
        
        # Rebuild EXTINF line
        self.entry.extinf_line = self.entry.build_extinf()
        
        return self.entry

class M3UEditorWidget(QWidget):
    """Main M3U Editor Widget"""

    def __init__(self):
        super().__init__()
        self.entries = []
        self.filtered_entries = []
        self.current_file_path = ""
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()

        # File operations
        file_group = QGroupBox("📁 File Operations")
        file_layout = QHBoxLayout()

        self.load_button = QPushButton("📂 Load M3U File")
        self.load_button.clicked.connect(self.load_m3u_file)
        file_layout.addWidget(self.load_button)

        self.save_button = QPushButton("💾 Save File")
        self.save_button.clicked.connect(self.save_m3u_file)
        self.save_button.setEnabled(False)
        file_layout.addWidget(self.save_button)

        self.save_as_button = QPushButton("💾 Save As...")
        self.save_as_button.clicked.connect(self.save_as_m3u_file)
        self.save_as_button.setEnabled(False)
        file_layout.addWidget(self.save_as_button)

        self.new_button = QPushButton("📄 New File")
        self.new_button.clicked.connect(self.new_m3u_file)
        file_layout.addWidget(self.new_button)

        file_layout.addStretch()

        # File info
        self.file_info_label = QLabel("No file loaded")
        self.file_info_label.setStyleSheet("color: #666; font-style: italic;")
        file_layout.addWidget(self.file_info_label)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # Filter and search
        filter_group = QGroupBox("🔍 Filter & Search")
        filter_layout = QHBoxLayout()

        filter_layout.addWidget(QLabel("Type:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["All", "Channels", "VOD", "Series", "Other"])
        self.type_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.type_filter)

        filter_layout.addWidget(QLabel("Search:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search by title, group, or URL...")
        self.search_input.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.search_input)

        self.clear_search_button = QPushButton("🗑️ Clear")
        self.clear_search_button.clicked.connect(self.clear_search)
        filter_layout.addWidget(self.clear_search_button)

        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # Main content area
        content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Entries table
        table_widget = QWidget()
        table_layout = QVBoxLayout()

        # Table controls
        table_controls = QHBoxLayout()

        self.add_entry_button = QPushButton("➕ Add Entry")
        self.add_entry_button.clicked.connect(self.add_entry)
        self.add_entry_button.setEnabled(False)
        table_controls.addWidget(self.add_entry_button)

        self.edit_entry_button = QPushButton("✏️ Edit Entry")
        self.edit_entry_button.clicked.connect(self.edit_entry)
        self.edit_entry_button.setEnabled(False)
        table_controls.addWidget(self.edit_entry_button)

        self.delete_entry_button = QPushButton("🗑️ Delete Entry")
        self.delete_entry_button.clicked.connect(self.delete_entry)
        self.delete_entry_button.setEnabled(False)
        table_controls.addWidget(self.delete_entry_button)

        self.duplicate_entry_button = QPushButton("📋 Duplicate")
        self.duplicate_entry_button.clicked.connect(self.duplicate_entry)
        self.duplicate_entry_button.setEnabled(False)
        table_controls.addWidget(self.duplicate_entry_button)

        table_controls.addStretch()

        # Entry count
        self.entry_count_label = QLabel("0 entries")
        table_controls.addWidget(self.entry_count_label)

        table_layout.addLayout(table_controls)

        # Entries table
        self.entries_table = QTableWidget()
        self.entries_table.setColumnCount(7)
        self.entries_table.setHorizontalHeaderLabels([
            "Type", "Title", "Group", "TVG ID", "Logo", "URL", "Metadata"
        ])

        # Set column widths
        header = self.entries_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Type
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)          # Title
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Group
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # TVG ID
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Logo
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)          # URL
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Metadata

        self.entries_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.entries_table.doubleClicked.connect(self.edit_entry)

        table_layout.addWidget(self.entries_table)
        table_widget.setLayout(table_layout)
        content_splitter.addWidget(table_widget)

        # Preview panel
        preview_widget = QWidget()
        preview_layout = QVBoxLayout()

        preview_layout.addWidget(QLabel("📋 Entry Preview"))

        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(200)
        self.preview_text.setPlaceholderText("Select an entry to see its M3U format...")
        preview_layout.addWidget(self.preview_text)

        # Statistics
        stats_group = QGroupBox("📊 Statistics")
        stats_layout = QGridLayout()

        self.stats_total_label = QLabel("Total: 0")
        stats_layout.addWidget(self.stats_total_label, 0, 0)

        self.stats_channels_label = QLabel("Channels: 0")
        stats_layout.addWidget(self.stats_channels_label, 0, 1)

        self.stats_vod_label = QLabel("VOD: 0")
        stats_layout.addWidget(self.stats_vod_label, 1, 0)

        self.stats_series_label = QLabel("Series: 0")
        stats_layout.addWidget(self.stats_series_label, 1, 1)

        stats_group.setLayout(stats_layout)
        preview_layout.addWidget(stats_group)

        preview_layout.addStretch()
        preview_widget.setLayout(preview_layout)
        content_splitter.addWidget(preview_widget)

        # Set splitter proportions
        content_splitter.setSizes([700, 300])
        layout.addWidget(content_splitter)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Status bar
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def load_m3u_file(self):
        """Load M3U file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load M3U File",
            "",
            "M3U Files (*.m3u *.m3u8);;All Files (*)"
        )

        if file_path:
            self.current_file_path = file_path
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # Start loading thread
            self.loader_thread = M3ULoader(file_path)
            self.loader_thread.progress_updated.connect(self.progress_bar.setValue)
            self.loader_thread.status_updated.connect(self.status_label.setText)
            self.loader_thread.entries_loaded.connect(self.on_entries_loaded)
            self.loader_thread.error_occurred.connect(self.on_load_error)
            self.loader_thread.start()

    def on_entries_loaded(self, entries):
        """Handle loaded entries"""
        self.entries = entries
        self.filtered_entries = entries.copy()
        self.progress_bar.setVisible(False)

        # Update UI
        self.populate_table()
        self.update_statistics()
        self.update_file_info()

        # Enable buttons
        self.save_button.setEnabled(True)
        self.save_as_button.setEnabled(True)
        self.add_entry_button.setEnabled(True)

        self.status_label.setText(f"✅ Loaded {len(entries)} entries from {os.path.basename(self.current_file_path)}")

    def on_load_error(self, error_message):
        """Handle load error"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"❌ {error_message}")
        QMessageBox.critical(self, "Error", error_message)

    def populate_table(self):
        """Populate the entries table"""
        self.entries_table.setRowCount(len(self.filtered_entries))

        for row, entry in enumerate(self.filtered_entries):
            # Type
            type_item = QTableWidgetItem(entry.get_type())
            self.entries_table.setItem(row, 0, type_item)

            # Title
            title_item = QTableWidgetItem(entry.title)
            self.entries_table.setItem(row, 1, title_item)

            # Group
            group_item = QTableWidgetItem(entry.group_title)
            self.entries_table.setItem(row, 2, group_item)

            # TVG ID
            tvg_id_item = QTableWidgetItem(entry.tvg_id)
            self.entries_table.setItem(row, 3, tvg_id_item)

            # Logo (show if available)
            logo_item = QTableWidgetItem("🖼️" if entry.tvg_logo else "")
            self.entries_table.setItem(row, 4, logo_item)

            # URL (truncated)
            url_display = entry.url[:50] + "..." if len(entry.url) > 50 else entry.url
            url_item = QTableWidgetItem(url_display)
            self.entries_table.setItem(row, 5, url_item)

            # Metadata
            metadata = []
            if entry.tvg_serie:
                metadata.append(f"Serie: {entry.tvg_serie}")
            if entry.tvg_season:
                metadata.append(f"S{entry.tvg_season}")
            if entry.tvg_episode:
                metadata.append(f"E{entry.tvg_episode}")

            metadata_item = QTableWidgetItem(" | ".join(metadata))
            self.entries_table.setItem(row, 6, metadata_item)

        self.entry_count_label.setText(f"{len(self.filtered_entries)} entries")

    def apply_filters(self):
        """Apply type and search filters"""
        type_filter = self.type_filter.currentText()
        search_text = self.search_input.text().lower()

        self.filtered_entries = []

        for entry in self.entries:
            # Type filter
            if type_filter != "All":
                entry_type = entry.get_type()
                if type_filter == "Channels" and entry_type != "Channel":
                    continue
                elif type_filter == "VOD" and entry_type != "VOD":
                    continue
                elif type_filter == "Series" and entry_type != "Series":
                    continue
                elif type_filter == "Other" and entry_type not in ["Channel", "VOD", "Series"]:
                    continue

            # Search filter
            if search_text:
                searchable_text = f"{entry.title} {entry.group_title} {entry.url}".lower()
                if search_text not in searchable_text:
                    continue

            self.filtered_entries.append(entry)

        self.populate_table()

    def clear_search(self):
        """Clear search and filters"""
        self.search_input.clear()
        self.type_filter.setCurrentText("All")

    def on_selection_changed(self):
        """Handle table selection change"""
        selected_rows = set()
        for item in self.entries_table.selectedItems():
            selected_rows.add(item.row())

        has_selection = len(selected_rows) > 0
        self.edit_entry_button.setEnabled(has_selection)
        self.delete_entry_button.setEnabled(has_selection)
        self.duplicate_entry_button.setEnabled(has_selection)

        # Show preview for single selection
        if len(selected_rows) == 1:
            row = list(selected_rows)[0]
            if row < len(self.filtered_entries):
                entry = self.filtered_entries[row]
                preview_text = f"{entry.extinf_line}\n{entry.url}"
                self.preview_text.setPlainText(preview_text)
        else:
            self.preview_text.clear()

    def add_entry(self):
        """Add new entry"""
        new_entry = M3UEntry()
        new_entry.title = "New Entry"
        new_entry.url = "http://example.com/stream"
        new_entry.group_title = "General"
        new_entry.extinf_line = new_entry.build_extinf()

        dialog = EntryEditDialog(new_entry, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_entry = dialog.get_updated_entry()
            self.entries.append(updated_entry)
            self.apply_filters()
            self.update_statistics()
            self.status_label.setText("✅ Entry added")

    def edit_entry(self):
        """Edit selected entry"""
        current_row = self.entries_table.currentRow()
        if current_row >= 0 and current_row < len(self.filtered_entries):
            entry = self.filtered_entries[current_row]

            dialog = EntryEditDialog(entry, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_entry = dialog.get_updated_entry()
                # Update in main entries list
                for i, e in enumerate(self.entries):
                    if e is entry:
                        self.entries[i] = updated_entry
                        break

                self.apply_filters()
                self.update_statistics()
                self.status_label.setText("✅ Entry updated")

    def delete_entry(self):
        """Delete selected entries"""
        selected_rows = set()
        for item in self.entries_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            return

        reply = QMessageBox.question(
            self,
            "Delete Entries",
            f"Are you sure you want to delete {len(selected_rows)} entries?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Get entries to delete
            entries_to_delete = []
            for row in selected_rows:
                if row < len(self.filtered_entries):
                    entries_to_delete.append(self.filtered_entries[row])

            # Remove from main list
            for entry in entries_to_delete:
                if entry in self.entries:
                    self.entries.remove(entry)

            self.apply_filters()
            self.update_statistics()
            self.status_label.setText(f"✅ Deleted {len(entries_to_delete)} entries")

    def duplicate_entry(self):
        """Duplicate selected entry"""
        current_row = self.entries_table.currentRow()
        if current_row >= 0 and current_row < len(self.filtered_entries):
            original_entry = self.filtered_entries[current_row]

            # Create duplicate
            duplicate_entry = M3UEntry(original_entry.extinf_line, original_entry.url)
            duplicate_entry.title = f"{original_entry.title} (Copy)"
            duplicate_entry.extinf_line = duplicate_entry.build_extinf()

            self.entries.append(duplicate_entry)
            self.apply_filters()
            self.update_statistics()
            self.status_label.setText("✅ Entry duplicated")

    def new_m3u_file(self):
        """Create new M3U file"""
        if self.entries and self.has_unsaved_changes():
            reply = QMessageBox.question(
                self,
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save before creating a new file?",
                QMessageBox.StandardButton.Save | QMessageBox.StandardButton.Discard | QMessageBox.StandardButton.Cancel
            )

            if reply == QMessageBox.StandardButton.Save:
                if not self.save_m3u_file():
                    return
            elif reply == QMessageBox.StandardButton.Cancel:
                return

        # Clear everything
        self.entries = []
        self.filtered_entries = []
        self.current_file_path = ""
        self.populate_table()
        self.update_statistics()
        self.update_file_info()

        # Enable add button
        self.add_entry_button.setEnabled(True)
        self.save_button.setEnabled(False)
        self.save_as_button.setEnabled(True)

        self.status_label.setText("📄 New M3U file created")

    def save_m3u_file(self):
        """Save current M3U file"""
        if not self.current_file_path:
            return self.save_as_m3u_file()

        return self.save_to_file(self.current_file_path)

    def save_as_m3u_file(self):
        """Save M3U file with new name"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save M3U File",
            f"playlist_{datetime.now().strftime('%Y%m%d_%H%M%S')}.m3u",
            "M3U Files (*.m3u *.m3u8);;All Files (*)"
        )

        if file_path:
            if self.save_to_file(file_path):
                self.current_file_path = file_path
                self.update_file_info()
                self.save_button.setEnabled(True)
                return True

        return False

    def save_to_file(self, file_path):
        """Save entries to file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("#EXTM3U\n")

                for entry in self.entries:
                    f.write(f"{entry.extinf_line}\n")
                    f.write(f"{entry.url}\n")

            self.status_label.setText(f"✅ Saved {len(self.entries)} entries to {os.path.basename(file_path)}")
            return True

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save file:\n{str(e)}")
            self.status_label.setText(f"❌ Failed to save file")
            return False

    def update_statistics(self):
        """Update statistics display"""
        total = len(self.entries)
        channels = sum(1 for entry in self.entries if entry.get_type() == "Channel")
        vod = sum(1 for entry in self.entries if entry.get_type() == "VOD")
        series = sum(1 for entry in self.entries if entry.get_type() == "Series")

        self.stats_total_label.setText(f"Total: {total}")
        self.stats_channels_label.setText(f"Channels: {channels}")
        self.stats_vod_label.setText(f"VOD: {vod}")
        self.stats_series_label.setText(f"Series: {series}")

    def update_file_info(self):
        """Update file info display"""
        if self.current_file_path:
            filename = os.path.basename(self.current_file_path)
            self.file_info_label.setText(f"📁 {filename}")
        else:
            self.file_info_label.setText("No file loaded")

    def has_unsaved_changes(self):
        """Check if there are unsaved changes"""
        # For simplicity, assume changes if entries exist and no file path
        return len(self.entries) > 0 and not self.current_file_path
