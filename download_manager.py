"""
Professional Movie/Series Download Manager for IPTV Applications
Supports downloading from Stalker and Xtream-Codes servers with advanced features
"""

import os
import sys
import json
import time
import requests
import subprocess
import threading
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Callable, Any
from urllib.parse import urlparse, urljoin, parse_qs, urlencode
from pathlib import Path

from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QTimer
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
                            QLabel, QPushButton, QProgressBar, QListWidget, 
                            QListWidgetItem, QGroupBox, QComboBox, QSpinBox,
                            QLineEdit, QTextEdit, QCheckBox, QFileDialog,
                            QMessageBox, QTabWidget, Q<PERSON>plitter, QFrame)
from PyQt6.QtGui import QFont, QIcon
from PyQt6.QtCore import Qt

import logging
logger = logging.getLogger(__name__)

class IPTVAuthenticator:
    """IPTV Token Authenticator from M3U-Downloader"""

    def __init__(self):
        self.session = requests.Session()
        # Use VLC User-Agent like M3U-Downloader
        self.session.headers.update({
            'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        })

    def authenticate(self, url: str) -> str:
        """Authenticate and get fresh token for IPTV stream (from M3U-Downloader)"""
        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)

            # Extract required parameters
            mac = params.get('mac', [''])[0]
            stream_id = params.get('stream', [''])[0]
            content_type = params.get('type', [''])[0]

            if not all([mac, stream_id, content_type]):
                logger.debug("Missing required parameters for authentication")
                return url

            # Construct authentication URL (from M3U-Downloader)
            auth_url = f"{parsed.scheme}://{parsed.netloc}/player_api.php"

            # Clean stream_id - handle complex formats like "6586:6586:7.mp4"
            clean_stream_id = stream_id
            # Remove file extensions
            for ext in ['.mp4', '.mkv', '.avi', '.ts', '.mov']:
                clean_stream_id = clean_stream_id.replace(ext, '')

            # Handle colon-separated IDs (like "6586:6586:7" -> "6586")
            if ':' in clean_stream_id:
                # Try different parts of the colon-separated ID
                parts = clean_stream_id.split(':')
                clean_stream_id = parts[0]  # Use first part
                logger.info(f"Complex stream ID detected: {stream_id} -> using {clean_stream_id}")

            auth_params = {
                'username': mac,
                'password': mac,
                'action': 'get_link',
                'stream_id': clean_stream_id,
                'type': content_type
            }

            logger.info(f"Authenticating with {auth_url}")

            # Try multiple authentication methods
            auth_methods = [
                ('POST', auth_params),
                ('GET', auth_params),
            ]

            # Also try different actions
            for action in ['get_link', 'create_link', 'get_play_token']:
                auth_params_alt = auth_params.copy()
                auth_params_alt['action'] = action
                auth_methods.append(('POST', auth_params_alt))
                auth_methods.append(('GET', auth_params_alt))

            for method, params_to_use in auth_methods:
                try:
                    if method == 'POST':
                        response = self.session.post(f"{auth_url}?{urlencode(params_to_use)}", timeout=10)
                    else:
                        response = self.session.get(f"{auth_url}?{urlencode(params_to_use)}", timeout=10)

                    if response.status_code == 200:
                        try:
                            data = response.json()
                            if isinstance(data, dict):
                                # Look for various token fields
                                token_fields = ['token', 'play_token', 'access_token', 'auth_token']
                                for field in token_fields:
                                    if field in data and data[field]:
                                        # Update URL with new token
                                        params['play_token'] = [data[field]]
                                        new_url = parsed._replace(query=urlencode(params, doseq=True)).geturl()
                                        logger.info(f"✅ Got fresh {field}: {data[field][:10]}... using {method} {params_to_use['action']}")
                                        return new_url

                                logger.debug(f"No token in {method} {params_to_use['action']} response: {data}")
                        except Exception as e:
                            logger.debug(f"JSON parse error for {method} {params_to_use['action']}: {e}")
                    else:
                        logger.debug(f"{method} {params_to_use['action']} failed: HTTP {response.status_code}")

                except Exception as e:
                    logger.debug(f"{method} {params_to_use['action']} error: {e}")
                    continue

        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")

        return url

@dataclass
class DownloadItem:
    """Represents a downloadable item (movie/episode)"""
    id: str
    title: str
    url: str
    server_type: str  # 'stalker' or 'xtream'
    content_type: str  # 'movie', 'episode'
    file_extension: str
    file_size: int = 0
    duration: str = ""
    quality: str = ""
    season: str = ""
    episode: str = ""
    year: str = ""
    genre: str = ""
    description: str = ""
    poster_url: str = ""
    subtitle_url: str = ""
    
    def to_dict(self) -> Dict:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DownloadItem':
        return cls(**data)

@dataclass
class DownloadProgress:
    """Tracks download progress"""
    item_id: str
    status: str  # 'queued', 'downloading', 'paused', 'completed', 'failed', 'cancelled'
    progress: float = 0.0  # 0-100
    downloaded_bytes: int = 0
    total_bytes: int = 0
    speed: float = 0.0  # bytes per second
    eta: int = 0  # seconds
    error_message: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    output_path: str = ""

class DownloadWorker(QThread):
    """Worker thread for downloading content"""
    progress_updated = pyqtSignal(str, float, int, int, float, int)  # id, progress, downloaded, total, speed, eta
    status_changed = pyqtSignal(str, str)  # id, status
    download_completed = pyqtSignal(str, str)  # id, output_path
    download_failed = pyqtSignal(str, str)  # id, error_message
    
    def __init__(self, item: DownloadItem, output_dir: str, quality_settings: Dict):
        super().__init__()
        self.item = item
        self.output_dir = output_dir
        self.quality_settings = quality_settings
        self.is_paused = False
        self.is_cancelled = False
        self.session = requests.Session()

        # Initialize M3U-Downloader style authenticator
        self.authenticator = IPTVAuthenticator()

        # Set VLC user agent like M3U-Downloader for better compatibility
        self.session.headers.update({
            'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        })
    
    def run(self):
        """Main download execution"""
        try:
            self.status_changed.emit(self.item.id, "downloading")
            
            # Create output directory structure
            output_path = self.create_output_path()
            
            # Download the content
            if self.item.server_type == 'xtream':
                success = self.download_xtream_content(output_path)
            elif self.item.server_type == 'stalker':
                success = self.download_stalker_content(output_path)
            else:
                success = self.download_direct_stream(output_path)
            
            if success and not self.is_cancelled:
                # Post-process if needed (convert format, add metadata, etc.)
                final_path = self.post_process_download(output_path)
                self.download_completed.emit(self.item.id, final_path)
                self.status_changed.emit(self.item.id, "completed")
            elif self.is_cancelled:
                self.status_changed.emit(self.item.id, "cancelled")
            else:
                self.download_failed.emit(self.item.id, "Download failed")
                self.status_changed.emit(self.item.id, "failed")
                
        except Exception as e:
            logger.error(f"Download error for {self.item.id}: {e}")
            self.download_failed.emit(self.item.id, str(e))
            self.status_changed.emit(self.item.id, "failed")
    
    def create_output_path(self) -> str:
        """Create organized output path"""
        # Create directory structure: Downloads/Movies or Downloads/Series/SeriesName/Season X
        base_dir = Path(self.output_dir)
        
        if self.item.content_type == 'movie':
            content_dir = base_dir / "Movies"
            if self.item.year:
                filename = f"{self.item.title} ({self.item.year})"
            else:
                filename = self.item.title
        else:  # episode
            content_dir = base_dir / "Series" / self.item.title
            if self.item.season:
                content_dir = content_dir / f"Season {self.item.season}"
            filename = f"S{self.item.season.zfill(2)}E{self.item.episode.zfill(2)} - {self.item.title}"
        
        # Create directory
        content_dir.mkdir(parents=True, exist_ok=True)
        
        # Clean filename
        filename = self.clean_filename(filename)
        output_path = content_dir / f"{filename}.{self.item.file_extension}"
        
        return str(output_path)
    
    def clean_filename(self, filename: str) -> str:
        """Clean filename for filesystem compatibility"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()
    
    def download_xtream_content(self, output_path: str) -> bool:
        """Download content from Xtream-Codes server with enhanced retry logic"""
        max_retries = 5
        chunk_size = 32768  # Larger chunks for better performance

        for attempt in range(max_retries):
            try:
                logger.info(f"Xtream download attempt {attempt + 1}/{max_retries}: {self.item.url}")

                # Enhanced headers to avoid detection
                headers = {
                    'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16',
                    'Accept': '*/*',
                    'Connection': 'keep-alive',
                    'Accept-Encoding': 'identity',  # Disable compression to avoid issues
                    'Range': 'bytes=0-'  # Request range support
                }

                # Add delay between attempts to avoid rate limiting
                if attempt > 0:
                    delay = min(2 ** attempt, 30)  # Exponential backoff, max 30 seconds
                    logger.info(f"Waiting {delay} seconds before retry...")
                    time.sleep(delay)

                # Try resumable download if partial file exists
                resume_pos = 0
                if attempt > 0 and os.path.exists(output_path):
                    resume_pos = os.path.getsize(output_path)
                    if resume_pos > 0:
                        headers['Range'] = f'bytes={resume_pos}-'
                        logger.info(f"Resuming download from byte {resume_pos}")

                response = self.session.get(self.item.url, stream=True, timeout=60, headers=headers)
                response.raise_for_status()

                total_size = int(response.headers.get('content-length', 0))
                if resume_pos > 0:
                    total_size += resume_pos  # Add already downloaded bytes

                downloaded = resume_pos
                start_time = time.time()

                # Open file in append mode if resuming, otherwise write mode
                file_mode = 'ab' if resume_pos > 0 else 'wb'

                with open(output_path, file_mode) as f:
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if self.is_cancelled:
                            return False

                        while self.is_paused and not self.is_cancelled:
                            time.sleep(0.1)

                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)

                            # Calculate progress and speed
                            elapsed = time.time() - start_time
                            speed = (downloaded - resume_pos) / elapsed if elapsed > 0 else 0
                            progress = (downloaded / total_size * 100) if total_size > 0 else 0
                            eta = int((total_size - downloaded) / speed) if speed > 0 else 0

                            # Emit progress
                            self.progress_updated.emit(
                                self.item.id, progress, downloaded, total_size, speed, eta
                            )

                logger.info(f"Xtream download completed successfully on attempt {attempt + 1}")
                return True

            except (requests.exceptions.ConnectionError, requests.exceptions.ChunkedEncodingError,
                    requests.exceptions.IncompleteRead) as e:
                logger.warning(f"Xtream download connection error (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    logger.error(f"Xtream download failed after {max_retries} attempts: {e}")
                    return False
                continue

            except requests.exceptions.Timeout as e:
                logger.warning(f"Xtream download timeout (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    logger.error(f"Xtream download timed out after {max_retries} attempts: {e}")
                    return False
                continue

            except Exception as e:
                logger.error(f"Xtream download error (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    logger.error(f"Xtream download failed after {max_retries} attempts: {e}")
                    return False
                continue

        return False
    
    def download_stalker_content(self, output_path: str) -> bool:
        """Download content from Stalker portal using M3U-Downloader approach"""
        try:
            # Step 1: Authenticate and get fresh token (like M3U-Downloader)
            if 'play_token' in self.item.url:
                logger.info("🔐 Authenticating URL with fresh token...")
                self.item.url = self.authenticator.authenticate(self.item.url)
                logger.info(f"🔗 Updated URL: {self.item.url}")

            # Use VLC User-Agent like M3U-Downloader (key improvement!)
            headers = {
                'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16',  # This is the key difference!
                'Accept': '*/*',
                'Connection': 'keep-alive'
            }

            # Add referer for MAG portals
            if 'mag.' in self.item.url or '/c/' in self.item.url:
                parsed = urlparse(self.item.url)
                base_url = f"{parsed.scheme}://{parsed.netloc}"
                headers['Referer'] = f"{base_url}/c/index.html"

            logger.info(f"Starting Stalker download with M3U-Downloader approach: {self.item.url}")

            # Try multiple download strategies like M3U-Downloader
            download_strategies = [
                self._download_with_m3u_auth,  # New: M3U-Downloader authentication strategy
                self._download_with_range_support,
                self._download_direct_stream,
                self._download_with_retry
            ]

            for strategy in download_strategies:
                try:
                    if strategy(output_path, headers):
                        logger.info(f"✅ Stalker download completed using {strategy.__name__}: {output_path}")
                        return True
                except Exception as e:
                    logger.debug(f"Download strategy {strategy.__name__} failed: {e}")
                    continue

            logger.error("❌ All download strategies failed")
            return False

        except Exception as e:
            logger.error(f"Stalker download error: {e}")
            return False

    def _download_with_m3u_auth(self, output_path: str, headers: dict) -> bool:
        """Download with M3U-Downloader authentication handling"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                response = self.session.get(self.item.url, stream=True, timeout=30, headers=headers)

                # Handle HTTP 458 (Token expired) like M3U-Downloader
                if response.status_code == 458:
                    logger.info(f"🔄 Token expired (HTTP 458), refreshing... (attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        # Refresh token and retry
                        self.item.url = self.authenticator.authenticate(self.item.url)
                        logger.info(f"🔗 Refreshed URL: {self.item.url}")
                        time.sleep(2)  # Wait before retry
                        continue

                # Handle other authentication errors
                if response.status_code in [401, 403, 404]:
                    logger.info(f"🔄 Authentication error (HTTP {response.status_code}), trying alternative approaches... (attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        # Try different approaches
                        if attempt == 0:
                            # First retry: refresh token
                            self.item.url = self.authenticator.authenticate(self.item.url)
                            logger.info(f"🔗 Refreshed URL: {self.item.url}")
                        elif attempt == 1:
                            # Second retry: try alternative URL format
                            self.item.url = self._try_alternative_url_format(self.item.url)
                            logger.info(f"🔗 Alternative URL: {self.item.url}")

                        time.sleep(2)  # Wait before retry
                        continue

                response.raise_for_status()

                total_size = int(response.headers.get('content-length', 0))
                logger.info(f"✅ M3U auth strategy successful, downloading {total_size} bytes")
                return self._download_stream(response, output_path, total_size)

            except Exception as e:
                logger.debug(f"M3U auth attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff

        return False

    def _try_alternative_url_format(self, url: str) -> str:
        """Try alternative URL formats when current format fails"""
        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)

            mac = params.get('mac', [''])[0]
            stream_param = params.get('stream', [''])[0]
            play_token = params.get('play_token', [''])[0]
            content_type = params.get('type', [''])[0]

            # Extract clean stream ID from complex formats
            stream_id = stream_param
            for ext in ['.mp4', '.mkv', '.avi', '.ts', '.mov']:
                stream_id = stream_id.replace(ext, '')

            # Try different URL formats
            base_url = f"{parsed.scheme}://{parsed.netloc}"

            # Remove /c/ if present and try without it
            if '/c/' in url:
                alt_url = f"{base_url}/play/movie.php?mac={mac}&stream={stream_param}&play_token={play_token}&type={content_type}"
                logger.info(f"Trying without /c/ path: {alt_url}")
                return alt_url

            # Try with /c/ if not present
            else:
                alt_url = f"{base_url}/c/play/movie.php?mac={mac}&stream={stream_param}&play_token={play_token}&type={content_type}"
                logger.info(f"Trying with /c/ path: {alt_url}")
                return alt_url

        except Exception as e:
            logger.debug(f"Error creating alternative URL: {e}")
            return url

    def _download_with_range_support(self, output_path: str, headers: dict) -> bool:
        """Download with range request support (like M3U-Downloader)"""
        # First check if server supports range requests
        head_response = self.session.head(self.item.url, headers=headers, timeout=10)

        if head_response.status_code != 200:
            raise Exception(f"HEAD request failed: {head_response.status_code}")

        supports_range = head_response.headers.get('accept-ranges') == 'bytes'
        total_size = int(head_response.headers.get('content-length', 0))

        if supports_range and total_size > 0:
            # Use range request for better reliability
            headers['Range'] = 'bytes=0-'

        response = self.session.get(self.item.url, stream=True, timeout=30, headers=headers)
        response.raise_for_status()

        return self._download_stream(response, output_path, total_size)

    def _download_direct_stream(self, output_path: str, headers: dict) -> bool:
        """Direct stream download"""
        response = self.session.get(self.item.url, stream=True, timeout=30, headers=headers)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        return self._download_stream(response, output_path, total_size)

    def _download_with_retry(self, output_path: str, headers: dict) -> bool:
        """Download with retry logic"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                response = self.session.get(self.item.url, stream=True, timeout=30, headers=headers)
                response.raise_for_status()

                total_size = int(response.headers.get('content-length', 0))
                return self._download_stream(response, output_path, total_size)

            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.debug(f"Download attempt {attempt + 1} failed: {e}, retrying...")
                time.sleep(2 ** attempt)  # Exponential backoff

        return False

    def _download_stream(self, response, output_path: str, total_size: int) -> bool:
        """Common download stream logic"""
        downloaded = 0
        start_time = time.time()

        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if self.is_cancelled:
                    return False

                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)

                    # Update progress
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                    else:
                        progress = 0

                    # Calculate speed and ETA
                    elapsed = time.time() - start_time
                    if elapsed > 0:
                        speed = downloaded / elapsed  # bytes per second
                        if total_size > 0 and speed > 0:
                            eta = int((total_size - downloaded) / speed)
                        else:
                            eta = 0
                    else:
                        speed = 0
                        eta = 0

                    self.progress_updated.emit(
                        self.item.id, progress, downloaded, total_size, speed, eta
                    )

        return True
    
    def download_direct_stream(self, output_path: str) -> bool:
        """Download from direct stream URL"""
        return self.download_xtream_content(output_path)
    
    def post_process_download(self, file_path: str) -> str:
        """Post-process downloaded file (convert, add metadata, etc.)"""
        try:
            # Check if we need to convert format
            target_format = self.quality_settings.get('output_format', 'mp4')
            current_ext = Path(file_path).suffix[1:]  # Remove the dot
            
            if current_ext.lower() != target_format.lower():
                converted_path = self.convert_video_format(file_path, target_format)
                if converted_path:
                    # Remove original file after successful conversion
                    os.remove(file_path)
                    return converted_path
            
            # Add metadata if available
            self.add_video_metadata(file_path)
            
            return file_path
            
        except Exception as e:
            logger.error(f"Post-processing error: {e}")
            return file_path
    
    def convert_video_format(self, input_path: str, target_format: str) -> Optional[str]:
        """Convert video to target format using ffmpeg"""
        try:
            output_path = input_path.rsplit('.', 1)[0] + f'.{target_format}'
            
            # Basic ffmpeg command for conversion
            cmd = [
                'ffmpeg', '-i', input_path,
                '-c:v', 'libx264',  # Video codec
                '-c:a', 'aac',      # Audio codec
                '-preset', 'medium', # Encoding speed vs compression
                '-crf', '23',       # Quality (lower = better quality)
                output_path
            ]
            
            # Run conversion
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return output_path
            else:
                logger.error(f"FFmpeg conversion failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Video conversion error: {e}")
            return None
    
    def add_video_metadata(self, file_path: str):
        """Add metadata to video file"""
        try:
            # Use ffmpeg to add metadata
            metadata_args = []
            
            if self.item.title:
                metadata_args.extend(['-metadata', f'title={self.item.title}'])
            if self.item.year:
                metadata_args.extend(['-metadata', f'date={self.item.year}'])
            if self.item.genre:
                metadata_args.extend(['-metadata', f'genre={self.item.genre}'])
            if self.item.description:
                metadata_args.extend(['-metadata', f'comment={self.item.description}'])
            
            if metadata_args:
                temp_path = file_path + '.temp'
                cmd = ['ffmpeg', '-i', file_path] + metadata_args + ['-c', 'copy', temp_path]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    os.replace(temp_path, file_path)
                else:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                        
        except Exception as e:
            logger.error(f"Metadata addition error: {e}")
    
    def pause(self):
        """Pause download"""
        self.is_paused = True
    
    def resume(self):
        """Resume download"""
        self.is_paused = False
    
    def cancel(self):
        """Cancel download"""
        self.is_cancelled = True
        self.is_paused = False
