from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtGui import QDesktopServices
from styles import Colors, set_button_style

class AboutDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("About Developer")
        self.setFixedSize(400, 350)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Developer Info
        info_text = """
        <h2>MEGA IPTV TOOLS v1.1</h2>
        <p><b>Version:</b> 1.1</p>
        <p><b>Developed By:</b> Zied <PERSON></p>
        <p><b>Year:</b> 2025-2026</p>
        <p>This application is for educational purposes only.</p>
        <p>Please use responsibly and in accordance with applicable laws.</p>
        """

        info_label = QLabel(info_text)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet(f"color: {Colors.TEXT};")
        layout.addWidget(info_label)

        # GitHub Button
        github_btn = QPushButton("Visit GitHub Profile")
        set_button_style(github_btn, Colors.PRIMARY)
        github_btn.clicked.connect(lambda: QDesktopServices.openUrl(QUrl("https://github.com/zinzied")))
        layout.addWidget(github_btn)

        # Buy Me a Coffee Button
        coffee_btn = QPushButton("Buy Me a Coffee")
        set_button_style(coffee_btn, Colors.WARNING)
        coffee_btn.clicked.connect(lambda: QDesktopServices.openUrl(QUrl("https://buymeacoffee.com/zied")))
        layout.addWidget(coffee_btn)

        # Close Button
        close_btn = QPushButton("Close")
        set_button_style(close_btn, Colors.ERROR)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)